// Service Worker for PWA functionality
const CACHE_NAME = 'incepta-v1';
const STATIC_CACHE = 'incepta-static-v1';
const DYNAMIC_CACHE = 'incepta-dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/validate-idea',
  '/reports',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json'
];

// Install event - cache static files
self.addEventListener('install', event => {
  console.log('🔧 Service Worker installing...');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('📦 Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('✅ Service Worker activating...');
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
            console.log('🗑️ Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache with network fallback
self.addEventListener('fetch', event => {
  const { request } = event;
  
  // Skip non-GET requests
  if (request.method !== 'GET') return;
  
  // Handle API requests differently
  if (request.url.includes('/api/')) {
    event.respondWith(handleApiRequest(request));
  } else {
    event.respondWith(handleStaticRequest(request));
  }
});

// Handle API requests with network-first strategy
async function handleApiRequest(request) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('🌐 Network failed, trying cache for:', request.url);
    
    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for validation requests
    if (request.url.includes('validation')) {
      return new Response(JSON.stringify({
        error: 'Offline',
        message: 'Please check your internet connection'
      }), {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    throw error;
  }
}

// Handle static requests with cache-first strategy
async function handleStaticRequest(request) {
  // Try cache first
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    // Fallback to network
    const networkResponse = await fetch(request);
    
    // Cache the response
    const cache = await caches.open(DYNAMIC_CACHE);
    cache.put(request, networkResponse.clone());
    
    return networkResponse;
  } catch (error) {
    // Return offline fallback
    if (request.destination === 'document') {
      return caches.match('/offline.html');
    }
    throw error;
  }
}

// Background sync for failed validation requests
self.addEventListener('sync', event => {
  if (event.tag === 'validation-retry') {
    console.log('🔄 Background sync: retrying failed validations');
    event.waitUntil(retryFailedValidations());
  }
});

async function retryFailedValidations() {
  // Get failed validations from IndexedDB
  const failedValidations = await getFailedValidations();
  
  for (const validation of failedValidations) {
    try {
      const response = await fetch('/api/v1/validation/comprehensive-validation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(validation.data)
      });
      
      if (response.ok) {
        // Remove from failed queue
        await removeFailedValidation(validation.id);
        
        // Notify user
        self.registration.showNotification('Validation Complete', {
          body: 'Your idea validation has been completed!',
          icon: '/icon-192x192.png',
          badge: '/badge-72x72.png',
          tag: 'validation-complete'
        });
      }
    } catch (error) {
      console.log('❌ Retry failed for validation:', validation.id);
    }
  }
}

// Push notifications for validation updates
self.addEventListener('push', event => {
  const data = event.data ? event.data.json() : {};
  
  const options = {
    body: data.message || 'Your validation has an update',
    icon: '/icon-192x192.png',
    badge: '/badge-72x72.png',
    tag: data.tag || 'validation-update',
    data: data,
    actions: [
      {
        action: 'view',
        title: 'View Report',
        icon: '/icon-view.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss',
        icon: '/icon-dismiss.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification(data.title || 'Incepta Update', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  if (event.action === 'view') {
    // Open the app to view the report
    event.waitUntil(
      clients.openWindow('/reports/' + event.notification.data.reportId)
    );
  } else if (event.action === 'dismiss') {
    // Just close the notification
    return;
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Utility functions for IndexedDB operations
async function getFailedValidations() {
  // Implementation would use IndexedDB to store/retrieve failed validations
  return [];
}

async function removeFailedValidation(id) {
  // Implementation would remove from IndexedDB
  console.log('Removing failed validation:', id);
}
