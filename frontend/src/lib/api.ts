const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export interface User {
  id: number;
  email: string;
  full_name: string | null;
  role: string;
  is_active: boolean;
  username: string | null;
  is_verified: boolean;
  avatar_url: string | null;
  created_at: string;
  last_login: string | null;
}

export interface RegisterRequest {
  email: string;
  password: string;
  confirm_password: string;
  full_name?: string;
}

export interface ApiError {
  detail: string;
}

export interface ValidationReportCreate {
  title: string;
  description: string;
  user_answers: Record<string, string>;
  report_data: Record<string, unknown>;
  agent_activities?: unknown[];
  processing_metadata?: Record<string, unknown>;
}

export interface ValidationReport {
  id: string;
  title: string;
  description: string;
  user_answers: Record<string, string>;
  report_data: Record<string, unknown>;
  agent_activities: unknown[] | null;
  processing_metadata: Record<string, unknown> | null;
  created_at: string;
  updated_at: string | null;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      try {
        const errorData: ApiError = await response.json();
        console.error('API Error:', errorData);
        
        // Handle array of validation errors
        if (Array.isArray(errorData.detail)) {
          const errorMessages = errorData.detail.map((err: any) => 
            typeof err === 'object' ? `${err.loc?.join('.')} ${err.msg}` : String(err)
          ).join(', ');
          throw new Error(`Validation Error: ${errorMessages}`);
        }
        
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      } catch (parseError) {
        console.error('Failed to parse error response:', parseError);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    }

    return response.json();
  }

  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return this.request<LoginResponse>('/api/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  }

  async register(userData: RegisterRequest): Promise<User> {
    return this.request<User>('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async getCurrentUser(token: string): Promise<User> {
    return this.request<User>('/api/v1/auth/me', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  async generateQuestions(ideaDescription: string, token: string): Promise<unknown> {
    console.log('Making request without auth (temporary test)');
    console.log('Request body:', { idea_description: ideaDescription });

    return this.request<unknown>('/api/v1/validation/generate-questions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Temporarily removed: Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ idea_description: ideaDescription }),
    });
  }

  async comprehensiveValidation(ideaDescription: string, answers: Record<string, string>, token: string): Promise<unknown> {
    return this.request<unknown>('/api/v1/validation/comprehensive-validation', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ 
        idea_description: ideaDescription, 
        answers: answers 
      }),
    });
  }

  // Validation Report endpoints
  async createValidationReport(reportData: ValidationReportCreate, token: string): Promise<ValidationReport> {
    return this.request<ValidationReport>('/api/v1/validation/reports', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(reportData),
    });
  }

  async getValidationReport(reportId: string): Promise<ValidationReport> {
    return this.request<ValidationReport>(`/api/v1/validation/reports/${reportId}`);
  }

  async getUserValidationReports(token: string, skip: number = 0, limit: number = 10): Promise<ValidationReport[]> {
    return this.request<ValidationReport[]>(`/api/v1/validation/reports?skip=${skip}&limit=${limit}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  async deleteValidationReport(reportId: string, token: string): Promise<{ message: string }> {
    return this.request<{ message: string }>(`/api/v1/validation/reports/${reportId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }
}

export const apiClient = new ApiClient();