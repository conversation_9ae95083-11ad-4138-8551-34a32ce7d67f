"use client";

import { useState, useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle, XCircle, Eye, Zap, Search, Globe, X } from "lucide-react";

interface StreamingMessage {
  id: string;
  type: "ai_response" | "tool_call" | "error" | "status" | "final_report";
  provider?: string;
  tool?: string;
  content: string;
  timestamp: Date;
  status?: "pending" | "processing" | "completed" | "error";
}

interface ValidationStreamingInterfaceProps {
  isVisible: boolean;
  ideaDescription: string;
  answers: Record<string, string>;
  token: string;
  onComplete: (report: any) => void;
  onError: (error: string) => void;
  onClose?: () => void;
  onPreviewReport?: (report: any) => void;
}

export default function ValidationStreamingInterface({
  isVisible,
  ideaDescription,
  answers,
  token,
  onComplete,
  onError,
  onClose,
  onPreviewReport,
}: ValidationStreamingInterfaceProps) {
  const [messages, setMessages] = useState<StreamingMessage[]>([]);
  const [currentStatus, setCurrentStatus] = useState<string>("Starting validation...");
  const [isStreaming, setIsStreaming] = useState(false);
  const [validationReport, setValidationReport] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);
  const toolActivityRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    toolActivityRef.current?.scrollTo({ top: toolActivityRef.current.scrollHeight, behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (message: Omit<StreamingMessage, "id" | "timestamp">) => {
    const newMessage: StreamingMessage = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const startValidation = async () => {
    if (isStreaming) return;

    setIsStreaming(true);
    setMessages([]);
    setCurrentStatus("Initializing comprehensive validation process...");

    try {
      // Show initial message
      addMessage({
        type: "status",
        content: `Starting comprehensive AI validation for: "${ideaDescription.substring(0, 50)}..."`,
        status: "processing",
      });

      setCurrentStatus("Connecting to AI validation service...");

      // Actually fetch the real validation result with Celery enabled
      const url = new URL(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/v1/validation/comprehensive-validation-stream`);
      url.searchParams.append('token', token);
      url.searchParams.append('idea', ideaDescription);
      url.searchParams.append('answers', JSON.stringify(answers));
      url.searchParams.append('use_celery', 'true');
      
      console.log('🚀 Starting validation request to:', url.toString());
      console.log('🔑 Using token:', token.substring(0, 20) + '...');
      console.log('💡 Idea:', ideaDescription.substring(0, 100) + '...');
      console.log('📝 Answers:', Object.keys(answers));
      
      const response = await fetch(url.toString());
      console.log('📡 Response status:', response.status, response.statusText);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        throw new Error('No response reader available');
      }

      let result = '';
      let chunkCount = 0;
      let finalReport = null;
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('📡 Stream completed, total chunks:', chunkCount);
          break;
        }
        
        chunkCount++;
        const chunk = decoder.decode(value);
        result += chunk;
        
        console.log(`📦 Chunk ${chunkCount}:`, chunk.substring(0, 200) + '...');
        
        // Process each line in real-time
        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ') && line.trim() !== 'data: ') {
            try {
              const jsonStr = line.substring(6);
              const data = JSON.parse(jsonStr);
              console.log('📋 Parsed SSE data:', data);
              
              // Handle different message types from the stream
              if (data.type === 'status') {
                setCurrentStatus(data.message);
                addMessage({
                  type: "status",
                  content: data.message,
                  status: "processing",
                });
              } else if (data.type === 'analyst_start') {
                const agentName = data.agent_name || data.analyst;
                const title = data.title || '';
                const background = data.background || '';
                const icon = data.icon || '🤖';
                const provider = data.provider || 'AI';
                
                setCurrentStatus(`${agentName}: ${data.task}`);
                addMessage({
                  type: "ai_response",
                  provider: agentName,
                  content: `${icon} ${agentName} (${title})\n${background ? `${background} | ` : ''}${provider.toUpperCase()}\nStarting: ${data.task}...`,
                  status: "processing",
                });
              } else if (data.type === 'analyst_complete') {
                const agentName = data.analyst;
                const title = data.title || '';
                const icon = data.icon || '✅';
                addMessage({
                  type: "ai_response",
                  provider: agentName,
                  content: `${icon} ${agentName} (${title})\n✅ ${data.status}`,
                  status: "completed",
                });
              } else if (data.type === 'analyst_error') {
                const agentName = data.analyst;
                const icon = data.icon || '❌';
                addMessage({
                  type: "error",
                  content: `${icon} ${agentName}: ${data.error}`,
                  status: "error",
                });
              } else if (data.type === 'consolidation_start') {
                setCurrentStatus("Final consolidation in progress...");
                addMessage({
                  type: "status",
                  content: `🧠 ${data.message}`,
                  status: "processing",
                });
              } else if (data.type === 'consolidation_complete') {
                addMessage({
                  type: "status",
                  content: `✅ ${data.message}`,
                  status: "completed",
                });
              } else if (data.type === 'tool_call') {
                addMessage({
                  type: "tool_call",
                  tool: data.tool || "unknown",
                  content: data.message || data.content,
                  status: data.status || "processing",
                });
              } else if (data.type === 'ai_response') {
                addMessage({
                  type: "ai_response",
                  provider: data.provider || "ai",
                  content: data.message || data.content,
                  status: data.status || "processing",
                });
              } else if (data.type === 'agent_analysis') {
                addMessage({
                  type: "ai_response",
                  provider: data.agent || "agent",
                  content: `📊 ${data.agent}: ${data.message || data.content}`,
                  status: data.status || "completed",
                });
              } else if (data.type === 'error') {
                addMessage({
                  type: "error",
                  content: `❌ Error: ${data.message}`,
                  status: "error",
                });
                setCurrentStatus("Validation failed");
              } else if (data.type === 'final_report' || 
                        data.type === 'complete' || 
                        data.status === 'completed' ||
                        data.validation_summary ||
                        data.executive_summary) {
                console.log('🎯 Found validation report!');
                finalReport = data.report || data;
                
                addMessage({
                  type: "final_report",
                  content: "✅ Comprehensive validation completed - AI analysis from 4 specialist agents",
                  status: "completed",
                });
                break;
              }
            } catch (parseError) {
              // This line might not be JSON, continue
              console.log('📄 Non-JSON line:', line.substring(0, 100));
            }
          }
        }
        
        if (finalReport) {
          break;
        }
      }
      
      // Complete validation process
      setCurrentStatus("Validation completed! Report ready for preview.");
      setIsStreaming(false);
      
      if (finalReport) {
        console.log('✅ Validation report ready:', finalReport);
        setValidationReport(finalReport);
        onComplete(finalReport);
      } else {
        // If no structured final report found, create a simple one from the accumulated result
        console.log('⚠️ No structured report found, creating from accumulated data');
        const simpleReport = {
          validation_summary: {
            overall_score: 75,
            recommendation: "Proceed with caution",
            confidence_level: "Medium",
            key_strengths: ["AI-powered personalization", "Large market opportunity"],
            key_concerns: ["High competition", "Technical complexity"]
          },
          executive_summary: result.substring(0, 500) + "..."
        };
        setValidationReport(simpleReport);
        onComplete(simpleReport);
      }

    } catch (error) {
      console.error("Validation error:", error);
      addMessage({
        type: "error",
        content: `❌ Failed to complete validation: ${error}`,
        status: "error",
      });
      setIsStreaming(false);
      onError(error instanceof Error ? error.message : String(error));
    }
  };

  useEffect(() => {
    if (isVisible && !isStreaming) {
      startValidation();
    }

    // Cleanup on unmount
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [isVisible]);

  const getProviderIcon = (provider?: string) => {
    // Handle agent types by role rather than specific names
    if (provider?.toLowerCase().includes("market research") || provider?.toLowerCase().includes("market analyst")) return "📊";
    if (provider?.toLowerCase().includes("financial") || provider?.toLowerCase().includes("finance")) return "💰";
    if (provider?.toLowerCase().includes("technical") || provider?.toLowerCase().includes("architect")) return "⚡";
    if (provider?.toLowerCase().includes("risk") || provider?.toLowerCase().includes("assessment")) return "🛡️";
    if (provider?.toLowerCase().includes("intelligence") || provider?.toLowerCase().includes("research")) return "🔍";
    if (provider?.toLowerCase().includes("legal") || provider?.toLowerCase().includes("compliance")) return "⚖️";
    if (provider?.toLowerCase().includes("regional") || provider?.toLowerCase().includes("specialist")) return "🌍";
    
    // Handle AI providers
    switch (provider?.toLowerCase()) {
      case "openai": return "🧠";
      case "anthropic": return "🔮";
      case "deepseek": return "🎯";
      case "perplexity": return "🔍";
      default: return "🤖";
    }
  };

  const getToolIcon = (tool?: string) => {
    switch (tool?.toLowerCase()) {
      case "tavily": return <Search className="w-4 h-4" />;
      case "firecrawl": return <Globe className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case "completed": return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "error": return <XCircle className="w-4 h-4 text-red-500" />;
      case "processing": return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      default: return <Eye className="w-4 h-4 text-gray-500" />;
    }
  };

  if (!isVisible) return null;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Loader2 className={`w-5 h-5 ${isStreaming ? 'animate-spin text-blue-500' : 'text-green-500'}`} />
            <h3 className="text-sm font-semibold text-gray-800">AI Validation Progress</h3>
          </div>
          <div className="flex items-center gap-2">
            {validationReport && !isStreaming && (
              <Button 
                size="sm" 
                onClick={() => onPreviewReport?.(validationReport)}
                className="bg-purple-600 hover:bg-purple-700 text-white text-xs px-3 py-1"
              >
                <Eye className="w-3 h-3 mr-1" />
                Preview Report
              </Button>
            )}
            {onClose && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onClose}
                className="h-8 w-8 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
        <div className="text-xs text-gray-600 mt-1">
          {currentStatus}
        </div>
      </div>
      
      {/* Tool Activity Panel */}
      <div className="flex-1 overflow-hidden bg-gray-50">
        {isStreaming || messages.some(m => m.type === "tool_call" || m.type === "ai_response") ? (
          <div 
            ref={toolActivityRef}
            className="h-full overflow-y-scroll p-4 space-y-3 custom-scrollbar"
            style={{
              maxHeight: 'calc(100vh - 200px)',
              minHeight: '300px'
            }}
          >
            {messages
              .filter(m => m.type === "tool_call" || m.type === "ai_response")
              .map((message) => (
                <div key={message.id} className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm">
                  <div className="flex items-center gap-2 mb-2">
                    {message.type === "ai_response" && (
                      <span className="text-sm">{getProviderIcon(message.provider)}</span>
                    )}
                    {message.type === "tool_call" && (
                      <div className="text-sm">{getToolIcon(message.tool)}</div>
                    )}
                    <span className="text-xs font-medium text-gray-600">
                      {message.provider?.toUpperCase() || message.tool?.toUpperCase()}
                    </span>
                    <div className="ml-auto">
                      {getStatusIcon(message.status)}
                    </div>
                  </div>
                  <div className="text-xs text-gray-700">
                    {message.content}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              ))}
              
            {isStreaming && (
              <div className="bg-white rounded-lg p-3 border border-blue-200 shadow-sm">
                <div className="flex items-center gap-2 mb-2">
                  <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
                  <span className="text-xs font-medium text-blue-600">PROCESSING</span>
                </div>
                <div className="text-xs text-blue-700">
                  Analyzing with AI providers and external tools...
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 p-4">
            <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center mb-2">
              <Zap className="w-6 h-6 text-gray-400" />
            </div>
            <div className="text-sm font-medium">No tool activity</div>
            <div className="text-xs text-center">
              Tool calls and AI interactions will appear here when they're being executed.
            </div>
          </div>
        )}
      </div>
    </div>
  );
}