/**
 * Refactored ValidationReportModal component using modular architecture
 */

"use client";

import { useState } from "react";
import { ValidationReportModalProps } from "./types";
import { extractScore, extractRecommendation } from "./utils";
import ModalHeader from "./components/ModalHeader";
import ModalTabs from "./components/ModalTabs";
import ModalContent from "./components/ModalContent";

export default function ValidationReportModal({
  isOpen,
  report,
  ideaTitle,
  onClose,
}: ValidationReportModalProps) {
  const [activeTab, setActiveTab] = useState<string>("overview");

  if (!isOpen) return null;

  const score = extractScore(report);
  const recommendation = extractRecommendation(report);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <ModalHeader
          ideaTitle={ideaTitle}
          score={score}
          recommendation={recommendation}
          onClose={onClose}
        />

        {/* Tabs */}
        <ModalTabs
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />

        {/* Content */}
        <ModalContent
          activeTab={activeTab}
          report={report}
        />
      </div>
    </div>
  );
}
