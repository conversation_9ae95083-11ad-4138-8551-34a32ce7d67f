/**
 * Types and interfaces for validation modal components
 */

export interface ValidationReport {
  validation_summary?: {
    overall_score?: number;
    recommendation?: string;
    confidence_level?: string;
    key_strengths?: string[];
    key_concerns?: string[];
  };
  executive_summary?: string;
  market_opportunity?: {
    size?: string;
    attractiveness?: string;
    competitive_position?: string;
  };
  business_model_viability?: {
    revenue_potential?: string;
    financial_feasibility?: string;
    monetization_clarity?: string;
  };
  technical_feasibility?: {
    implementation_complexity?: string;
    resource_requirements?: string;
    technical_risks?: string;
  };
  risk_profile?: {
    overall_risk_level?: string;
    critical_risks?: string[];
    risk_mitigation?: string;
  };
  recommendations?: {
    immediate_next_steps?: string[];
    success_factors?: string[];
    timeline_to_market?: string;
    funding_strategy?: string;
  };
  analyst_consensus?: {
    areas_of_agreement?: string[];
    areas_of_concern?: string[];
    conflicting_views?: string;
  };
  // Fallback for simple reports
  score?: number;
  analysis?: string;
}

export interface ValidationReportModalProps {
  isOpen: boolean;
  report: ValidationReport;
  ideaTitle: string;
  onClose: () => void;
}

export interface ModalHeaderProps {
  ideaTitle: string;
  score: number;
  recommendation: string;
  onClose: () => void;
}

export interface ModalTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export interface ModalContentProps {
  activeTab: string;
  report: ValidationReport;
}

export interface TabConfig {
  id: string;
  label: string;
  icon: any;
}

export interface SectionCardProps {
  title: string;
  icon: any;
  children: React.ReactNode;
  className?: string;
}
