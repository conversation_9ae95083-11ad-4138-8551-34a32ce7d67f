/**
 * Modal content component that renders different tab sections
 */

import { 
  FileText, 
  TrendingUp, 
  DollarSign, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Users,
  Target,
  Clock
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ModalContentProps } from "../types";
import { 
  hasContent, 
  safeRender, 
  renderArrayItems, 
  createFallbackContent,
  getRiskLevelColor,
  getScoreColor,
  formatScore
} from "../utils";
import SectionCard from "./SectionCard";

export default function ModalContent({ activeTab, report }: ModalContentProps) {
  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return (
          <div className="space-y-6">
            {/* Executive Summary */}
            <SectionCard title="Executive Summary" icon={FileText}>
              {hasContent(report.executive_summary) ? (
                <p className="text-gray-700 leading-relaxed">
                  {safeRender(report.executive_summary)}
                </p>
              ) : (
                <p className="text-gray-500 italic">
                  Executive summary not available in this report.
                </p>
              )}
            </SectionCard>

            {/* Key Highlights */}
            {report.validation_summary && (
              <div className="grid md:grid-cols-2 gap-6">
                {/* Key Strengths */}
                {hasContent(report.validation_summary.key_strengths) && (
                  <SectionCard title="Key Strengths" icon={CheckCircle} className="border-green-200">
                    <ul className="space-y-2">
                      {renderArrayItems(report.validation_summary.key_strengths, "text-green-700")}
                    </ul>
                  </SectionCard>
                )}

                {/* Key Concerns */}
                {hasContent(report.validation_summary.key_concerns) && (
                  <SectionCard title="Key Concerns" icon={AlertTriangle} className="border-orange-200">
                    <ul className="space-y-2">
                      {renderArrayItems(report.validation_summary.key_concerns, "text-orange-700")}
                    </ul>
                  </SectionCard>
                )}
              </div>
            )}

            {/* Confidence Level */}
            {report.validation_summary?.confidence_level && (
              <SectionCard title="Analysis Confidence" icon={Target}>
                <div className="flex items-center gap-2">
                  <span className="text-gray-700">Confidence Level:</span>
                  <Badge className="bg-blue-50 text-blue-700">
                    {safeRender(report.validation_summary.confidence_level)}
                  </Badge>
                </div>
              </SectionCard>
            )}
          </div>
        );

      case "market":
        return (
          <div className="space-y-6">
            {hasContent(report.market_opportunity) ? (
              <SectionCard title="Market Opportunity" icon={TrendingUp}>
                <div className="space-y-4">
                  {report.market_opportunity.size && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Market Size</h4>
                      <p className="text-gray-700">{safeRender(report.market_opportunity.size)}</p>
                    </div>
                  )}
                  {report.market_opportunity.attractiveness && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Market Attractiveness</h4>
                      <p className="text-gray-700">{safeRender(report.market_opportunity.attractiveness)}</p>
                    </div>
                  )}
                  {report.market_opportunity.competitive_position && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Competitive Position</h4>
                      <p className="text-gray-700">{safeRender(report.market_opportunity.competitive_position)}</p>
                    </div>
                  )}
                </div>
              </SectionCard>
            ) : (
              createFallbackContent("Market opportunity")
            )}
          </div>
        );

      case "business":
        return (
          <div className="space-y-6">
            {hasContent(report.business_model_viability) ? (
              <SectionCard title="Business Model Viability" icon={DollarSign}>
                <div className="space-y-4">
                  {report.business_model_viability.revenue_potential && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Revenue Potential</h4>
                      <p className="text-gray-700">{safeRender(report.business_model_viability.revenue_potential)}</p>
                    </div>
                  )}
                  {report.business_model_viability.financial_feasibility && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Financial Feasibility</h4>
                      <p className="text-gray-700">{safeRender(report.business_model_viability.financial_feasibility)}</p>
                    </div>
                  )}
                  {report.business_model_viability.monetization_clarity && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Monetization Clarity</h4>
                      <p className="text-gray-700">{safeRender(report.business_model_viability.monetization_clarity)}</p>
                    </div>
                  )}
                </div>
              </SectionCard>
            ) : (
              createFallbackContent("Business model")
            )}
          </div>
        );

      case "technical":
        return (
          <div className="space-y-6">
            {hasContent(report.technical_feasibility) ? (
              <SectionCard title="Technical Feasibility" icon={Shield}>
                <div className="space-y-4">
                  {report.technical_feasibility.implementation_complexity && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Implementation Complexity</h4>
                      <p className="text-gray-700">{safeRender(report.technical_feasibility.implementation_complexity)}</p>
                    </div>
                  )}
                  {report.technical_feasibility.resource_requirements && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Resource Requirements</h4>
                      <p className="text-gray-700">{safeRender(report.technical_feasibility.resource_requirements)}</p>
                    </div>
                  )}
                  {report.technical_feasibility.technical_risks && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Technical Risks</h4>
                      <p className="text-gray-700">{safeRender(report.technical_feasibility.technical_risks)}</p>
                    </div>
                  )}
                </div>
              </SectionCard>
            ) : (
              createFallbackContent("Technical feasibility")
            )}
          </div>
        );

      case "risks":
        return (
          <div className="space-y-6">
            {hasContent(report.risk_profile) ? (
              <SectionCard title="Risk Profile" icon={AlertTriangle}>
                <div className="space-y-4">
                  {report.risk_profile.overall_risk_level && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Overall Risk Level</h4>
                      <Badge className={getRiskLevelColor(report.risk_profile.overall_risk_level)}>
                        {safeRender(report.risk_profile.overall_risk_level)}
                      </Badge>
                    </div>
                  )}
                  {hasContent(report.risk_profile.critical_risks) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Critical Risks</h4>
                      <ul className="space-y-2">
                        {renderArrayItems(report.risk_profile.critical_risks, "text-red-700")}
                      </ul>
                    </div>
                  )}
                  {report.risk_profile.risk_mitigation && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Risk Mitigation</h4>
                      <p className="text-gray-700">{safeRender(report.risk_profile.risk_mitigation)}</p>
                    </div>
                  )}
                </div>
              </SectionCard>
            ) : (
              createFallbackContent("Risk profile")
            )}
          </div>
        );

      case "recommendations":
        return (
          <div className="space-y-6">
            {hasContent(report.recommendations) ? (
              <SectionCard title="Recommendations & Next Steps" icon={CheckCircle}>
                <div className="space-y-4">
                  {hasContent(report.recommendations.immediate_next_steps) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Immediate Next Steps</h4>
                      <ul className="space-y-2">
                        {renderArrayItems(report.recommendations.immediate_next_steps, "text-blue-700")}
                      </ul>
                    </div>
                  )}
                  {hasContent(report.recommendations.success_factors) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Success Factors</h4>
                      <ul className="space-y-2">
                        {renderArrayItems(report.recommendations.success_factors, "text-green-700")}
                      </ul>
                    </div>
                  )}
                  {report.recommendations.timeline_to_market && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Timeline to Market</h4>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-blue-600" />
                        <span className="text-gray-700">{safeRender(report.recommendations.timeline_to_market)}</span>
                      </div>
                    </div>
                  )}
                  {report.recommendations.funding_strategy && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Funding Strategy</h4>
                      <p className="text-gray-700">{safeRender(report.recommendations.funding_strategy)}</p>
                    </div>
                  )}
                </div>
              </SectionCard>
            ) : (
              createFallbackContent("Recommendations")
            )}
          </div>
        );

      case "consensus":
        return (
          <div className="space-y-6">
            {hasContent(report.analyst_consensus) ? (
              <SectionCard title="Analyst Consensus" icon={Users}>
                <div className="space-y-4">
                  {hasContent(report.analyst_consensus.areas_of_agreement) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Areas of Agreement</h4>
                      <ul className="space-y-2">
                        {renderArrayItems(report.analyst_consensus.areas_of_agreement, "text-green-700")}
                      </ul>
                    </div>
                  )}
                  {hasContent(report.analyst_consensus.areas_of_concern) && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Areas of Concern</h4>
                      <ul className="space-y-2">
                        {renderArrayItems(report.analyst_consensus.areas_of_concern, "text-orange-700")}
                      </ul>
                    </div>
                  )}
                  {report.analyst_consensus.conflicting_views && (
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Conflicting Views</h4>
                      <p className="text-gray-700">{safeRender(report.analyst_consensus.conflicting_views)}</p>
                    </div>
                  )}
                </div>
              </SectionCard>
            ) : (
              createFallbackContent("Analyst consensus")
            )}
          </div>
        );

      default:
        return createFallbackContent("This section");
    }
  };

  return (
    <div className="flex-1 overflow-y-auto p-6">
      {renderTabContent()}
    </div>
  );
}
