/**
 * Modal tabs navigation component
 */

import { 
  FileText, 
  TrendingUp, 
  DollarSign, 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Users 
} from "lucide-react";
import { ModalTabsProps, TabConfig } from "../types";

const tabs: TabConfig[] = [
  { id: "overview", label: "Overview", icon: FileText },
  { id: "market", label: "Market", icon: TrendingUp },
  { id: "business", label: "Business", icon: DollarSign },
  { id: "technical", label: "Technical", icon: Shield },
  { id: "risks", label: "Risks", icon: AlertTriangle },
  { id: "recommendations", label: "Next Steps", icon: CheckCircle },
  { id: "consensus", label: "Consensus", icon: Users },
];

export default function ModalTabs({ activeTab, onTabChange }: ModalTabsProps) {
  return (
    <div className="border-b border-gray-200 bg-gray-50">
      <div className="flex overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`flex items-center gap-2 px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 transition-colors ${
                activeTab === tab.id
                  ? "border-blue-500 text-blue-600 bg-white"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              <Icon className="w-4 h-4" />
              {tab.label}
            </button>
          );
        })}
      </div>
    </div>
  );
}
