/**
 * Modal header component with score display and close button
 */

import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ModalHeaderProps } from "../types";
import { getScoreColor, getRecommendationColor, formatScore } from "../utils";

export default function ModalHeader({ 
  ideaTitle, 
  score, 
  recommendation, 
  onClose 
}: ModalHeaderProps) {
  return (
    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h2 className="text-2xl font-bold mb-2">{ideaTitle}</h2>
          <p className="text-blue-100 mb-4">Comprehensive Validation Report</p>
          
          {/* Score and Recommendation */}
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-blue-100">Overall Score:</span>
              <Badge className={`${getScoreColor(score)} border-0 font-bold`}>
                {formatScore(score)}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-blue-100">Recommendation:</span>
              <Badge className={`${getRecommendationColor(recommendation)} border-0`}>
                {recommendation}
              </Badge>
            </div>
          </div>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-white hover:bg-white/20 ml-4"
        >
          <X className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
}
