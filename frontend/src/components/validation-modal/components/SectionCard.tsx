/**
 * Reusable section card component for modal content
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SectionCardProps } from "../types";

export default function SectionCard({ 
  title, 
  icon: Icon, 
  children, 
  className = "" 
}: SectionCardProps) {
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Icon className="w-5 h-5 text-blue-600" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
}
