/**
 * Utility functions for validation modal components
 */

import { ValidationReport } from "./types";

/**
 * Get score color classes based on score value
 */
export const getScoreColor = (score: number): string => {
  if (score >= 8) return "text-green-600 bg-green-50";
  if (score >= 6) return "text-yellow-600 bg-yellow-50";
  return "text-red-600 bg-red-50";
};

/**
 * Get recommendation color classes based on recommendation text
 */
export const getRecommendationColor = (recommendation: string): string => {
  if (recommendation?.includes("Strong Proceed") || recommendation?.includes("Proceed")) {
    return "text-green-600 bg-green-50";
  }
  if (recommendation?.includes("Caution")) {
    return "text-yellow-600 bg-yellow-50";
  }
  return "text-red-600 bg-red-50";
};

/**
 * Extract score from validation report
 */
export const extractScore = (report: ValidationReport): number => {
  return report?.validation_summary?.overall_score || report?.score || 7.5;
};

/**
 * Extract recommendation from validation report
 */
export const extractRecommendation = (report: ValidationReport): string => {
  return report?.validation_summary?.recommendation || "Assessment Complete";
};

/**
 * Check if content exists and is not empty
 */
export const hasContent = (content: any): boolean => {
  if (!content) return false;
  if (Array.isArray(content)) return content.length > 0;
  if (typeof content === 'object') return Object.keys(content).length > 0;
  if (typeof content === 'string') return content.trim().length > 0;
  return true;
};

/**
 * Safely render content as string
 */
export const safeRender = (content: any): string => {
  if (typeof content === 'string') return content;
  if (typeof content === 'number') return content.toString();
  if (typeof content === 'boolean') return content.toString();
  if (content === null || content === undefined) return '';
  if (Array.isArray(content)) return content.join(', ');
  if (typeof content === 'object') return JSON.stringify(content);
  return String(content);
};

/**
 * Render array items as list elements
 */
export const renderArrayItems = (items: any[], className: string = ""): JSX.Element[] => {
  if (!Array.isArray(items)) return [];
  
  return items.map((item, index) => (
    <li key={index} className={`flex items-start gap-2 ${className}`}>
      <div className="w-2 h-2 rounded-full bg-gray-400 mt-2 flex-shrink-0"></div>
      <span className="text-gray-700">{safeRender(item)}</span>
    </li>
  ));
};

/**
 * Create fallback content when data is missing
 */
export const createFallbackContent = (sectionName: string): JSX.Element => (
  <div className="text-center py-8">
    <div className="text-gray-400 text-4xl mb-4">📊</div>
    <p className="text-gray-500">
      {sectionName} information is not available in this report.
    </p>
  </div>
);

/**
 * Get risk level color
 */
export const getRiskLevelColor = (riskLevel: string): string => {
  const level = riskLevel?.toLowerCase() || '';
  if (level.includes('low')) return "text-green-600 bg-green-50";
  if (level.includes('medium') || level.includes('moderate')) return "text-yellow-600 bg-yellow-50";
  if (level.includes('high')) return "text-red-600 bg-red-50";
  return "text-gray-600 bg-gray-50";
};

/**
 * Format percentage or score display
 */
export const formatScore = (score: number): string => {
  return `${score.toFixed(1)}/10`;
};
