"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, T<PERSON>dingUp, DollarSign, Shield, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Users, Target, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";

interface ValidationReport {
  validation_summary?: {
    overall_score?: number;
    recommendation?: string;
    confidence_level?: string;
    key_strengths?: string[];
    key_concerns?: string[];
  };
  executive_summary?: string;
  market_opportunity?: {
    size?: string;
    attractiveness?: string;
    competitive_position?: string;
  };
  business_model_viability?: {
    revenue_potential?: string;
    financial_feasibility?: string;
    monetization_clarity?: string;
  };
  technical_feasibility?: {
    implementation_complexity?: string;
    resource_requirements?: string;
    technical_risks?: string;
  };
  risk_profile?: {
    overall_risk_level?: string;
    critical_risks?: string[];
    risk_mitigation?: string;
  };
  recommendations?: {
    immediate_next_steps?: string[];
    success_factors?: string[];
    timeline_to_market?: string;
    funding_strategy?: string;
  };
  analyst_consensus?: {
    areas_of_agreement?: string[];
    areas_of_concern?: string[];
    conflicting_views?: string;
  };
  // Fallback for simple reports
  score?: number;
  summary?: string;
  recommendations?: string[];
  market_potential?: string;
  risks?: string[];
}

interface ValidationReportModalProps {
  isOpen: boolean;
  report: ValidationReport;
  ideaTitle: string;
  onClose: () => void;
}

export default function ValidationReportModal({
  isOpen,
  report,
  ideaTitle,
  onClose,
}: ValidationReportModalProps) {
  const [activeTab, setActiveTab] = useState<string>("overview");

  if (!isOpen) return null;

  const getScoreColor = (score: number) => {
    if (score >= 8) return "text-green-600 bg-green-50";
    if (score >= 6) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  const getRecommendationColor = (recommendation: string) => {
    if (recommendation?.includes("Strong Proceed") || recommendation?.includes("Proceed")) return "text-green-600 bg-green-50";
    if (recommendation?.includes("Caution")) return "text-yellow-600 bg-yellow-50";
    return "text-red-600 bg-red-50";
  };

  const score = report.validation_summary?.overall_score || report.score || 0;
  const recommendation = report.validation_summary?.recommendation || "Assessment Complete";

  const tabs = [
    { id: "overview", label: "Overview", icon: FileText },
    { id: "market", label: "Market", icon: TrendingUp },
    { id: "business", label: "Business", icon: DollarSign },
    { id: "technical", label: "Technical", icon: Shield },
    { id: "risks", label: "Risks", icon: AlertTriangle },
    { id: "recommendations", label: "Next Steps", icon: CheckCircle },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-purple-500 to-indigo-500 text-white">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <h2 className="text-2xl font-bold mb-2">Validation Report</h2>
              <p className="text-purple-100 mb-4">{ideaTitle}</p>
              
              <div className="flex items-center gap-4">
                <div className={`px-4 py-2 rounded-lg font-bold text-lg ${getScoreColor(score)} text-gray-800 bg-white`}>
                  Score: {score}/10
                </div>
                <div className={`px-4 py-2 rounded-lg font-medium ${getRecommendationColor(recommendation)} text-gray-800 bg-white`}>
                  {recommendation}
                </div>
                {report.validation_summary?.confidence_level && (
                  <div className="px-3 py-1 rounded-lg bg-white bg-opacity-20 text-sm">
                    Confidence: {report.validation_summary.confidence_level}
                  </div>
                )}
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white hover:bg-opacity-20"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <div className="flex h-[calc(90vh-140px)]">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 bg-gray-50">
            <nav className="p-4 space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? "bg-purple-100 text-purple-700 font-medium"
                        : "text-gray-600 hover:bg-gray-100"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {activeTab === "overview" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">Executive Summary</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-700 leading-relaxed">
                      {report.executive_summary || report.summary || "No executive summary available."}
                    </p>
                  </div>
                </div>

                {(report.validation_summary?.key_strengths || report.validation_summary?.key_concerns) && (
                  <div className="grid md:grid-cols-2 gap-6">
                    {report.validation_summary.key_strengths && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-green-700 flex items-center gap-2">
                            <CheckCircle className="w-5 h-5" />
                            Key Strengths
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {report.validation_summary.key_strengths.map((strength, index) => (
                              <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                                <span className="text-green-500 mt-1">•</span>
                                {strength}
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    )}

                    {report.validation_summary.key_concerns && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-orange-700 flex items-center gap-2">
                            <AlertTriangle className="w-5 h-5" />
                            Key Concerns
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {report.validation_summary.key_concerns.map((concern, index) => (
                              <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                                <span className="text-orange-500 mt-1">•</span>
                                {concern}
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                )}
              </div>
            )}

            {activeTab === "market" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Market Analysis</h3>
                
                {report.market_opportunity && (
                  <div className="grid gap-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Target className="w-5 h-5" />
                          Market Opportunity
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {report.market_opportunity.size && (
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Market Size</h4>
                            <p className="text-gray-700">{report.market_opportunity.size}</p>
                          </div>
                        )}
                        {report.market_opportunity.attractiveness && (
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Attractiveness</h4>
                            <p className="text-gray-700">{report.market_opportunity.attractiveness}</p>
                          </div>
                        )}
                        {report.market_opportunity.competitive_position && (
                          <div>
                            <h4 className="font-medium text-gray-900 mb-2">Competitive Position</h4>
                            <p className="text-gray-700">{report.market_opportunity.competitive_position}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                )}

                {report.market_potential && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Market Potential</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700">{report.market_potential}</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeTab === "business" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Business Model</h3>
                
                {report.business_model_viability && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-5 h-5" />
                        Business Viability
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {report.business_model_viability.revenue_potential && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Revenue Potential</h4>
                          <p className="text-gray-700">{report.business_model_viability.revenue_potential}</p>
                        </div>
                      )}
                      {report.business_model_viability.financial_feasibility && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Financial Feasibility</h4>
                          <p className="text-gray-700">{report.business_model_viability.financial_feasibility}</p>
                        </div>
                      )}
                      {report.business_model_viability.monetization_clarity && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Monetization Strategy</h4>
                          <p className="text-gray-700">{report.business_model_viability.monetization_clarity}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeTab === "technical" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Technical Assessment</h3>
                
                {report.technical_feasibility && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Shield className="w-5 h-5" />
                        Technical Feasibility
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {report.technical_feasibility.implementation_complexity && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Implementation Complexity</h4>
                          <p className="text-gray-700">{report.technical_feasibility.implementation_complexity}</p>
                        </div>
                      )}
                      {report.technical_feasibility.resource_requirements && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Resource Requirements</h4>
                          <p className="text-gray-700">{report.technical_feasibility.resource_requirements}</p>
                        </div>
                      )}
                      {report.technical_feasibility.technical_risks && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Technical Risks</h4>
                          <p className="text-gray-700">{report.technical_feasibility.technical_risks}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeTab === "risks" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Risk Assessment</h3>
                
                {report.risk_profile && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="w-5 h-5" />
                        Risk Profile
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {report.risk_profile.overall_risk_level && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Overall Risk Level</h4>
                          <Badge variant={report.risk_profile.overall_risk_level === "Low" ? "default" : 
                                        report.risk_profile.overall_risk_level === "Medium" ? "secondary" : "destructive"}>
                            {report.risk_profile.overall_risk_level}
                          </Badge>
                        </div>
                      )}
                      {report.risk_profile.critical_risks && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Critical Risks</h4>
                          <ul className="space-y-2">
                            {report.risk_profile.critical_risks.map((risk, index) => (
                              <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                                <span className="text-red-500 mt-1">•</span>
                                {risk}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {report.risk_profile.risk_mitigation && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2">Risk Mitigation</h4>
                          <p className="text-gray-700">{report.risk_profile.risk_mitigation}</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {report.risks && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Identified Risks</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {report.risks.map((risk, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                            <span className="text-red-500 mt-1">•</span>
                            {risk}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeTab === "recommendations" && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-gray-900">Recommendations & Next Steps</h3>
                
                {report.recommendations && (
                  <div className="grid gap-6">
                    {report.recommendations.immediate_next_steps && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <CheckCircle className="w-5 h-5" />
                            Immediate Next Steps
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {report.recommendations.immediate_next_steps.map((step, index) => (
                              <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                                <span className="text-blue-500 mt-1">•</span>
                                {step}
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    )}

                    {report.recommendations.success_factors && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <Target className="w-5 h-5" />
                            Success Factors
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <ul className="space-y-2">
                            {report.recommendations.success_factors.map((factor, index) => (
                              <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                                <span className="text-green-500 mt-1">•</span>
                                {factor}
                              </li>
                            ))}
                          </ul>
                        </CardContent>
                      </Card>
                    )}

                    <div className="grid md:grid-cols-2 gap-6">
                      {report.recommendations.timeline_to_market && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Clock className="w-5 h-5" />
                              Timeline to Market
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-700">{report.recommendations.timeline_to_market}</p>
                          </CardContent>
                        </Card>
                      )}

                      {report.recommendations.funding_strategy && (
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <DollarSign className="w-5 h-5" />
                              Funding Strategy
                            </CardTitle>
                          </CardHeader>
                          <CardContent>
                            <p className="text-gray-700">{report.recommendations.funding_strategy}</p>
                          </CardContent>
                        </Card>
                      )}
                    </div>
                  </div>
                )}

                {/* Fallback for simple recommendations */}
                {!report.recommendations?.immediate_next_steps && report.recommendations && Array.isArray(report.recommendations) && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Recommendations</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {report.recommendations.map((rec, index) => (
                          <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                            <span className="text-blue-500 mt-1">•</span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50 flex justify-end">
          <Button onClick={onClose} className="bg-purple-600 hover:bg-purple-700 text-white">
            Close Report
          </Button>
        </div>
      </div>
    </div>
  );
}