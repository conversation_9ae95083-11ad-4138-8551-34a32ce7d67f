/**
 * Custom hook for loading and managing report data
 */

import { useState, useEffect } from "react";
import { apiClient } from "@/lib/api";
import { ValidationReport } from "../types";
import { loadReportFromLocalStorage } from "../utils";

interface UseReportDataResult {
  report: ValidationReport | null;
  loading: boolean;
  error: string | null;
}

export const useReportData = (reportId: string): UseReportDataResult => {
  const [report, setReport] = useState<ValidationReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadReport = async () => {
      if (!reportId) {
        setError("No report ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Try to fetch from database first
        try {
          const dbReport = await apiClient.getValidationReport(reportId);
          setReport(dbReport);
          console.log('✅ Loaded report from database:', reportId);
          return;
        } catch (dbError) {
          console.log('⚠️ Database fetch failed, trying localStorage:', dbError);
        }

        // Fallback to localStorage
        const localReport = loadReportFromLocalStorage(reportId);
        if (localReport) {
          setReport(localReport);
          console.log('✅ Loaded report from localStorage:', reportId);
          return;
        }

        // No report found
        setError("Report not found");
        console.log('❌ Report not found:', reportId);

      } catch (err) {
        console.error('❌ Error loading report:', err);
        setError(err instanceof Error ? err.message : "Failed to load report");
      } finally {
        setLoading(false);
      }
    };

    loadReport();
  }, [reportId]);

  return { report, loading, error };
};
