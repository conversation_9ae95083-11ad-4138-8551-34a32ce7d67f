/**
 * Types and interfaces for reports components
 */

export interface ValidationReport {
  id: string;
  title: string;
  description: string;
  user_answers: Record<string, string>;
  report_data: any;
  created_at: string;
}

export interface ReportPageProps {
  reportId: string;
}

export interface ReportHeaderProps {
  report: ValidationReport;
  score: number;
  recommendation: string;
  onBack: () => void;
}

export interface ReportTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export interface ReportContentProps {
  activeTab: string;
  reportData: any;
}

export interface ReportSectionProps {
  title: string;
  data: any;
  children?: React.ReactNode;
}

export interface TabConfig {
  id: string;
  label: string;
}

export const TAB_CONFIG: TabConfig[] = [
  { id: "executive", label: "Executive Summary" },
  { id: "concept", label: "Project Concept" },
  { id: "users", label: "Target Users" },
  { id: "features", label: "Features" },
  { id: "market", label: "Market Analysis" },
  { id: "competitors", label: "Competitors" },
  { id: "business", label: "Business Model" },
  { id: "challenges", label: "Challenges & Opportunities" },
  { id: "financial", label: "Financial" },
  { id: "implementation", label: "Implementation" },
  { id: "sources", label: "Sources" },
  { id: "next", label: "Next Steps" },
];
