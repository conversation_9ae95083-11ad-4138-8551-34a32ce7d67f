/**
 * Refactored report page component using modular architecture
 */

"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { ReportPageProps } from "./types";
import { useReportData } from "./hooks/useReportData";
import { extractScore, extractRecommendation } from "./utils";
import ReportHeader from "./components/ReportHeader";
import ReportTabs from "./components/ReportTabs";
import ReportContent from "./components/ReportContent";

export default function ReportPage({ reportId }: ReportPageProps) {
  const router = useRouter();
  const { report, loading, error } = useReportData(reportId);
  const [activeTab, setActiveTab] = useState("executive");

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading validation report...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !report) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="text-6xl mb-4">📊</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Report Not Found</h1>
          <p className="text-gray-600 mb-6">
            {error || "The validation report you're looking for doesn't exist or has been removed."}
          </p>
          <button
            onClick={() => router.push('/history')}
            className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
          >
            View All Reports
          </button>
        </div>
      </div>
    );
  }

  // Extract report data
  const reportData = report.report_data;
  const score = extractScore(reportData);
  const recommendation = extractRecommendation(reportData);

  const handleBack = () => {
    router.push('/history');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with score display */}
      <ReportHeader
        report={report}
        score={score}
        recommendation={recommendation}
        onBack={handleBack}
      />

      {/* Navigation tabs */}
      <ReportTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Main content */}
      <ReportContent
        activeTab={activeTab}
        reportData={reportData}
      />
    </div>
  );
}
