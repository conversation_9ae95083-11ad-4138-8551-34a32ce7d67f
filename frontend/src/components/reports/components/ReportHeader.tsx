/**
 * Report header component with score display and navigation
 */

import { ArrowLeft, CheckCircle, Clock, TrendingUp } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ReportHeaderProps } from "../types";
import { getScoreColor, formatTimestamp } from "../utils";

export default function ReportHeader({ 
  report, 
  score, 
  recommendation, 
  onBack 
}: ReportHeaderProps) {
  return (
    <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-white hover:bg-white/20 flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Dashboard
          </Button>
        </div>

        {/* Report Title and Info */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">{report.title}</h1>
          <p className="text-purple-100 text-lg mb-4">{report.description}</p>
          <div className="text-purple-200 text-sm">
            Generated on {formatTimestamp(report.created_at)}
          </div>
        </div>

        {/* Score Dashboard */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Overall Score */}
          <div className="text-center">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${getScoreColor(score)} text-white font-bold text-xl mb-2`}>
              {score}
            </div>
            <div className="text-sm text-purple-200">Overall Score</div>
            <div className="font-semibold">{score}/10</div>
          </div>

          {/* Recommendation */}
          <div className="text-center">
            <CheckCircle className="w-16 h-16 mx-auto mb-2 text-green-300" />
            <div className="text-sm text-purple-200">Recommendation</div>
            <div className="font-semibold text-sm">{recommendation}</div>
          </div>

          {/* Timeline */}
          <div className="text-center">
            <Clock className="w-16 h-16 mx-auto mb-2 text-blue-300" />
            <div className="text-sm text-purple-200">Timeline</div>
            <div className="font-semibold">6-12 months</div>
          </div>

          {/* Market Size */}
          <div className="text-center">
            <TrendingUp className="w-16 h-16 mx-auto mb-2 text-emerald-300" />
            <div className="text-sm text-purple-200">Market Size</div>
            <div className="font-semibold">$1.2B+</div>
          </div>
        </div>
      </div>
    </div>
  );
}
