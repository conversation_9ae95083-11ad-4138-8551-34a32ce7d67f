/**
 * Generic report section component
 */

import { createEmptyState } from "../jsx-utils";
import { ReportSectionProps } from "../types";
import { hasContent } from "../utils";

export default function ReportSection({ 
  title, 
  data, 
  children 
}: ReportSectionProps) {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">{title}</h2>
      
      {hasContent(data) ? (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          {children}
        </div>
      ) : (
        createEmptyState(title)
      )}
    </div>
  );
}
