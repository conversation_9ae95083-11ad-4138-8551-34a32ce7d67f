/**
 * Main report content component that renders different sections
 */

import { renderArrayItems, renderObjectProperties } from "../jsx-utils";
import ExecutiveSummarySection from "../sections/ExecutiveSummarySection";
import { ReportContentProps } from "../types";
import { safeRender } from "../utils";
import ReportSection from "./ReportSection";

export default function ReportContent({ activeTab, reportData }: ReportContentProps) {
  const renderSection = () => {
    switch (activeTab) {
      case "executive":
        return <ExecutiveSummarySection data={reportData} />;

      case "concept":
        return (
          <ReportSection title="Project Concept" data={reportData?.project_concept}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.project_concept)}
            </div>
          </ReportSection>
        );

      case "users":
        return (
          <ReportSection title="Target Users & Market" data={reportData?.target_users}>
            <div className="space-y-6">
              {/* Primary Personas */}
              {reportData?.target_users?.primary_personas && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Primary User Personas</h3>
                  <ul className="space-y-2">
                    {renderArrayItems(reportData.target_users.primary_personas)}
                  </ul>
                </div>
              )}

              {/* User Needs */}
              {reportData?.target_users?.user_needs && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">User Needs</h3>
                  <ul className="space-y-2">
                    {renderArrayItems(reportData.target_users.user_needs)}
                  </ul>
                </div>
              )}

              {/* Market Segments */}
              {reportData?.target_users?.market_segments && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Market Segments</h3>
                  <ul className="space-y-2">
                    {renderArrayItems(reportData.target_users.market_segments)}
                  </ul>
                </div>
              )}
            </div>
          </ReportSection>
        );

      case "features":
        return (
          <ReportSection title="Features" data={reportData?.features}>
            <div className="space-y-6">
              {/* Core Features */}
              {reportData?.features?.core_features && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Core Features</h3>
                  <div className="grid gap-3">
                    {reportData.features.core_features.map((feature: any, index: number) => (
                      <div key={index} className="p-3 bg-blue-50 rounded-lg">
                        <p className="text-gray-700">{safeRender(feature)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Must Have Features */}
              {reportData?.features?.must_have_features && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Must-Have Features</h3>
                  <div className="grid gap-3">
                    {reportData.features.must_have_features.map((feature: any, index: number) => (
                      <div key={index} className="p-3 bg-green-50 rounded-lg">
                        <p className="text-gray-700">{safeRender(feature)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Nice to Have Features */}
              {reportData?.features?.nice_to_have_features && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Nice-to-Have Features</h3>
                  <div className="grid gap-3">
                    {reportData.features.nice_to_have_features.map((feature: any, index: number) => (
                      <div key={index} className="p-3 bg-yellow-50 rounded-lg">
                        <p className="text-gray-700">{safeRender(feature)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Future Features */}
              {reportData?.features?.future_features && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Future Features</h3>
                  <div className="grid gap-3">
                    {reportData.features.future_features.map((feature: any, index: number) => (
                      <div key={index} className="p-3 bg-gray-50 rounded-lg">
                        <p className="text-gray-700">{safeRender(feature)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </ReportSection>
        );

      case "market":
        return (
          <ReportSection title="Market Analysis" data={reportData?.market_analysis}>
            <div className="space-y-6">
              {/* Market Size */}
              {reportData?.market_analysis?.market_size && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Market Size</h3>
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="p-4 bg-green-50 rounded-lg text-center">
                      <div className="text-sm font-medium text-green-800">TAM</div>
                      <div className="text-lg font-bold text-green-900">
                        {safeRender(reportData.market_analysis.market_size.tam)}
                      </div>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg text-center">
                      <div className="text-sm font-medium text-blue-800">SAM</div>
                      <div className="text-lg font-bold text-blue-900">
                        {safeRender(reportData.market_analysis.market_size.sam)}
                      </div>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg text-center">
                      <div className="text-sm font-medium text-purple-800">SOM</div>
                      <div className="text-lg font-bold text-purple-900">
                        {safeRender(reportData.market_analysis.market_size.som)}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Market Trends */}
              {reportData?.market_analysis?.market_trends && (
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Market Trends</h3>
                  <ul className="space-y-2">
                    {renderArrayItems(reportData.market_analysis.market_trends)}
                  </ul>
                </div>
              )}

              {/* Other market analysis data */}
              <div className="space-y-4">
                {renderObjectProperties(reportData?.market_analysis, ['market_size', 'market_trends'])}
              </div>
            </div>
          </ReportSection>
        );

      case "competitors":
        return (
          <ReportSection title="Competitive Landscape" data={reportData?.competitive_landscape}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.competitive_landscape)}
            </div>
          </ReportSection>
        );

      case "business":
        return (
          <ReportSection title="Business Model" data={reportData?.business_model}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.business_model)}
            </div>
          </ReportSection>
        );

      case "challenges":
        return (
          <ReportSection title="Challenges & Opportunities" data={reportData?.challenges_and_opportunities}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.challenges_and_opportunities)}
            </div>
          </ReportSection>
        );

      case "financial":
        return (
          <ReportSection title="Financial Projections" data={reportData?.financial_projections}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.financial_projections)}
            </div>
          </ReportSection>
        );

      case "implementation":
        return (
          <ReportSection title="Implementation Roadmap" data={reportData?.implementation_roadmap}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.implementation_roadmap)}
            </div>
          </ReportSection>
        );

      case "sources":
        return (
          <ReportSection title="Sources & Citations" data={reportData?.citations_and_sources}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.citations_and_sources)}
            </div>
          </ReportSection>
        );

      case "next":
        return (
          <ReportSection title="Next Steps" data={reportData?.next_steps}>
            <div className="space-y-4">
              {renderObjectProperties(reportData?.next_steps)}
            </div>
          </ReportSection>
        );

      default:
        return (
          <ReportSection title="Section Not Found" data={null}>
            <div className="text-center py-8">
              <p className="text-gray-500">This section is not available.</p>
            </div>
          </ReportSection>
        );
    }
  };

  return <div>{renderSection()}</div>;
}
