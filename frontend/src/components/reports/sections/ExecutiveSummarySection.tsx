/**
 * Executive summary section component
 */

import ReportSection from "../components/ReportSection";
import { safeRender, renderArrayItems } from "../utils";

interface ExecutiveSummarySectionProps {
  data: any;
}

export default function ExecutiveSummarySection({ data }: ExecutiveSummarySectionProps) {
  return (
    <ReportSection title="Executive Summary" data={data}>
      <div className="space-y-6">
        {/* Main Summary */}
        {data?.executive_summary && (
          <div>
            <p className="text-gray-700 leading-relaxed text-lg">
              {safeRender(data.executive_summary)}
            </p>
          </div>
        )}

        {/* Key Highlights Grid */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Key Strengths */}
          {data?.validation_summary?.key_strengths && (
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="font-semibold text-green-800 mb-3">Key Strengths</h3>
              <ul className="space-y-2">
                {renderArrayItems(data.validation_summary.key_strengths, "text-green-700")}
              </ul>
            </div>
          )}

          {/* Key Concerns */}
          {data?.validation_summary?.key_concerns && (
            <div className="bg-orange-50 p-4 rounded-lg">
              <h3 className="font-semibold text-orange-800 mb-3">Key Concerns</h3>
              <ul className="space-y-2">
                {renderArrayItems(data.validation_summary.key_concerns, "text-orange-700")}
              </ul>
            </div>
          )}
        </div>

        {/* Confidence Level */}
        {data?.validation_summary?.confidence_level && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800 mb-2">Analysis Confidence</h3>
            <p className="text-blue-700">
              <strong>Confidence Level:</strong> {safeRender(data.validation_summary.confidence_level)}
            </p>
          </div>
        )}

        {/* Fallback content if no structured data */}
        {!data?.executive_summary && !data?.validation_summary && (
          <div className="text-center py-8">
            <p className="text-gray-500">
              Executive summary data is being processed or not available.
            </p>
          </div>
        )}
      </div>
    </ReportSection>
  );
}
