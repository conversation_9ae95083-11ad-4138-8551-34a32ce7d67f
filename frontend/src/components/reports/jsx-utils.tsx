/**
 * JSX utility functions for reports components
 */

import { safeRender } from "./utils";

/**
 * Render array items as JSX list elements
 */
export const renderArrayItems = (items: any[], className: string = ""): JSX.Element[] => {
  if (!Array.isArray(items)) return [];
  
  return items.map((item, index) => (
    <li key={index} className={`flex items-start gap-2 ${className}`}>
      <div className="w-2 h-2 rounded-full bg-gray-400 mt-2 flex-shrink-0"></div>
      <span className="text-gray-700">{safeRender(item)}</span>
    </li>
  ));
};

/**
 * Render object properties as key-value pairs
 */
export const renderObjectProperties = (obj: any, excludeKeys: string[] = []): JSX.Element[] => {
  if (!obj || typeof obj !== 'object') return [];
  
  return Object.entries(obj)
    .filter(([key]) => !excludeKeys.includes(key))
    .map(([key, value]) => (
      <div key={key} className="mb-3">
        <h4 className="font-semibold text-gray-900 mb-1">
          {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </h4>
        <p className="text-gray-700">{safeRender(value)}</p>
      </div>
    ));
};

/**
 * Create fallback content when data is missing
 */
export const createFallbackContent = (sectionName: string): JSX.Element => (
  <div className="text-center py-8">
    <div className="text-gray-400 text-4xl mb-4">📊</div>
    <p className="text-gray-500">
      {sectionName} information is not available in this report.
    </p>
  </div>
);

/**
 * Create empty state message (alias for createFallbackContent)
 */
export const createEmptyState = createFallbackContent;
