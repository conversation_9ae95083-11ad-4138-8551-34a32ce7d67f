/**
 * Utility functions for reports components
 */

import { ValidationReport } from "./types";

/**
 * Safely render content of any type as a string
 */
export const safeRender = (content: any): string => {
  if (typeof content === 'string') return content;
  if (typeof content === 'number') return content.toString();
  if (typeof content === 'boolean') return content.toString();
  if (content === null || content === undefined) return '';
  if (Array.isArray(content)) return content.map(item => safeRender(item)).join(', ');
  if (typeof content === 'object') return JSON.stringify(content);
  return String(content);
};

/**
 * Get score color class based on score value
 */
export const getScoreColor = (score: number): string => {
  if (score >= 8) return "bg-green-500";
  if (score >= 6) return "bg-yellow-500";
  if (score >= 4) return "bg-orange-500";
  return "bg-red-500";
};

/**
 * Extract score from report data
 */
export const extractScore = (reportData: any): number => {
  return reportData?.validation_summary?.overall_score || 
         reportData?.overall_score || 
         reportData?.score || 
         7.5;
};

/**
 * Extract recommendation from report data
 */
export const extractRecommendation = (reportData: any): string => {
  return reportData?.validation_summary?.recommendation || "Assessment Complete";
};

/**
 * Check if data exists and has content
 */
export const hasContent = (data: any): boolean => {
  if (!data) return false;
  if (Array.isArray(data)) return data.length > 0;
  if (typeof data === 'object') return Object.keys(data).length > 0;
  if (typeof data === 'string') return data.trim().length > 0;
  return true;
};

/**
 * Render array items as list
 */
export const renderArrayItems = (items: any[], className: string = ""): JSX.Element[] => {
  if (!Array.isArray(items)) return [];
  
  return items.map((item, index) => (
    <li key={index} className={`flex items-start gap-2 ${className}`}>
      <div className="w-2 h-2 rounded-full bg-gray-400 mt-2 flex-shrink-0"></div>
      <span className="text-gray-700">{safeRender(item)}</span>
    </li>
  ));
};

/**
 * Transform localStorage report to database format
 */
export const transformLocalStorageReport = (localReport: any, reportId: string): ValidationReport => {
  return {
    id: reportId,
    title: localReport.title || "Validation Report",
    description: localReport.description || "Idea validation analysis",
    user_answers: localReport.userAnswers || {},
    report_data: localReport.report || localReport,
    created_at: localReport.timestamp || new Date().toISOString()
  };
};

/**
 * Load report from localStorage
 */
export const loadReportFromLocalStorage = (reportId: string): ValidationReport | null => {
  try {
    const savedReports = JSON.parse(localStorage.getItem('validation_reports') || '[]');
    const foundReport = savedReports.find((r: any) => r.id === reportId);
    
    if (foundReport) {
      return transformLocalStorageReport(foundReport, reportId);
    }
    
    return null;
  } catch (error) {
    console.error('Error loading report from localStorage:', error);
    return null;
  }
};

/**
 * Format timestamp for display
 */
export const formatTimestamp = (timestamp: string): string => {
  try {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return timestamp;
  }
};

/**
 * Render object properties as key-value pairs
 */
export const renderObjectProperties = (obj: any, excludeKeys: string[] = []): JSX.Element[] => {
  if (!obj || typeof obj !== 'object') return [];
  
  return Object.entries(obj)
    .filter(([key]) => !excludeKeys.includes(key))
    .map(([key, value]) => (
      <div key={key} className="mb-3">
        <h4 className="font-semibold text-gray-900 mb-1">
          {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </h4>
        <p className="text-gray-700">{safeRender(value)}</p>
      </div>
    ));
};

/**
 * Create empty state message
 */
export const createEmptyState = (sectionName: string): JSX.Element => (
  <div className="text-center py-8">
    <div className="text-gray-400 text-4xl mb-4">📊</div>
    <p className="text-gray-500">
      {sectionName} information is not available in this report.
    </p>
  </div>
);
