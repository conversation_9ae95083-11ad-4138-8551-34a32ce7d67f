import { Code2, File<PERSON>earch, <PERSON>bul<PERSON>, <PERSON> } from "lucide-react";
import WorkStep from "./howitworks/WorkStep";
import WhyPrototype from "./howitworks/WhyPrototype";
import { WorkStep as WorkStepType } from "@/types/howitworks";

const steps: WorkStepType[] = [
  {
    icon: Lightbulb,
    title: "Share Your Vision",
    description: "Use our AI-powered questionnaire to define your software requirements - no technical knowledge needed.",
    features: [
      "Smart project analysis",
      "AI-generated suggestions",
      "Clear requirements capture"
    ],
    badge: "AI-Powered"
  },
  {
    icon: FileSearch,
    title: "Get Detailed Roadmap",
    description: "Receive a comprehensive development roadmap with timeline, cost, and resource estimates.",
    features: [
      "Phase-wise breakdown",
      "Resource allocation",
      "Cost transparency"
    ],
    badge: "Swift Process"
  },
  {
    icon: Code2,
    title: "Interactive Prototype",
    description: "Get a fully functional prototype with real working features - not just static mockups.",
    features: [
      "Working features",
      "Real data flow",
      "User interaction"
    ],
    badge: "Interactive"
  },
  {
    icon: Rocket,
    title: "Validate & Scale",
    description: "Test with real users, gather feedback, and confidently move forward with full development.",
    features: [
      "User testing ready",
      "Feedback system",
      "Easy iterations"
    ],
    badge: "Market Ready"
  }
];

export default function HowItWorks() {
  return (
    <section className="py-24 bg-white dark:bg-gray-900 scroll-mt-16" id="how-it-works">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Swift Journey: Concept to Interactive Prototype
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our streamlined process focuses on delivering a working prototype that helps validate your idea quickly and effectively.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <WorkStep key={index} step={step} index={index} />
          ))}
        </div>

        <WhyPrototype />
      </div>
    </section>
  );
}