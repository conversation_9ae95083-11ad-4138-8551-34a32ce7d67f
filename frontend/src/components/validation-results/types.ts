/**
 * Shared types for validation results components
 */

export interface ValidationReport {
  validation_summary?: {
    overall_score?: number;
    recommendation?: string;
    confidence_level?: string;
    key_strengths?: string[];
    key_concerns?: string[];
  };
  executive_summary?: string;
  project_concept?: {
    problem_statement?: string;
    solution_overview?: string;
    target_market?: string;
    value_proposition?: string;
  };
  target_users?: {
    primary_personas?: any[];
    user_needs?: string[];
    market_segments?: any[];
  };
  features?: {
    core_features?: any[];
    must_have_features?: any[];
    nice_to_have_features?: any[];
    future_features?: any[];
  };
  market_analysis?: {
    market_size?: {
      tam?: string;
      sam?: string;
      som?: string;
    };
    market_trends?: string[];
    competitive_landscape?: any;
  };
  competitive_landscape?: {
    direct_competitors?: any[];
    indirect_competitors?: any[];
    competitive_advantages?: string[];
  };
  business_model?: {
    value_proposition?: string;
    revenue_streams?: any[];
    cost_structure?: any;
  };
  challenges_and_opportunities?: {
    market_challenges?: string[];
    growth_opportunities?: string[];
    risk_factors?: string[];
  };
  financial_projections?: {
    investment_requirements?: string;
    revenue_projections?: any;
    break_even_analysis?: any;
  };
  implementation_roadmap?: {
    phases?: any[];
    milestones?: any[];
    timeline?: string;
  };
  citations_and_sources?: {
    research_sources?: any[];
    data_sources?: any[];
    methodology?: string;
  };
  next_steps?: {
    immediate_actions?: string[];
    short_term_goals?: string[];
    long_term_objectives?: string[];
  };
  recommendations?: {
    timeline_to_market?: string;
    funding_requirements?: string;
    key_success_factors?: string[];
  };
}

export interface ValidationResultsProps {
  isOpen: boolean;
  report: ValidationReport;
  ideaTitle: string;
  ideaDescription: string;
  userAnswers?: Record<string, any>;
  onClose: () => void;
  isStandalonePage?: boolean;
}

export interface TabConfig {
  id: string;
  label: string;
  icon: any;
}

export interface ScoreDisplayProps {
  score: number;
  recommendation: string;
  report: ValidationReport;
}

export interface SectionProps {
  report: ValidationReport;
  isCompact?: boolean;
}
