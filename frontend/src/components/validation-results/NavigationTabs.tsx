/**
 * Navigation tabs component for validation results
 */

import { 
  BookOpen, 
  Lightbulb, 
  Users, 
  Package, 
  BarChart3, 
  Target, 
  DollarSign, 
  AlertTriangle, 
  TrendingUp, 
  GitBranch, 
  ArrowRight 
} from "lucide-react";

interface NavigationTabsProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  isCompact?: boolean;
}

const iconMap = {
  BookOpen,
  Lightbulb,
  Users,
  Package,
  BarChart3,
  Target,
  DollarSign,
  AlertTriangle,
  TrendingUp,
  GitBranch,
  ArrowRight
};

const tabs = [
  { id: "overview", label: "Executive Summary", icon: "BookOpen" },
  { id: "concept", label: "Project Concept", icon: "Lightbulb" },
  { id: "users", label: "Target Users", icon: "Users" },
  { id: "features", label: "Features", icon: "Package" },
  { id: "market", label: "Market Analysis", icon: "BarChart3" },
  { id: "competitive", label: "Competition", icon: "Target" },
  { id: "business", label: "Business Model", icon: "DollarSign" },
  { id: "challenges", label: "Challenges", icon: "AlertTriangle" },
  { id: "financial", label: "Financial", icon: "TrendingUp" },
  { id: "roadmap", label: "Roadmap", icon: "GitBranch" },
  { id: "citations", label: "Sources", icon: "BookOpen" },
  { id: "next", label: "Next Steps", icon: "ArrowRight" }
];

export default function NavigationTabs({ 
  activeSection, 
  onSectionChange, 
  isCompact = false 
}: NavigationTabsProps) {
  return (
    <div className={`flex space-x-1 bg-gray-100 p-1 rounded-lg ${isCompact ? 'm-4 mb-4' : 'mb-8'}`}>
      {tabs.map((tab) => {
        const IconComponent = iconMap[tab.icon as keyof typeof iconMap];
        
        return (
          <button
            key={tab.id}
            onClick={() => onSectionChange(tab.id)}
            className={`flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              activeSection === tab.id
                ? "bg-white text-[#4F46E5] shadow-sm"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            } ${isCompact ? 'text-xs px-2 py-1' : ''}`}
          >
            <IconComponent className={`${isCompact ? 'w-3 h-3' : 'w-4 h-4'}`} />
            <span className={isCompact ? 'hidden sm:inline' : ''}>{tab.label}</span>
          </button>
        );
      })}
    </div>
  );
}
