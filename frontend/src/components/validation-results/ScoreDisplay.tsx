/**
 * Score display component for validation results
 */

import { CheckCircle, Clock, TrendingUp } from "lucide-react";
import { ScoreDisplayProps } from "./types";
import { getScoreColor, safeRender } from "./utils";

export default function ScoreDisplay({ score, recommendation, report }: ScoreDisplayProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {/* Overall Score */}
      <div className="text-center">
        <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${getScoreColor(score)} text-white font-bold text-xl mb-2`}>
          {score}
        </div>
        <div className="text-sm opacity-90">Overall Score</div>
        <div className="font-semibold">{score}/10</div>
      </div>

      {/* Recommendation */}
      <div className="text-center">
        <CheckCircle className="w-16 h-16 mx-auto mb-2 text-green-300" />
        <div className="text-sm opacity-90">Recommendation</div>
        <div className="font-semibold text-sm">{recommendation}</div>
      </div>

      {/* Timeline */}
      <div className="text-center">
        <Clock className="w-16 h-16 mx-auto mb-2 text-blue-300" />
        <div className="text-sm opacity-90">Timeline</div>
        <div className="font-semibold">
          {safeRender(report.recommendations?.timeline_to_market) || "6-12 months"}
        </div>
      </div>

      {/* Market Size */}
      <div className="text-center">
        <TrendingUp className="w-16 h-16 mx-auto mb-2 text-emerald-300" />
        <div className="text-sm opacity-90">Market Size</div>
        <div className="font-semibold">
          {safeRender(report.market_analysis?.market_size?.tam) || "$1.2B+"}
        </div>
      </div>
    </div>
  );
}
