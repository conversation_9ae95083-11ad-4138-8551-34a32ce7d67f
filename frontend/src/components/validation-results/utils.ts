/**
 * Utility functions for validation results components
 */

/**
 * Safely render content of any type as a string
 */
export const safeRender = (content: any): string => {
  if (typeof content === 'string') return content;
  if (typeof content === 'number') return content.toString();
  if (typeof content === 'boolean') return content.toString();
  if (content === null || content === undefined) return '';
  if (Array.isArray(content)) return content.map(item => safeRender(item)).join(', ');
  if (typeof content === 'object') return JSON.stringify(content);
  return String(content);
};

/**
 * Get color classes based on validation score
 */
export const getScoreColor = (score: number): string => {
  if (score >= 8) return "from-green-500 to-emerald-600";
  if (score >= 6) return "from-yellow-500 to-orange-500";
  if (score >= 4) return "from-orange-500 to-red-500";
  return "from-red-500 to-red-700";
};

/**
 * Get recommendation text based on score
 */
export const getRecommendation = (score: number): string => {
  if (score >= 8) return "Highly Recommended";
  if (score >= 6) return "Recommended with Conditions";
  if (score >= 4) return "Proceed with Caution";
  return "Not Recommended";
};

/**
 * Extract score from validation report
 */
export const extractScore = (report: any): number => {
  return report?.validation_summary?.overall_score || 
         report?.overall_score || 
         report?.score || 
         7.5;
};

/**
 * Extract recommendation from validation report
 */
export const extractRecommendation = (report: any): string => {
  return report?.validation_summary?.recommendation || 
         report?.recommendation || 
         getRecommendation(extractScore(report));
};

/**
 * Check if content exists and is not empty
 */
export const hasContent = (content: any): boolean => {
  if (!content) return false;
  if (Array.isArray(content)) return content.length > 0;
  if (typeof content === 'object') return Object.keys(content).length > 0;
  if (typeof content === 'string') return content.trim().length > 0;
  return true;
};

/**
 * Render array content as list items
 */
export const renderArrayContent = (
  items: any[], 
  colorClass: string = "bg-gray-500",
  isCompact: boolean = false
): JSX.Element[] => {
  if (!Array.isArray(items)) return [];
  
  return items.map((item, index) => (
    <li key={index} className="flex items-start gap-2">
      <div className={`w-2 h-2 rounded-full ${colorClass} ${isCompact ? 'mt-1.5' : 'mt-2'} flex-shrink-0`}></div>
      <span className={`text-gray-700 ${isCompact ? 'text-sm' : ''}`}>
        {safeRender(item)}
      </span>
    </li>
  ));
};

/**
 * Render feature cards with different styling based on priority
 */
export const renderFeatureCards = (
  features: any[], 
  title: string, 
  colorClass: string,
  isCompact: boolean = false
): JSX.Element[] => {
  if (!Array.isArray(features)) return [];
  
  return features.map((feature, index) => (
    <div 
      key={index} 
      className={`${isCompact ? 'p-2' : 'p-3'} ${colorClass} rounded-lg`}
    >
      <p className={`text-gray-700 ${isCompact ? 'text-sm' : ''}`}>
        {safeRender(feature)}
      </p>
    </div>
  ));
};

/**
 * Format market size data for display
 */
export const formatMarketSize = (marketSize: any) => {
  if (!marketSize) return null;
  
  return {
    tam: safeRender(marketSize.tam) || "N/A",
    sam: safeRender(marketSize.sam) || "N/A", 
    som: safeRender(marketSize.som) || "N/A"
  };
};

/**
 * Get tab configuration for navigation
 */
export const getTabConfig = () => [
  { id: "overview", label: "Executive Summary", icon: "BookOpen" },
  { id: "concept", label: "Project Concept", icon: "Lightbulb" },
  { id: "users", label: "Target Users", icon: "Users" },
  { id: "features", label: "Features", icon: "Package" },
  { id: "market", label: "Market Analysis", icon: "BarChart3" },
  { id: "competitive", label: "Competition", icon: "Target" },
  { id: "business", label: "Business Model", icon: "DollarSign" },
  { id: "challenges", label: "Challenges", icon: "AlertTriangle" },
  { id: "financial", label: "Financial", icon: "TrendingUp" },
  { id: "roadmap", label: "Roadmap", icon: "GitBranch" },
  { id: "citations", label: "Sources", icon: "BookOpen" },
  { id: "next", label: "Next Steps", icon: "ArrowRight" }
];

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number = 150): string => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};
