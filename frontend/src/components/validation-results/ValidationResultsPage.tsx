/**
 * Refactored ValidationResultsPage component using modular architecture
 */

"use client";

import { useState } from "react";
import { X, Download, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";

import { ValidationResultsProps } from "./types";
import { extractScore, extractRecommendation, truncateText } from "./utils";
import ScoreDisplay from "./ScoreDisplay";
import NavigationTabs from "./NavigationTabs";
import {
  OverviewSection,
  ProjectConceptSection,
  MarketAnalysisSection,
  TargetUsersSection,
  FeaturesSection,
  CompetitiveSection,
  BusinessModelSection,
  ChallengesSection,
  FinancialSection,
  RoadmapSection,
  CitationsSection,
  NextStepsSection
} from "./sections";

export default function ValidationResultsPage({
  isOpen,
  report,
  ideaTitle,
  ideaDescription,
  userAnswers,
  onClose,
  isStandalonePage = false
}: ValidationResultsProps) {
  const [activeSection, setActiveSection] = useState("overview");

  if (!isOpen && !isStandalonePage) return null;

  const score = extractScore(report);
  const recommendation = extractRecommendation(report);

  const renderSection = (sectionId: string, isCompact: boolean = false) => {
    const sectionProps = { report, isCompact };
    
    switch (sectionId) {
      case "overview":
        return <OverviewSection {...sectionProps} />;
      case "concept":
        return <ProjectConceptSection {...sectionProps} />;
      case "users":
        return <TargetUsersSection {...sectionProps} />;
      case "features":
        return <FeaturesSection {...sectionProps} />;
      case "market":
        return <MarketAnalysisSection {...sectionProps} />;
      case "competitive":
        return <CompetitiveSection {...sectionProps} />;
      case "business":
        return <BusinessModelSection {...sectionProps} />;
      case "challenges":
        return <ChallengesSection {...sectionProps} />;
      case "financial":
        return <FinancialSection {...sectionProps} />;
      case "roadmap":
        return <RoadmapSection {...sectionProps} />;
      case "citations":
        return <CitationsSection {...sectionProps} />;
      case "next":
        return <NextStepsSection {...sectionProps} />;
      default:
        return <OverviewSection {...sectionProps} />;
    }
  };

  // Standalone page version
  if (isStandalonePage) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{ideaTitle}</h1>
                <p className="text-gray-600 mt-2">{ideaDescription}</p>
              </div>
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export PDF
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share Report
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Score Banner */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <ScoreDisplay score={score} recommendation={recommendation} report={report} />
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <NavigationTabs 
            activeSection={activeSection} 
            onSectionChange={setActiveSection}
          />
          
          {/* Content Sections */}
          {renderSection(activeSection)}
        </div>
      </div>
    );
  }

  // Modal version
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-7xl w-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{ideaTitle}</h1>
              <p className="text-indigo-100 mt-1">{truncateText(ideaDescription, 150)}</p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose} className="text-white hover:bg-white/20">
              <X className="w-5 h-5" />
            </Button>
          </div>
          
          {/* Score Banner */}
          <div className="mt-6">
            <ScoreDisplay score={score} recommendation={recommendation} report={report} />
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(95vh-200px)]">
          <NavigationTabs 
            activeSection={activeSection} 
            onSectionChange={setActiveSection}
            isCompact={true}
          />
          
          {/* Content Sections */}
          <div className="px-4 pb-4">
            {renderSection(activeSection, true)}
          </div>
        </div>
      </div>
    </div>
  );
}
