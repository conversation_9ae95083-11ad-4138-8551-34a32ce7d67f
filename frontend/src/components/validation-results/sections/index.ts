/**
 * Export all section components
 */

export { default as OverviewSection } from './OverviewSection';
export { default as ProjectConceptSection } from './ProjectConceptSection';
export { default as MarketAnalysisSection } from './MarketAnalysisSection';

// Placeholder exports for sections not yet implemented
// These can be implemented later following the same pattern
export const TargetUsersSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Target Users & Market</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Target users section - to be implemented</p>
    </div>
  </div>
);

export const FeaturesSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Features & Technical Requirements</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Features section - to be implemented</p>
    </div>
  </div>
);

export const CompetitiveSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Competitive Landscape</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Competitive analysis section - to be implemented</p>
    </div>
  </div>
);

export const BusinessModelSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Business Model</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Business model section - to be implemented</p>
    </div>
  </div>
);

export const ChallengesSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Challenges & Opportunities</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Challenges section - to be implemented</p>
    </div>
  </div>
);

export const FinancialSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Financial Projections</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Financial projections section - to be implemented</p>
    </div>
  </div>
);

export const RoadmapSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Implementation Roadmap</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Roadmap section - to be implemented</p>
    </div>
  </div>
);

export const CitationsSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Sources & Citations</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Citations section - to be implemented</p>
    </div>
  </div>
);

export const NextStepsSection = ({ report, isCompact = false }: any) => (
  <div className={isCompact ? "space-y-6" : "space-y-8"}>
    <h2 className={`${isCompact ? "text-xl" : "text-2xl"} font-bold text-gray-900`}>Next Steps & Recommendations</h2>
    <div className="bg-white p-6 rounded-lg border">
      <p className="text-gray-500">Next steps section - to be implemented</p>
    </div>
  </div>
);
