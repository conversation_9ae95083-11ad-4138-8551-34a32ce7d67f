/**
 * Market analysis section component for validation results
 */

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3 } from "lucide-react";
import { SectionProps } from "../types";
import { safeRender, hasContent, formatMarketSize, renderArrayContent } from "../utils";

export default function MarketAnalysisSection({ report, isCompact = false }: SectionProps) {
  const titleClass = isCompact ? "text-xl" : "text-2xl";
  const spacingClass = isCompact ? "space-y-6" : "space-y-8";
  const textSize = isCompact ? "text-sm" : "";
  const headerSize = isCompact ? "text-sm" : "";
  const gridGap = isCompact ? "gap-3" : "gap-4";

  if (!hasContent(report.market_analysis)) {
    return (
      <div className={spacingClass}>
        <h2 className={`${titleClass} font-bold text-gray-900`}>Market Analysis</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-gray-500">Market analysis information not available.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const marketSize = formatMarketSize(report.market_analysis.market_size);

  return (
    <div className={spacingClass}>
      <h2 className={`${titleClass} font-bold text-gray-900`}>Market Analysis</h2>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              Market Size Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`space-y-4 ${textSize}`}>
              {/* TAM/SAM/SOM Display */}
              {marketSize && (
                <div className={`grid md:grid-cols-3 ${gridGap}`}>
                  <div className={`${isCompact ? 'p-3' : 'p-4'} bg-green-50 rounded-lg text-center`}>
                    <div className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-green-800`}>
                      TAM (Total Addressable Market)
                    </div>
                    <div className={`${isCompact ? 'text-sm' : 'text-lg'} font-bold text-green-900`}>
                      {marketSize.tam}
                    </div>
                  </div>
                  <div className={`${isCompact ? 'p-3' : 'p-4'} bg-blue-50 rounded-lg text-center`}>
                    <div className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-blue-800`}>
                      SAM (Serviceable Addressable Market)
                    </div>
                    <div className={`${isCompact ? 'text-sm' : 'text-lg'} font-bold text-blue-900`}>
                      {marketSize.sam}
                    </div>
                  </div>
                  <div className={`${isCompact ? 'p-3' : 'p-4'} bg-purple-50 rounded-lg text-center`}>
                    <div className={`${isCompact ? 'text-xs' : 'text-sm'} font-medium text-purple-800`}>
                      SOM (Serviceable Obtainable Market)
                    </div>
                    <div className={`${isCompact ? 'text-sm' : 'text-lg'} font-bold text-purple-900`}>
                      {marketSize.som}
                    </div>
                  </div>
                </div>
              )}

              {/* Market Trends */}
              {hasContent(report.market_analysis.market_trends) && (
                <div>
                  <h4 className={`font-semibold text-gray-900 mb-2 ${headerSize}`}>
                    Market Trends
                  </h4>
                  <ul className="space-y-2">
                    {renderArrayContent(
                      report.market_analysis.market_trends,
                      "bg-blue-500",
                      isCompact
                    )}
                  </ul>
                </div>
              )}

              {/* Additional market analysis data */}
              {typeof report.market_analysis === 'object' && (
                <div className="space-y-3">
                  {Object.entries(report.market_analysis)
                    .filter(([key]) => !['market_size', 'market_trends', 'competitive_landscape'].includes(key))
                    .map(([key, value]) => {
                      if (!hasContent(value)) return null;
                      
                      return (
                        <div key={key}>
                          <h4 className={`font-semibold text-gray-900 mb-2 ${headerSize}`}>
                            {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                          </h4>
                          {Array.isArray(value) ? (
                            <ul className="space-y-1">
                              {renderArrayContent(value, "bg-gray-500", isCompact)}
                            </ul>
                          ) : (
                            <p className="text-gray-700">{safeRender(value)}</p>
                          )}
                        </div>
                      );
                    })}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Competitive Landscape (if available in market analysis) */}
        {hasContent(report.market_analysis.competitive_landscape) && (
          <Card>
            <CardHeader>
              <CardTitle>Competitive Landscape Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`text-gray-700 ${textSize}`}>
                {typeof report.market_analysis.competitive_landscape === 'string' ? (
                  <p>{safeRender(report.market_analysis.competitive_landscape)}</p>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(report.market_analysis.competitive_landscape).map(([key, value]) => (
                      <div key={key}>
                        <strong>{key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</strong>{' '}
                        {safeRender(value)}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
