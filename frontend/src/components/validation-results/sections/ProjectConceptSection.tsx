/**
 * Project concept section component for validation results
 */

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Lightbulb } from "lucide-react";
import { SectionProps } from "../types";
import { safeRender, hasContent } from "../utils";

export default function ProjectConceptSection({ report, isCompact = false }: SectionProps) {
  const titleClass = isCompact ? "text-xl" : "text-2xl";
  const spacingClass = isCompact ? "space-y-6" : "space-y-8";
  const textSize = isCompact ? "text-sm" : "";
  const headerSize = isCompact ? "text-sm" : "";

  if (!hasContent(report.project_concept)) {
    return (
      <div className={spacingClass}>
        <h2 className={`${titleClass} font-bold text-gray-900`}>Project Concept</h2>
        <Card>
          <CardContent className="p-6">
            <p className="text-gray-500">Project concept information not available.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={spacingClass}>
      <h2 className={`${titleClass} font-bold text-gray-900`}>Project Concept</h2>
      
      <div className="grid gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="w-5 h-5 text-yellow-600" />
              Core Concept
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`space-y-4 ${textSize}`}>
              {/* Problem Statement */}
              {hasContent(report.project_concept.problem_statement) && (
                <div>
                  <h4 className={`font-semibold text-gray-900 mb-2 ${headerSize}`}>
                    Problem Statement
                  </h4>
                  <p className="text-gray-700">
                    {safeRender(report.project_concept.problem_statement)}
                  </p>
                </div>
              )}

              {/* Solution Overview */}
              {hasContent(report.project_concept.solution_overview) && (
                <div>
                  <h4 className={`font-semibold text-gray-900 mb-2 ${headerSize}`}>
                    Solution Overview
                  </h4>
                  <p className="text-gray-700">
                    {safeRender(report.project_concept.solution_overview)}
                  </p>
                </div>
              )}

              {/* Target Market */}
              {hasContent(report.project_concept.target_market) && (
                <div>
                  <h4 className={`font-semibold text-gray-900 mb-2 ${headerSize}`}>
                    Target Market
                  </h4>
                  <p className="text-gray-700">
                    {safeRender(report.project_concept.target_market)}
                  </p>
                </div>
              )}

              {/* Value Proposition */}
              {hasContent(report.project_concept.value_proposition) && (
                <div>
                  <h4 className={`font-semibold text-gray-900 mb-2 ${headerSize}`}>
                    Value Proposition
                  </h4>
                  <p className="text-gray-700">
                    {safeRender(report.project_concept.value_proposition)}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Additional concept details if available */}
        {typeof report.project_concept === 'object' && Object.keys(report.project_concept).length > 4 && (
          <Card>
            <CardHeader>
              <CardTitle>Additional Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className={`space-y-3 ${textSize}`}>
                {Object.entries(report.project_concept)
                  .filter(([key]) => !['problem_statement', 'solution_overview', 'target_market', 'value_proposition'].includes(key))
                  .map(([key, value]) => (
                    <div key={key}>
                      <h4 className={`font-semibold text-gray-900 mb-1 ${headerSize}`}>
                        {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </h4>
                      <p className="text-gray-700">{safeRender(value)}</p>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
