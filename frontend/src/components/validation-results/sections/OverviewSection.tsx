/**
 * Overview section component for validation results
 */

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { CheckCircle, AlertTriangle } from "lucide-react";
import { SectionProps } from "../types";
import { safeRender, hasContent, renderArrayContent } from "../utils";

export default function OverviewSection({ report, isCompact = false }: SectionProps) {
  const titleClass = isCompact ? "text-xl" : "text-2xl";
  const spacingClass = isCompact ? "space-y-6" : "space-y-8";
  const cardPadding = isCompact ? "p-4" : "p-6";
  const textSize = isCompact ? "text-sm" : "";

  return (
    <div className={spacingClass}>
      <div>
        <h2 className={`${titleClass} font-bold text-gray-900 ${isCompact ? 'mb-3' : 'mb-4'}`}>
          Executive Summary
        </h2>
        <div className={`bg-white ${cardPadding} rounded-lg border`}>
          <p className={`text-gray-700 leading-relaxed ${textSize}`}>
            {safeRender(report.executive_summary) || 
             "Comprehensive analysis completed using multi-analyst approach with market research, financial modeling, technical assessment, and risk evaluation."}
          </p>
        </div>
      </div>

      <div className={`grid lg:grid-cols-2 ${isCompact ? 'gap-4' : 'gap-8'}`}>
        {/* Key Strengths */}
        {hasContent(report.validation_summary?.key_strengths) && (
          <Card>
            <CardHeader className={isCompact ? "pb-3" : ""}>
              <CardTitle className="text-green-800 flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Key Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className={`space-y-2 ${textSize}`}>
                {renderArrayContent(
                  report.validation_summary.key_strengths, 
                  "bg-green-500",
                  isCompact
                )}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Key Concerns */}
        {hasContent(report.validation_summary?.key_concerns) && (
          <Card>
            <CardHeader className={isCompact ? "pb-3" : ""}>
              <CardTitle className="text-orange-800 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Key Concerns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className={`space-y-2 ${textSize}`}>
                {renderArrayContent(
                  report.validation_summary.key_concerns,
                  "bg-orange-500", 
                  isCompact
                )}
              </ul>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Additional Summary Information */}
      {hasContent(report.validation_summary?.confidence_level) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-800">Analysis Confidence</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-gray-700 ${textSize}`}>
              <p><strong>Confidence Level:</strong> {safeRender(report.validation_summary.confidence_level)}</p>
              {report.validation_summary.recommendation && (
                <p className="mt-2">
                  <strong>Recommendation:</strong> {safeRender(report.validation_summary.recommendation)}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
