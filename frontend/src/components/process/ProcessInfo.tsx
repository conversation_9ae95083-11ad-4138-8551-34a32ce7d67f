import { LucideIcon } from "lucide-react";

interface ProcessInfoItemProps {
  icon: LucideIcon;
  title: string;
  description: string;
  bgColor: string;
  textColor: string;
}

export function ProcessInfoItem({ icon: Icon, title, description, bgColor, textColor }: ProcessInfoItemProps) {
  return (
    <div className="text-center group">
      <div className={`w-16 h-16 ${bgColor} rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300`}>
        <Icon className={`h-8 w-8 ${textColor}`} />
      </div>
      <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2">{title}</h4>
      <p className="text-gray-600 dark:text-gray-300">{description}</p>
    </div>
  );
}