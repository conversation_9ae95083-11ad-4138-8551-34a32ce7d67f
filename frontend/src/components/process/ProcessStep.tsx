import { LucideIcon } from "lucide-react";

interface ProcessStepProps {
  week: string;
  title: string;
  items: string[];
  icon: LucideIcon;
  color: string;
  isLast: boolean;
}

export function ProcessStep({ week, title, items, icon: Icon, color, isLast }: ProcessStepProps) {
  return (
    <div className="relative">
      {!isLast && (
        <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-gray-200 to-transparent dark:from-gray-700" />
      )}
      
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-300 group border border-gray-100 dark:border-gray-700">
        <div className="text-center mb-6">
          <div className={`w-16 h-16 ${color} rounded-xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-105 transition-transform duration-300`}>
            <Icon className="h-8 w-8 text-white" />
          </div>
          <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">{week}</div>
          <h3 className="text-lg font-bold text-gray-900 dark:text-gray-100">{title}</h3>
        </div>
        
        <ul className="space-y-3">
          {items.map((item, index) => (
            <li key={index} className="flex items-start text-gray-600 dark:text-gray-300">
              <span className="w-1.5 h-1.5 rounded-full bg-primary mt-2 mr-3 flex-shrink-0" />
              <span className="text-sm">{item}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}