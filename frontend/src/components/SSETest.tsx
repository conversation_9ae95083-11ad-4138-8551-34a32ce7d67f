"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

export default function SSETest() {
  const [messages, setMessages] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>("Disconnected");
  
  const quickLogin = async () => {
    console.log('🔐 Quick login for testing...');
    setConnectionStatus("Setting up test account...");
    
    try {
      // First, try to register the test user (in case it doesn't exist)
      const registerResponse = await fetch('http://localhost:8000/api/v1/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpass123',
          confirm_password: 'testpass123',
          full_name: 'Test User'
        })
      });
      
      console.log('Registration response:', registerResponse.status);
      
      // Now try to login (whether registration succeeded or failed)
      const loginResponse = await fetch('http://localhost:8000/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'testpass123'
        })
      });
      
      console.log('Login response:', loginResponse.status);
      
      if (loginResponse.ok) {
        const data = await loginResponse.json();
        localStorage.setItem('auth-token', data.access_token);
        setConnectionStatus(`Logged in successfully! Token: ${data.access_token.substring(0, 20)}...`);
        setMessages(prev => [...prev, "✅ Quick login successful - you can now test validation SSE"]);
      } else {
        const error = await loginResponse.text();
        console.log('Login error response:', error);
        setConnectionStatus(`Login failed: ${loginResponse.status} - ${error}`);
        setMessages(prev => [...prev, `❌ Login failed: ${error}`]);
      }
    } catch (error) {
      console.error('Network error:', error);
      setConnectionStatus(`Network error: ${error}`);
      setMessages(prev => [...prev, `❌ Network error: ${error}`]);
    }
  };

  const testBasicSSE = async () => {
    console.log('🧪 TEST: Starting basic SSE test...');
    setMessages([]);
    setConnectionStatus("Connecting to test endpoint...");

    try {
      const url = `http://localhost:8000/api/v1/validation/test-sse`;
      console.log('🧪 TEST: Connecting to:', url);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      console.log('🧪 TEST: Response status:', response.status, response.statusText);
      console.log('🧪 TEST: Response headers:', Object.fromEntries(response.headers));

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      setIsConnected(true);
      setConnectionStatus("Connected - receiving data");

      let chunkCount = 0;
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('🧪 TEST: Stream completed');
          setConnectionStatus("Stream completed");
          break;
        }

        chunkCount++;
        const chunk = decoder.decode(value);
        console.log(`🧪 TEST: Chunk ${chunkCount}:`, chunk);

        // Parse SSE data
        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              console.log('🧪 TEST: Parsed data:', data);
              setMessages(prev => [...prev, `${data.type}: ${data.message} (count: ${data.count})`]);
            } catch (e) {
              console.log('🧪 TEST: Failed to parse data:', line);
            }
          }
        }
      }

    } catch (error) {
      console.error('🧪 TEST: SSE Error:', error);
      setConnectionStatus(`Error: ${error}`);
    } finally {
      setIsConnected(false);
    }
  };

  const testValidationSSE = async () => {
    console.log('🧪 TEST: Starting validation SSE test...');
    setMessages([]);
    setConnectionStatus("Connecting to validation endpoint...");

    // Get token from localStorage or auth store
    let token = localStorage.getItem('auth-token');
    
    // Also try the auth store format
    if (!token) {
      const authData = localStorage.getItem('auth-storage');
      if (authData) {
        try {
          const parsed = JSON.parse(authData);
          token = parsed.state?.token;
        } catch (e) {
          console.log('Failed to parse auth storage:', e);
        }
      }
    }
    
    if (!token) {
      setConnectionStatus("Error: No authentication token found. Please log in first at /login");
      setMessages(prev => [...prev, "❌ Please navigate to /login first, then return to test validation"]);
      return;
    }
    
    console.log('🧪 TEST: Found token:', token.substring(0, 20) + '...');

    try {
      const url = new URL('http://localhost:8000/api/v1/validation/comprehensive-validation-stream');
      url.searchParams.append('token', token);
      url.searchParams.append('idea', 'Test AI application for symptom checking');
      url.searchParams.append('answers', JSON.stringify({
        target_users: "Healthcare consumers",
        problem_solved: "Quick symptom assessment", 
        unique_value: "AI-powered analysis"
      }));
      url.searchParams.append('use_celery', 'false');

      console.log('🧪 TEST: Connecting to validation URL:', url.toString());

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      console.log('🧪 TEST: Validation response status:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      setIsConnected(true);
      setConnectionStatus("Connected - receiving validation data");

      let chunkCount = 0;
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('🧪 TEST: Validation stream completed');
          setConnectionStatus("Validation stream completed");
          break;
        }

        chunkCount++;
        const chunk = decoder.decode(value);
        console.log(`🧪 TEST: Validation chunk ${chunkCount}:`, chunk);

        // Parse SSE data
        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));
              console.log('🧪 TEST: Parsed validation data:', data);
              setMessages(prev => [...prev, `${data.type || 'unknown'}: ${data.message || data.analyst || JSON.stringify(data).substring(0, 100)}`]);
            } catch (e) {
              console.log('🧪 TEST: Failed to parse validation data:', line);
            }
          }
        }
      }

    } catch (error) {
      console.error('🧪 TEST: Validation SSE Error:', error);
      setConnectionStatus(`Error: ${error}`);
    } finally {
      setIsConnected(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">SSE Connection Test</h1>
      
      <div className="space-y-4 mb-6">
        <div className="flex flex-wrap gap-4">
          <Button 
            onClick={testBasicSSE} 
            disabled={isConnected}
          >
            Test Basic SSE
          </Button>
          
          <Button 
            onClick={quickLogin} 
            disabled={isConnected}
            variant="outline"
          >
            Quick Login
          </Button>
          
          <Button 
            onClick={testValidationSSE} 
            disabled={isConnected}
            variant="secondary"
          >
            Test Validation SSE
          </Button>
        </div>
      </div>

      <div className="mb-4">
        <strong>Status:</strong> <span className={isConnected ? "text-green-600" : "text-gray-600"}>{connectionStatus}</span>
      </div>

      <div className="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
        <h3 className="font-semibold mb-2">Messages:</h3>
        {messages.length === 0 ? (
          <p className="text-gray-500">No messages yet...</p>
        ) : (
          <ul className="space-y-1">
            {messages.map((message, index) => (
              <li key={index} className="text-sm font-mono bg-white p-2 rounded">
                {message}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}