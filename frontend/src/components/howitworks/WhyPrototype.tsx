export default function WhyPrototype() {
  return (
    <div className="mt-16 relative">
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 rounded-xl" />
      <div className="relative bg-white/10 dark:bg-gray-800/20 backdrop-blur-xl rounded-xl p-8 shadow-xl border border-white/20 dark:border-gray-700/30 hover:border-indigo-200/30 dark:hover:border-indigo-700/40 transition-all duration-300">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Why Choose Interactive Prototypes?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Interactive prototypes help validate your concept with real user feedback before full development, 
            saving time, money, and reducing risks significantly.
          </p>
        </div>
      </div>
    </div>
  );
}