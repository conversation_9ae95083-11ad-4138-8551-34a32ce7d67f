import type { WorkStep as WorkStepType } from "@/types/howitworks";

interface Props {
  step: WorkStepType;
  index: number;
}

const gradients = [
  "from-purple-500 to-indigo-500",
  "from-blue-500 to-cyan-500",
  "from-green-500 to-emerald-500",
  "from-orange-500 to-yellow-500"
];

const WorkStep = ({ step, index }: Props) => {
  const Icon = step.icon;
  const gradient = gradients[index];
  
  return (
    <div className="relative bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 group">
      <div className="absolute -top-3 right-4">
        <span className={`px-3 py-1 text-sm text-white rounded-full bg-gradient-to-r ${gradient}`}>
          {step.badge}
        </span>
      </div>
      
      <div className="flex items-center mb-4">
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center mr-4 bg-gradient-to-br ${gradient}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
          Step {index + 1}
        </span>
      </div>
      
      <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        {step.title}
      </h3>
      <p className="text-gray-600 dark:text-gray-300 mb-4">
        {step.description}
      </p>
      
      <ul className="space-y-2">
        {step.features.map((feature, idx) => (
          <li key={idx} className="flex items-center text-gray-600 dark:text-gray-300">
            <span className={`w-1.5 h-1.5 rounded-full mr-2 bg-gradient-to-br ${gradient}`} />
            {feature}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default WorkStep;