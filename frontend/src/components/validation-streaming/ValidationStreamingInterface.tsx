/**
 * Refactored ValidationStreamingInterface component using modular architecture
 * This component reuses the ToolActivityInterface components for consistency
 */

"use client";

import { useState } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ToolActivityInterface } from "@/components/tool-activity";

interface ValidationStreamingInterfaceProps {
  isVisible: boolean;
  ideaDescription: string;
  answers: Record<string, string>;
  token: string;
  onComplete: (report: any) => void;
  onError: (error: string) => void;
  onClose?: () => void;
  onPreviewReport?: (report: any) => void;
}

export default function ValidationStreamingInterface({
  isVisible,
  ideaDescription,
  answers,
  token,
  onComplete,
  onError,
  onClose,
  onPreviewReport,
}: ValidationStreamingInterfaceProps) {
  const [showPreview, setShowPreview] = useState(false);
  const [previewReport, setPreviewReport] = useState<any>(null);

  const handleComplete = (report: any) => {
    // Store the report for preview
    setPreviewReport(report);
    
    // Call the original completion handler
    onComplete(report);
  };

  const handlePreviewReport = () => {
    if (previewReport && onPreviewReport) {
      onPreviewReport(previewReport);
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">AI Validation Streaming</h2>
              <p className="text-indigo-100 mt-1">
                Real-time validation analysis with AI agents and external tools
              </p>
            </div>
            {onClose && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onClose}
                className="text-white hover:bg-white/20"
              >
                <X className="w-5 h-5" />
              </Button>
            )}
          </div>
        </div>

        {/* Tool Activity Interface */}
        <div className="flex-1 overflow-hidden">
          <ToolActivityInterface
            isVisible={true}
            ideaDescription={ideaDescription}
            answers={answers}
            token={token}
            onComplete={handleComplete}
            onError={onError}
            onClose={undefined} // Don't show close button in nested component
          />
        </div>

        {/* Footer with Preview Button */}
        {previewReport && (
          <div className="border-t bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Validation completed successfully
              </div>
              
              {onPreviewReport && (
                <Button
                  onClick={handlePreviewReport}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  Preview Report
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
