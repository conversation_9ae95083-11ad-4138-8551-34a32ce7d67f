/**
 * Advanced Analytics Dashboard Component
 */

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>C<PERSON>, Line, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  AreaChart, Area, Scatter<PERSON>hart, Scatter
} from 'recharts';
import {
  TrendingUp, TrendingDown, Users, Clock, DollarSign,
  Activity, AlertTriangle, CheckCircle, Zap, Brain
} from 'lucide-react';

interface DashboardData {
  systemMetrics: {
    activeValidations: number;
    completedToday: number;
    avgValidationTime: number;
    successRate: number;
    errorRate: number;
    costToday: number;
  };
  providerStats: Array<{
    provider: string;
    requests: number;
    failures: number;
    avgTime: number;
    cost: number;
  }>;
  agentPerformance: Array<{
    agent: string;
    successRate: number;
    avgTime: number;
    executions: number;
  }>;
  timeSeriesData: Array<{
    timestamp: string;
    validations: number;
    errors: number;
    avgTime: number;
  }>;
  userMetrics: {
    totalUsers: number;
    activeUsers: number;
    newUsers: number;
    retentionRate: number;
  };
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

export default function AdvancedDashboard() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  useEffect(() => {
    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, refreshInterval);
    return () => clearInterval(interval);
  }, [timeRange, refreshInterval]);

  const fetchDashboardData = async () => {
    try {
      const response = await fetch(`/api/v1/analytics/dashboard?range=${timeRange}`);
      if (response.ok) {
        const dashboardData = await response.json();
        setData(dashboardData);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center text-gray-500 py-8">
        Failed to load dashboard data
      </div>
    );
  }

  const MetricCard = ({ title, value, change, icon: Icon, color = 'blue' }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <div className={`flex items-center mt-1 ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {change >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
              <span className="text-sm ml-1">{Math.abs(change)}%</span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-full bg-${color}-100`}>
          <Icon className={`w-6 h-6 text-${color}-600`} />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <div className="flex space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          <select
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            className="border border-gray-300 rounded-md px-3 py-2"
          >
            <option value={10000}>10s refresh</option>
            <option value={30000}>30s refresh</option>
            <option value={60000}>1m refresh</option>
            <option value={300000}>5m refresh</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Active Validations"
          value={data.systemMetrics.activeValidations}
          icon={Activity}
          color="blue"
        />
        <MetricCard
          title="Completed Today"
          value={data.systemMetrics.completedToday}
          icon={CheckCircle}
          color="green"
        />
        <MetricCard
          title="Avg Time (min)"
          value={Math.round(data.systemMetrics.avgValidationTime / 60)}
          icon={Clock}
          color="yellow"
        />
        <MetricCard
          title="Success Rate"
          value={`${Math.round(data.systemMetrics.successRate * 100)}%`}
          icon={TrendingUp}
          color="green"
        />
      </div>

      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Validation Timeline */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Validation Timeline</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data.timeSeriesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="timestamp" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area
                type="monotone"
                dataKey="validations"
                stackId="1"
                stroke="#8884d8"
                fill="#8884d8"
                name="Validations"
              />
              <Area
                type="monotone"
                dataKey="errors"
                stackId="1"
                stroke="#ff7300"
                fill="#ff7300"
                name="Errors"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Provider Performance */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Provider Performance</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.providerStats}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="provider" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="requests" fill="#8884d8" name="Requests" />
              <Bar dataKey="failures" fill="#ff7300" name="Failures" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Agent Success Rates */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Agent Success Rates</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.agentPerformance} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" domain={[0, 1]} />
              <YAxis dataKey="agent" type="category" width={100} />
              <Tooltip formatter={(value) => `${Math.round(value * 100)}%`} />
              <Bar dataKey="successRate" fill="#00C49F" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Provider Distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Provider Usage</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data.providerStats}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ provider, percent }) => `${provider} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="requests"
              >
                {data.providerStats.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Performance vs Cost */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Performance vs Cost</h3>
          <ResponsiveContainer width="100%" height={300}>
            <ScatterChart data={data.providerStats}>
              <CartesianGrid />
              <XAxis dataKey="avgTime" name="Avg Time" unit="s" />
              <YAxis dataKey="cost" name="Cost" unit="$" />
              <Tooltip cursor={{ strokeDasharray: '3 3' }} />
              <Scatter name="Providers" dataKey="cost" fill="#8884d8" />
            </ScatterChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Real-time Alerts */}
      {data.systemMetrics.errorRate > 0.1 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-800 font-medium">
              High Error Rate Detected: {Math.round(data.systemMetrics.errorRate * 100)}%
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
