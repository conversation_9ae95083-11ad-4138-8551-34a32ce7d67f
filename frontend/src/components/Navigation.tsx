"use client";

import React, { useState } from 'react';
import { Menu, X, Rocket } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/store/authStore';

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated } = useAuthStore();
  const router = useRouter();

  const handleValidateClick = () => {
    if (isAuthenticated) {
      router.push('/validate-idea');
    } else {
      router.push('/login');
    }
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offset = 80;
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - offset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      setIsOpen(false);
    }
  };

  const NavButton = ({ 
    children, 
    onClick 
  }: { 
    children: React.ReactNode; 
    onClick?: () => void;
  }) => (
    <Button
      variant="ghost"
      onClick={onClick}
      className="relative px-4 py-2 text-sm font-medium transition-all duration-200 before:absolute before:inset-x-0 before:bottom-0 before:h-0.5 before:origin-left before:scale-x-0 before:bg-primary before:transition-transform hover:before:scale-x-100"
    >
      {children}
    </Button>
  );

  return (
    <nav className="bg-white/80 backdrop-blur-lg dark:bg-gray-900/80 shadow-sm dark:shadow-gray-800/10 sticky top-0 z-50 border-b border-gray-200 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <a href="/" className="flex items-center group">
              <img 
                src="/logo-site.png" 
                alt="Incepta Logo" 
                className="h-8 w-auto transition-transform duration-200 group-hover:scale-105" 
              />
            </a>
          </div>
          
          {/* Desktop Menu */}
          <div className="hidden md:flex items-center gap-1">
            <NavButton onClick={() => scrollToSection('about')}>
              About
            </NavButton>
            <NavButton onClick={() => scrollToSection('benefits')}>
              Benefits
            </NavButton>
            <NavButton onClick={() => scrollToSection('how-it-works')}>
              How it Works
            </NavButton>
            <NavButton onClick={() => scrollToSection('process')}>
              Process
            </NavButton>
            <Button 
              onClick={handleValidateClick}
              className="bg-primary hover:bg-primary/90 text-white ml-2 shadow-md hover:shadow-lg transition-all duration-200"
            >
              <Rocket className="w-4 h-4 mr-2" />
              Validate Your Idea
            </Button>
          </div>

          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <div className="md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <button
              onClick={() => scrollToSection('about')}
              className="block px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors w-full text-left"
            >
              About
            </button>
            <button
              onClick={() => scrollToSection('benefits')}
              className="block px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors w-full text-left"
            >
              Benefits
            </button>
            <button
              onClick={() => scrollToSection('how-it-works')}
              className="block px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors w-full text-left"
            >
              How it Works
            </button>
            <button
              onClick={() => scrollToSection('process')}
              className="block px-3 py-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors w-full text-left"
            >
              Process
            </button>
            <div className="px-3 py-2">
              <Button 
                onClick={handleValidateClick}
                className="w-full bg-primary hover:bg-primary/90 text-white"
              >
                <Rocket className="w-4 h-4 mr-2" />
                Validate Your Idea
              </Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}