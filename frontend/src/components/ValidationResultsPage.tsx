"use client";

import { useState } from "react";
import { 
  X, 
  Lightbulb, 
  Users, 
  Package, 
  BarChart3, 
  ExternalLink, 
  Presentation, 
  GitBranch,
  TrendingUp,
  DollarSign,
  Shield,
  AlertTriangle,
  Target,
  Clock,
  Star,
  Download,
  Share2,
  BookOpen,
  Zap,
  CheckCircle,
  XCircle,
  ArrowRight,
  Play,
  Pause,
  <PERSON>pForward,
  SkipBack,
  Maximize,
  Eye,
  Bookmark,
  Heart,
  MessageCircle,
  Calendar,
  MapPin
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

// Incepta-Proto Validation Report Structure
interface ValidationReport {
  validation_summary?: {
    overall_score?: number;
    recommendation?: string;
    confidence_level?: string;
    key_strengths?: string[];
    key_concerns?: string[];
    market_timing?: string;
    investment_requirement?: string;
    time_to_market?: string;
    risk_level?: string;
  };
  executive_summary?: string;
  project_concept?: {
    problem_statement?: string;
    solution_overview?: string;
    target_problem?: string;
    value_proposition?: string;
    differentiation?: string;
  };
  target_users?: {
    primary_personas?: any[];
    market_segmentation?: any;
    user_needs?: string[];
    market_size?: string;
  };
  features?: {
    core_features?: any[];
    must_have_features?: any[];
    nice_to_have_features?: any[];
    technical_complexity?: string;
  };
  market_analysis?: {
    market_size?: {
      tam?: string;
      sam?: string;
      som?: string;
    };
    market_trends?: string[];
    growth_rate?: string;
    market_maturity?: string;
  };
  competitive_landscape?: {
    direct_competitors?: any[];
    indirect_competitors?: any[];
    competitive_advantages?: string[];
    market_positioning?: string;
  };
  business_model?: {
    value_proposition?: string;
    revenue_streams?: any[];
    pricing_strategy?: string;
    monetization_approach?: string;
  };
  challenges_and_opportunities?: {
    market_challenges?: string[];
    growth_opportunities?: string[];
    risk_factors?: string[];
    success_factors?: string[];
  };
  financial_projections?: {
    revenue_projections?: any;
    investment_requirements?: string;
    funding_strategy?: string;
    break_even_timeline?: string;
  };
  implementation_roadmap?: {
    phases?: any[];
    timeline?: string;
    resource_requirements?: string;
    technical_milestones?: string[];
  };
  citations_and_sources?: {
    research_sources?: any[];
    market_data_sources?: any[];
    expert_opinions?: any[];
    methodology?: string;
  };
  next_steps?: {
    immediate_actions?: string[];
    short_term_goals?: string[];
    long_term_vision?: string;
    success_metrics?: string[];
  };
  // Legacy support for backward compatibility
  market_opportunity?: any;
  business_model_viability?: any;
  technical_feasibility?: any;
  risk_profile?: any;
  recommendations?: any;
  analyst_consensus?: any;
}

interface ValidationResultsPageProps {
  isOpen: boolean;
  report: ValidationReport;
  ideaTitle: string;
  ideaDescription: string;
  userAnswers: Record<string, string>;
  onClose: () => void;
  isStandalonePage?: boolean;
}

// Helper function to safely render content
const safeRender = (content: any): string => {
  if (typeof content === 'string') return content;
  if (typeof content === 'number') return content.toString();
  if (typeof content === 'boolean') return content.toString();
  if (content === null || content === undefined) return '';
  if (Array.isArray(content)) return content.map(item => safeRender(item)).join(', ');
  if (typeof content === 'object') return JSON.stringify(content);
  return String(content);
};

export default function ValidationResultsPage({
  isOpen,
  report,
  ideaTitle,
  ideaDescription,
  userAnswers,
  onClose,
  isStandalonePage = false,
}: ValidationResultsPageProps) {
  const [activeSection, setActiveSection] = useState("overview");

  // Debug: Log the report structure
  console.log('🔍 ValidationResultsPage received report:', report);
  console.log('📊 Report keys:', Object.keys(report || {}));
  console.log('🎯 Validation summary:', report?.validation_summary);

  if (!isOpen) return null;

  const score = report.validation_summary?.overall_score || 0;
  const recommendation = report.validation_summary?.recommendation || "Assessment Complete";

  const getScoreColor = (score: number) => {
    if (score >= 8) return "from-green-500 to-emerald-600";
    if (score >= 6) return "from-yellow-500 to-orange-500";
    if (score >= 4) return "from-orange-500 to-red-500";
    return "from-red-500 to-pink-600";
  };

  if (isStandalonePage) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{ideaTitle}</h1>
                <p className="text-gray-600 mt-2">{ideaDescription}</p>
              </div>
              <div className="flex items-center gap-4">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export PDF
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Score Banner */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${getScoreColor(score)} text-white font-bold text-xl mb-2`}>
                  {score}
                </div>
                <div className="text-sm opacity-90">Overall Score</div>
                <div className="font-semibold">{score}/10</div>
              </div>
              <div className="text-center">
                <CheckCircle className="w-16 h-16 mx-auto mb-2 text-green-300" />
                <div className="text-sm opacity-90">Recommendation</div>
                <div className="font-semibold text-sm">{recommendation}</div>
              </div>
              <div className="text-center">
                <Clock className="w-16 h-16 mx-auto mb-2 text-blue-300" />
                <div className="text-sm opacity-90">Timeline</div>
                <div className="font-semibold">{report.recommendations?.timeline_to_market || "6-12 months"}</div>
              </div>
              <div className="text-center">
                <TrendingUp className="w-16 h-16 mx-auto mb-2 text-emerald-300" />
                <div className="text-sm opacity-90">Market Size</div>
                <div className="font-semibold">{report.market_analysis?.market_size?.tam || "$1.2B+"}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Navigation Tabs */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-8">
            {[
              { id: "overview", label: "Executive Summary", icon: BookOpen },
              { id: "concept", label: "Project Concept", icon: Lightbulb },
              { id: "users", label: "Target Users", icon: Users },
              { id: "features", label: "Features", icon: Package },
              { id: "market", label: "Market Analysis", icon: BarChart3 },
              { id: "competitive", label: "Competitors", icon: Shield },
              { id: "business", label: "Business Model", icon: DollarSign },
              { id: "challenges", label: "Challenges & Opportunities", icon: AlertTriangle },
              { id: "financial", label: "Financial", icon: TrendingUp },
              { id: "roadmap", label: "Implementation", icon: GitBranch },
              { id: "citations", label: "Sources", icon: BookOpen },
              { id: "next", label: "Next Steps", icon: Target },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveSection(tab.id)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-md font-medium text-sm transition-all ${
                    activeSection === tab.id
                      ? "bg-white text-[#4F46E5] shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Content Sections */}
          {activeSection === "overview" && (
            <div className="space-y-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Executive Summary</h2>
                <div className="bg-white p-6 rounded-lg border">
                  <p className="text-gray-700 leading-relaxed">
                    {safeRender(report.executive_summary) || "Comprehensive analysis completed using multi-analyst approach with market research, financial modeling, technical assessment, and risk evaluation."}
                  </p>
                </div>
              </div>

              <div className="grid lg:grid-cols-2 gap-8">
                {report.validation_summary?.key_strengths && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-green-800 flex items-center gap-2">
                        <CheckCircle className="w-6 h-6" />
                        Key Strengths
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {report.validation_summary.key_strengths.map((strength, index) => (
                          <li key={index} className="flex items-start gap-3 text-gray-700">
                            <div className="w-2 h-2 rounded-full bg-green-500 mt-2 flex-shrink-0"></div>
                            {safeRender(strength)}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {report.validation_summary?.key_concerns && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-orange-800 flex items-center gap-2">
                        <AlertTriangle className="w-6 h-6" />
                        Key Concerns
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-3">
                        {report.validation_summary.key_concerns.map((concern, index) => (
                          <li key={index} className="flex items-start gap-3 text-gray-700">
                            <div className="w-2 h-2 rounded-full bg-orange-500 mt-2 flex-shrink-0"></div>
                            {safeRender(concern)}
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}

          {activeSection === "concept" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Project Concept</h2>
              {report.project_concept ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Lightbulb className="w-6 h-6" />
                        Problem Statement & Solution
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Problem Statement</h4>
                          <p className="text-gray-700">{safeRender(report.project_concept.problem_statement)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Solution Overview</h4>
                          <p className="text-gray-700">{safeRender(report.project_concept.solution_overview)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Value Proposition</h4>
                          <p className="text-gray-700">{safeRender(report.project_concept.value_proposition)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Project concept analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "users" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Target Users & Market</h2>
              {report.target_users ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Users className="w-6 h-6" />
                        User Personas & Segmentation
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {report.target_users.primary_personas && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Primary Personas</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.target_users.primary_personas) ? 
                                report.target_users.primary_personas.map((persona, index) => (
                                  <div key={index} className="p-3 bg-blue-50 rounded-lg">
                                    <p className="text-gray-700">{safeRender(persona)}</p>
                                  </div>
                                )) : 
                                <p className="text-gray-700">{safeRender(report.target_users.primary_personas)}</p>
                              }
                            </div>
                          </div>
                        )}
                        {report.target_users.user_needs && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">User Needs</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.target_users.user_needs) ?
                                report.target_users.user_needs.map((need, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                                    <span className="text-gray-700">{safeRender(need)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700">{safeRender(report.target_users.user_needs)}</li>
                              }
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Target user analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "features" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Features & Technical Requirements</h2>
              {report.features ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Package className="w-6 h-6" />
                        Feature Prioritization
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        {report.features.core_features && (
                          <div>
                            <h4 className="font-semibold text-green-800 mb-3">Core Features (Must Have)</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.features.core_features) ?
                                report.features.core_features.map((feature, index) => (
                                  <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                                    <p className="text-gray-700">{safeRender(feature)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700">{safeRender(report.features.core_features)}</p>
                              }
                            </div>
                          </div>
                        )}
                        {report.features.must_have_features && (
                          <div>
                            <h4 className="font-semibold text-yellow-800 mb-3">Must-Have Features</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.features.must_have_features) ?
                                report.features.must_have_features.map((feature, index) => (
                                  <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <p className="text-gray-700">{safeRender(feature)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700">{safeRender(report.features.must_have_features)}</p>
                              }
                            </div>
                          </div>
                        )}
                        {report.features.nice_to_have_features && (
                          <div>
                            <h4 className="font-semibold text-blue-800 mb-3">Nice-to-Have Features</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.features.nice_to_have_features) ?
                                report.features.nice_to_have_features.map((feature, index) => (
                                  <div key={index} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                    <p className="text-gray-700">{safeRender(feature)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700">{safeRender(report.features.nice_to_have_features)}</p>
                              }
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Feature analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "market" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Market Analysis</h2>
              {report.market_analysis ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="w-6 h-6" />
                        Market Size & Opportunity
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {report.market_analysis.market_size && (
                          <div className="grid md:grid-cols-3 gap-4">
                            <div className="p-4 bg-green-50 rounded-lg text-center">
                              <div className="text-sm font-medium text-green-800">TAM (Total Addressable Market)</div>
                              <div className="text-lg font-bold text-green-900">{safeRender(report.market_analysis.market_size.tam)}</div>
                            </div>
                            <div className="p-4 bg-blue-50 rounded-lg text-center">
                              <div className="text-sm font-medium text-blue-800">SAM (Serviceable Addressable Market)</div>
                              <div className="text-lg font-bold text-blue-900">{safeRender(report.market_analysis.market_size.sam)}</div>
                            </div>
                            <div className="p-4 bg-purple-50 rounded-lg text-center">
                              <div className="text-sm font-medium text-purple-800">SOM (Serviceable Obtainable Market)</div>
                              <div className="text-lg font-bold text-purple-900">{safeRender(report.market_analysis.market_size.som)}</div>
                            </div>
                          </div>
                        )}
                        {report.market_analysis.market_trends && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Market Trends</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.market_analysis.market_trends) ?
                                report.market_analysis.market_trends.map((trend, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <TrendingUp className="w-4 h-4 text-green-500 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700">{safeRender(trend)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700">{safeRender(report.market_analysis.market_trends)}</li>
                              }
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Market analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "competitive" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Competitive Landscape</h2>
              {report.competitive_landscape ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Shield className="w-6 h-6" />
                        Competitive Analysis
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {report.competitive_landscape.direct_competitors && (
                          <div>
                            <h4 className="font-semibold text-red-800 mb-2">Direct Competitors</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.competitive_landscape.direct_competitors) ?
                                report.competitive_landscape.direct_competitors.map((competitor, index) => (
                                  <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <p className="text-gray-700">{safeRender(competitor)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700">{safeRender(report.competitive_landscape.direct_competitors)}</p>
                              }
                            </div>
                          </div>
                        )}
                        {report.competitive_landscape.competitive_advantages && (
                          <div>
                            <h4 className="font-semibold text-green-800 mb-2">Competitive Advantages</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.competitive_landscape.competitive_advantages) ?
                                report.competitive_landscape.competitive_advantages.map((advantage, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <CheckCircle className="w-4 h-4 text-green-500 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700">{safeRender(advantage)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700">{safeRender(report.competitive_landscape.competitive_advantages)}</li>
                              }
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Competitive analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "business" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Business Model</h2>
              {report.business_model ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-6 h-6" />
                        Revenue Model & Monetization
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Value Proposition</h4>
                          <p className="text-gray-700">{safeRender(report.business_model.value_proposition)}</p>
                        </div>
                        {report.business_model.revenue_streams && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Revenue Streams</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.business_model.revenue_streams) ?
                                report.business_model.revenue_streams.map((stream, index) => (
                                  <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                                    <p className="text-gray-700">{safeRender(stream)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700">{safeRender(report.business_model.revenue_streams)}</p>
                              }
                            </div>
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Pricing Strategy</h4>
                          <p className="text-gray-700">{safeRender(report.business_model.pricing_strategy)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Business model analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "challenges" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Challenges & Opportunities</h2>
              {report.challenges_and_opportunities ? (
                <div className="grid lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-red-800 flex items-center gap-2">
                        <AlertTriangle className="w-6 h-6" />
                        Market Challenges
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {Array.isArray(report.challenges_and_opportunities.market_challenges) ?
                          report.challenges_and_opportunities.market_challenges.map((challenge, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 rounded-full bg-red-500 mt-2 flex-shrink-0"></div>
                              <span className="text-gray-700">{safeRender(challenge)}</span>
                            </li>
                          )) :
                          <li className="text-gray-700">{safeRender(report.challenges_and_opportunities.market_challenges)}</li>
                        }
                      </ul>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-green-800 flex items-center gap-2">
                        <TrendingUp className="w-6 h-6" />
                        Growth Opportunities
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ul className="space-y-2">
                        {Array.isArray(report.challenges_and_opportunities.growth_opportunities) ?
                          report.challenges_and_opportunities.growth_opportunities.map((opportunity, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 rounded-full bg-green-500 mt-2 flex-shrink-0"></div>
                              <span className="text-gray-700">{safeRender(opportunity)}</span>
                            </li>
                          )) :
                          <li className="text-gray-700">{safeRender(report.challenges_and_opportunities.growth_opportunities)}</li>
                        }
                      </ul>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Challenges and opportunities analysis not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "financial" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Financial Projections</h2>
              {report.financial_projections ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="w-6 h-6" />
                        Investment & Revenue
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Investment Requirements</h4>
                          <p className="text-gray-700">{safeRender(report.financial_projections.investment_requirements)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Break-even Timeline</h4>
                          <p className="text-gray-700">{safeRender(report.financial_projections.break_even_timeline)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Funding Strategy</h4>
                          <p className="text-gray-700">{safeRender(report.financial_projections.funding_strategy)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Financial projections not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "roadmap" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Implementation Roadmap</h2>
              {report.implementation_roadmap ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <GitBranch className="w-6 h-6" />
                        Development Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {report.implementation_roadmap.phases && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-3">Development Phases</h4>
                            <div className="space-y-3">
                              {Array.isArray(report.implementation_roadmap.phases) ?
                                report.implementation_roadmap.phases.map((phase, index) => (
                                  <div key={index} className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div className="text-gray-700">{safeRender(phase)}</div>
                                  </div>
                                )) :
                                <div className="text-gray-700">{safeRender(report.implementation_roadmap.phases)}</div>
                              }
                            </div>
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Timeline</h4>
                          <p className="text-gray-700">{safeRender(report.implementation_roadmap.timeline)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Resource Requirements</h4>
                          <p className="text-gray-700">{safeRender(report.implementation_roadmap.resource_requirements)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Implementation roadmap not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "citations" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Sources & Citations</h2>
              {report.citations_and_sources ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BookOpen className="w-6 h-6" />
                        Research Sources
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {report.citations_and_sources.research_sources && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-2">Research Sources</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.citations_and_sources.research_sources) ?
                                report.citations_and_sources.research_sources.map((source, index) => (
                                  <div key={index} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                    <p className="text-gray-700 text-sm">{safeRender(source)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700">{safeRender(report.citations_and_sources.research_sources)}</p>
                              }
                            </div>
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">Methodology</h4>
                          <p className="text-gray-700">{safeRender(report.citations_and_sources.methodology)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Citations and sources not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {activeSection === "next" && (
            <div className="space-y-8">
              <h2 className="text-2xl font-bold text-gray-900">Next Steps & Recommendations</h2>
              {report.next_steps ? (
                <div className="grid gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Target className="w-6 h-6" />
                        Action Plan
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {report.next_steps.immediate_actions && (
                          <div>
                            <h4 className="font-semibold text-red-800 mb-2">Immediate Actions (Week 1-2)</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.next_steps.immediate_actions) ?
                                report.next_steps.immediate_actions.map((action, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-2 h-2 rounded-full bg-red-500 mt-2 flex-shrink-0"></div>
                                    <span className="text-gray-700">{safeRender(action)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700">{safeRender(report.next_steps.immediate_actions)}</li>
                              }
                            </ul>
                          </div>
                        )}
                        {report.next_steps.short_term_goals && (
                          <div>
                            <h4 className="font-semibold text-yellow-800 mb-2">Short-term Goals (Month 1-3)</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.next_steps.short_term_goals) ?
                                report.next_steps.short_term_goals.map((goal, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-2 h-2 rounded-full bg-yellow-500 mt-2 flex-shrink-0"></div>
                                    <span className="text-gray-700">{safeRender(goal)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700">{safeRender(report.next_steps.short_term_goals)}</li>
                              }
                            </ul>
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-green-800 mb-2">Long-term Vision</h4>
                          <p className="text-gray-700">{safeRender(report.next_steps.long_term_vision)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-6">
                    <p className="text-gray-600">Next steps and recommendations not available in this report.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Modal version (for when not standalone) - Full report display
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-7xl w-full max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">{ideaTitle}</h1>
              <p className="text-indigo-100 mt-1">{ideaDescription.substring(0, 150)}...</p>
            </div>
            <Button onClick={onClose} variant="ghost" size="sm" className="text-white hover:bg-white/20">
              <X className="w-5 h-5" />
            </Button>
          </div>
          
          {/* Score Banner */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
            <div className="text-center">
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${getScoreColor(score)} text-white font-bold text-xl mb-2`}>
                {score}
              </div>
              <div className="text-sm opacity-90">Overall Score</div>
              <div className="font-semibold">{score}/10</div>
            </div>
            <div className="text-center">
              <CheckCircle className="w-16 h-16 mx-auto mb-2 text-green-300" />
              <div className="text-sm opacity-90">Recommendation</div>
              <div className="font-semibold text-sm">{recommendation}</div>
            </div>
            <div className="text-center">
              <Clock className="w-16 h-16 mx-auto mb-2 text-blue-300" />
              <div className="text-sm opacity-90">Timeline</div>
              <div className="font-semibold">{report.recommendations?.timeline_to_market || "6-12 months"}</div>
            </div>
            <div className="text-center">
              <TrendingUp className="w-16 h-16 mx-auto mb-2 text-emerald-300" />
              <div className="text-sm opacity-90">Market Size</div>
              <div className="font-semibold">{report.market_analysis?.market_size?.tam || "$1.2B+"}</div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto max-h-[calc(95vh-200px)]">
          {/* Navigation Tabs */}
          <div className="flex space-x-1 bg-gray-100 p-1 m-4 rounded-lg">
            {[
              { id: "overview", label: "Executive Summary", icon: BookOpen },
              { id: "concept", label: "Project Concept", icon: Lightbulb },
              { id: "users", label: "Target Users", icon: Users },
              { id: "features", label: "Features", icon: Package },
              { id: "market", label: "Market Analysis", icon: BarChart3 },
              { id: "competitive", label: "Competitors", icon: Shield },
              { id: "business", label: "Business Model", icon: DollarSign },
              { id: "challenges", label: "Challenges & Opportunities", icon: AlertTriangle },
              { id: "financial", label: "Financial", icon: TrendingUp },
              { id: "roadmap", label: "Implementation", icon: GitBranch },
              { id: "citations", label: "Sources", icon: BookOpen },
              { id: "next", label: "Next Steps", icon: Target },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveSection(tab.id)}
                  className={`flex items-center gap-2 px-3 py-2 rounded-md font-medium text-xs transition-all ${
                    activeSection === tab.id
                      ? "bg-white text-[#4F46E5] shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <Icon className="w-3 h-3" />
                  {tab.label}
                </button>
              );
            })}
          </div>

          {/* Content Sections - Same as standalone version */}
          <div className="px-4 pb-4">
            {activeSection === "overview" && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 mb-3">Executive Summary</h2>
                  <div className="bg-white p-4 rounded-lg border">
                    <p className="text-gray-700 leading-relaxed text-sm">
                      {safeRender(report.executive_summary) || "Comprehensive analysis completed using multi-analyst approach with market research, financial modeling, technical assessment, and risk evaluation."}
                    </p>
                  </div>
                </div>

                <div className="grid lg:grid-cols-2 gap-4">
                  {report.validation_summary?.key_strengths && (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-green-800 flex items-center gap-2 text-base">
                          <CheckCircle className="w-5 h-5" />
                          Key Strengths
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {report.validation_summary.key_strengths.map((strength, index) => (
                            <li key={index} className="flex items-start gap-2 text-gray-700 text-sm">
                              <div className="w-2 h-2 rounded-full bg-green-500 mt-1.5 flex-shrink-0"></div>
                              {safeRender(strength)}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}

                  {report.validation_summary?.key_concerns && (
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-orange-800 flex items-center gap-2 text-base">
                          <AlertTriangle className="w-5 h-5" />
                          Key Concerns
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ul className="space-y-2">
                          {report.validation_summary.key_concerns.map((concern, index) => (
                            <li key={index} className="flex items-start gap-2 text-gray-700 text-sm">
                              <div className="w-2 h-2 rounded-full bg-orange-500 mt-1.5 flex-shrink-0"></div>
                              {safeRender(concern)}
                            </li>
                          ))}
                        </ul>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {activeSection === "concept" && (
              <div className="space-y-6">
                <h2 className="text-xl font-bold text-gray-900">Project Concept</h2>
                {report.project_concept ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <Lightbulb className="w-5 h-5" />
                        Problem Statement & Solution
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1 text-sm">Problem Statement</h4>
                          <p className="text-gray-700 text-sm">{safeRender(report.project_concept.problem_statement)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1 text-sm">Solution Overview</h4>
                          <p className="text-gray-700 text-sm">{safeRender(report.project_concept.solution_overview)}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1 text-sm">Value Proposition</h4>
                          <p className="text-gray-700 text-sm">{safeRender(report.project_concept.value_proposition)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-4">
                      <p className="text-gray-600 text-sm">Project concept analysis not available in this report.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeSection === "users" && (
              <div className="space-y-6">
                <h2 className="text-xl font-bold text-gray-900">Target Users & Market</h2>
                {report.target_users ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <Users className="w-5 h-5" />
                        User Personas & Segmentation
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {report.target_users.primary_personas && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1 text-sm">Primary Personas</h4>
                            <div className="space-y-2">
                              {Array.isArray(report.target_users.primary_personas) ? 
                                report.target_users.primary_personas.map((persona, index) => (
                                  <div key={index} className="p-2 bg-blue-50 rounded-lg">
                                    <p className="text-gray-700 text-sm">{safeRender(persona)}</p>
                                  </div>
                                )) : 
                                <p className="text-gray-700 text-sm">{safeRender(report.target_users.primary_personas)}</p>
                              }
                            </div>
                          </div>
                        )}
                        {report.target_users.user_needs && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1 text-sm">User Needs</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.target_users.user_needs) ?
                                report.target_users.user_needs.map((need, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-2 h-2 rounded-full bg-blue-500 mt-1.5 flex-shrink-0"></div>
                                    <span className="text-gray-700 text-sm">{safeRender(need)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700 text-sm">{safeRender(report.target_users.user_needs)}</li>
                              }
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-4">
                      <p className="text-gray-600 text-sm">Target user analysis not available in this report.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeSection === "market" && (
              <div className="space-y-6">
                <h2 className="text-xl font-bold text-gray-900">Market Analysis</h2>
                {report.market_analysis ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <BarChart3 className="w-5 h-5" />
                        Market Size & Opportunity
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {report.market_analysis.market_size && (
                          <div className="grid md:grid-cols-3 gap-3">
                            <div className="p-3 bg-green-50 rounded-lg text-center">
                              <div className="text-xs font-medium text-green-800">TAM</div>
                              <div className="text-sm font-bold text-green-900">{safeRender(report.market_analysis.market_size.tam)}</div>
                            </div>
                            <div className="p-3 bg-blue-50 rounded-lg text-center">
                              <div className="text-xs font-medium text-blue-800">SAM</div>
                              <div className="text-sm font-bold text-blue-900">{safeRender(report.market_analysis.market_size.sam)}</div>
                            </div>
                            <div className="p-3 bg-purple-50 rounded-lg text-center">
                              <div className="text-xs font-medium text-purple-800">SOM</div>
                              <div className="text-sm font-bold text-purple-900">{safeRender(report.market_analysis.market_size.som)}</div>
                            </div>
                          </div>
                        )}
                        {report.market_analysis.market_trends && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1 text-sm">Market Trends</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.market_analysis.market_trends) ?
                                report.market_analysis.market_trends.map((trend, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <TrendingUp className="w-3 h-3 text-green-500 mt-1 flex-shrink-0" />
                                    <span className="text-gray-700 text-sm">{safeRender(trend)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700 text-sm">{safeRender(report.market_analysis.market_trends)}</li>
                              }
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-4">
                      <p className="text-gray-600 text-sm">Market analysis not available in this report.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Add similar compact versions for other sections */}
            {activeSection === "business" && (
              <div className="space-y-6">
                <h2 className="text-xl font-bold text-gray-900">Business Model</h2>
                {report.business_model ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <DollarSign className="w-5 h-5" />
                        Revenue Model & Monetization
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1 text-sm">Value Proposition</h4>
                          <p className="text-gray-700 text-sm">{safeRender(report.business_model.value_proposition)}</p>
                        </div>
                        {report.business_model.revenue_streams && (
                          <div>
                            <h4 className="font-semibold text-gray-900 mb-1 text-sm">Revenue Streams</h4>
                            <div className="space-y-1">
                              {Array.isArray(report.business_model.revenue_streams) ?
                                report.business_model.revenue_streams.map((stream, index) => (
                                  <div key={index} className="p-2 bg-green-50 border border-green-200 rounded-lg">
                                    <p className="text-gray-700 text-sm">{safeRender(stream)}</p>
                                  </div>
                                )) :
                                <p className="text-gray-700 text-sm">{safeRender(report.business_model.revenue_streams)}</p>
                              }
                            </div>
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-1 text-sm">Pricing Strategy</h4>
                          <p className="text-gray-700 text-sm">{safeRender(report.business_model.pricing_strategy)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-4">
                      <p className="text-gray-600 text-sm">Business model analysis not available in this report.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {activeSection === "next" && (
              <div className="space-y-6">
                <h2 className="text-xl font-bold text-gray-900">Next Steps & Recommendations</h2>
                {report.next_steps ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-base">
                        <Target className="w-5 h-5" />
                        Action Plan
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {report.next_steps.immediate_actions && (
                          <div>
                            <h4 className="font-semibold text-red-800 mb-1 text-sm">Immediate Actions (Week 1-2)</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.next_steps.immediate_actions) ?
                                report.next_steps.immediate_actions.map((action, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-2 h-2 rounded-full bg-red-500 mt-1.5 flex-shrink-0"></div>
                                    <span className="text-gray-700 text-sm">{safeRender(action)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700 text-sm">{safeRender(report.next_steps.immediate_actions)}</li>
                              }
                            </ul>
                          </div>
                        )}
                        {report.next_steps.short_term_goals && (
                          <div>
                            <h4 className="font-semibold text-yellow-800 mb-1 text-sm">Short-term Goals (Month 1-3)</h4>
                            <ul className="space-y-1">
                              {Array.isArray(report.next_steps.short_term_goals) ?
                                report.next_steps.short_term_goals.map((goal, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-2 h-2 rounded-full bg-yellow-500 mt-1.5 flex-shrink-0"></div>
                                    <span className="text-gray-700 text-sm">{safeRender(goal)}</span>
                                  </li>
                                )) :
                                <li className="text-gray-700 text-sm">{safeRender(report.next_steps.short_term_goals)}</li>
                              }
                            </ul>
                          </div>
                        )}
                        <div>
                          <h4 className="font-semibold text-green-800 mb-1 text-sm">Long-term Vision</h4>
                          <p className="text-gray-700 text-sm">{safeRender(report.next_steps.long_term_vision)}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="p-4">
                      <p className="text-gray-600 text-sm">Next steps and recommendations not available in this report.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}

            {/* Add fallback for other sections */}
            {!["overview", "concept", "users", "market", "business", "next"].includes(activeSection) && (
              <div className="space-y-6">
                <h2 className="text-xl font-bold text-gray-900">
                  {activeSection === "features" && "Features & Technical Requirements"}
                  {activeSection === "competitive" && "Competitive Landscape"}
                  {activeSection === "challenges" && "Challenges & Opportunities"}
                  {activeSection === "financial" && "Financial Projections"}
                  {activeSection === "roadmap" && "Implementation Roadmap"}
                  {activeSection === "citations" && "Sources & Citations"}
                </h2>
                <Card>
                  <CardContent className="p-4">
                    <p className="text-gray-600 text-sm">This section is available in the full report. Click sections above to explore available content.</p>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}