"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";

interface Question {
  id: string;
  question: string;
  type: "textarea" | "text";
  placeholder?: string;
  required: boolean;
}

interface DynamicQuestionFormProps {
  questions: Question[];
  onSubmit: (answers: Record<string, string>) => void;
  onBack: () => void;
  isSubmitting?: boolean;
}

export default function DynamicQuestionForm({ 
  questions, 
  onSubmit, 
  onBack, 
  isSubmitting = false 
}: DynamicQuestionFormProps) {
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleAnswerChange = (questionId: string, value: string) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
    
    // Clear error when user provides an answer
    if (errors[questionId]) {
      setErrors(prev => ({
        ...prev,
        [questionId]: ""
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    questions.forEach(question => {
      if (question.required && !answers[question.id]?.trim()) {
        newErrors[question.id] = "This field is required";
      }
    });
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(answers);
    }
  };

  const renderQuestion = (question: Question) => {
    const hasError = errors[question.id];
    
    return (
      <div key={question.id} className="space-y-3">
        <label className="text-base font-medium text-gray-900 block">
          {question.question}
          {question.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <textarea
          value={answers[question.id] || ""}
          onChange={(e) => handleAnswerChange(question.id, e.target.value)}
          placeholder={question.placeholder}
          rows={question.type === "text" ? 2 : 4}
          className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none transition-colors ${
            hasError ? "border-red-500" : "border-gray-300"
          }`}
        />
        {hasError && (
          <p className="text-red-600 text-sm">{errors[question.id]}</p>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Additional Information</h2>
        <p className="text-gray-600">
          Please provide more details about your idea to help our AI agents perform comprehensive validation.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {questions.map(renderQuestion)}
        
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            disabled={isSubmitting}
            className="gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Idea
          </Button>
          
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-medium px-8 py-3 gap-2"
          >
            <Brain className="w-5 h-5" />
            {isSubmitting ? "Starting Validation..." : "Start AI Validation"}
          </Button>
        </div>
      </form>
    </div>
  );
}