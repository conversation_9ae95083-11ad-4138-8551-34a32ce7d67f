/**
 * Predictive input component with AI-powered suggestions
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { debounce } from 'lodash';
import { Lightbulb, TrendingUp, Users, DollarSign, Zap } from 'lucide-react';

interface Suggestion {
  id: string;
  text: string;
  category: 'market' | 'tech' | 'business' | 'user' | 'trend';
  confidence: number;
  reasoning: string;
}

interface PredictiveInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minLength?: number;
  maxSuggestions?: number;
  onSuggestionSelect?: (suggestion: Suggestion) => void;
}

const categoryIcons = {
  market: DollarSign,
  tech: Zap,
  business: TrendingUp,
  user: Users,
  trend: Lightbulb
};

const categoryColors = {
  market: 'text-green-600 bg-green-50',
  tech: 'text-blue-600 bg-blue-50',
  business: 'text-purple-600 bg-purple-50',
  user: 'text-orange-600 bg-orange-50',
  trend: 'text-yellow-600 bg-yellow-50'
};

export default function PredictiveInput({
  value,
  onChange,
  placeholder = "Describe your idea...",
  minLength = 10,
  maxSuggestions = 5,
  onSuggestionSelect
}: PredictiveInputProps) {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounced function to get suggestions
  const getSuggestions = useCallback(
    debounce(async (text: string) => {
      if (text.length < minLength) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      setIsLoading(true);
      try {
        const response = await fetch('/api/v1/ai/suggestions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            text, 
            maxSuggestions,
            context: 'idea_validation'
          })
        });

        if (response.ok) {
          const data = await response.json();
          setSuggestions(data.suggestions || []);
          setShowSuggestions(true);
          setSelectedIndex(-1);
        }
      } catch (error) {
        console.error('Failed to get suggestions:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, 500),
    [minLength, maxSuggestions]
  );

  useEffect(() => {
    getSuggestions(value);
  }, [value, getSuggestions]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        if (selectedIndex >= 0) {
          e.preventDefault();
          handleSuggestionClick(suggestions[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  const handleSuggestionClick = (suggestion: Suggestion) => {
    const newValue = value + ' ' + suggestion.text;
    onChange(newValue);
    setShowSuggestions(false);
    setSelectedIndex(-1);
    
    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }

    // Focus back to input
    inputRef.current?.focus();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const handleInputBlur = () => {
    // Delay hiding suggestions to allow clicks
    setTimeout(() => setShowSuggestions(false), 200);
  };

  const handleInputFocus = () => {
    if (suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  return (
    <div className="relative">
      {/* Input Field */}
      <div className="relative">
        <textarea
          ref={inputRef}
          value={value}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onBlur={handleInputBlur}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          className="w-full min-h-[120px] p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
          style={{ paddingBottom: isLoading ? '40px' : '16px' }}
        />
        
        {/* Loading Indicator */}
        {isLoading && (
          <div className="absolute bottom-3 right-3 flex items-center text-gray-500">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-sm">Getting suggestions...</span>
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div
          ref={suggestionsRef}
          className="absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto"
        >
          <div className="p-2 border-b border-gray-100">
            <div className="flex items-center text-sm text-gray-600">
              <Lightbulb className="w-4 h-4 mr-2" />
              AI Suggestions ({suggestions.length})
            </div>
          </div>
          
          {suggestions.map((suggestion, index) => {
            const Icon = categoryIcons[suggestion.category];
            const colorClass = categoryColors[suggestion.category];
            
            return (
              <div
                key={suggestion.id}
                className={`p-3 cursor-pointer transition-colors duration-150 ${
                  index === selectedIndex 
                    ? 'bg-blue-50 border-l-4 border-blue-500' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleSuggestionClick(suggestion)}
              >
                <div className="flex items-start space-x-3">
                  <div className={`p-1 rounded ${colorClass}`}>
                    <Icon className="w-4 h-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {suggestion.text}
                      </p>
                      <span className="ml-2 text-xs text-gray-500">
                        {Math.round(suggestion.confidence * 100)}%
                      </span>
                    </div>
                    
                    <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                      {suggestion.reasoning}
                    </p>
                    
                    <div className="flex items-center mt-2">
                      <span className={`text-xs px-2 py-1 rounded-full ${colorClass}`}>
                        {suggestion.category}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          
          <div className="p-2 border-t border-gray-100 text-center">
            <p className="text-xs text-gray-500">
              Use ↑↓ to navigate, Enter to select, Esc to close
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
