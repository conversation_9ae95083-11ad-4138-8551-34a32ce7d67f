import { Bar<PERSON>hart, DollarSign, Shield, Target, Users, Zap } from "lucide-react";

const benefits = [
  {
    icon: Shield,
    title: "Risk-Free Validation",
    description: "Test your idea with real users before committing to full development. Get market validation with minimal investment."
  },
  {
    icon: Zap,
    title: "Rapid Development",
    description: "Get a working prototype rapidly. Our AI-powered process accelerates development without compromising quality."
  },
  {
    icon: DollarSign,
    title: "Fixed Price",
    description: "No surprises or hidden costs. Get a clear breakdown of costs and timeline before you start."
  },
  {
    icon: Users,
    title: "User Testing Ready",
    description: "Your prototype comes ready for user testing with real features and data flow - not just static mockups."
  },
  {
    icon: Target,
    title: "Enterprise Focus",
    description: "Specialized in complex enterprise solutions, ERP systems, and AI-powered applications."
  },
  {
    icon: BarChart,
    title: "Scalable Architecture",
    description: "Built with scalability in mind, your prototype can evolve into a full production system."
  }
];

export default function Benefits() {
  return (
    <section id="benefits" className="py-20 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Why Choose Our Prototyping Service?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Get all the benefits of a full development cycle with our rapid prototyping service, delivering results in minimal time.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => {
            const gradients = [
              "from-purple-500 to-indigo-500",
              "from-blue-500 to-cyan-500",
              "from-green-500 to-emerald-500",
              "from-orange-500 to-yellow-500",
              "from-pink-500 to-rose-500",
              "from-indigo-500 to-purple-500"
            ];
            
            return (
              <div key={index} className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 rounded-xl transform rotate-1 group-hover:rotate-2 transition-transform duration-300"></div>
                <div className="relative bg-white dark:bg-gray-800 p-8 rounded-xl shadow-sm group-hover:shadow-md transition-all duration-300">
                  <div className={`w-12 h-12 bg-gradient-to-br ${gradients[index]} rounded-lg flex items-center justify-center mb-6`}>
                    <benefit.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {benefit.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-16 bg-gradient-to-r from-indigo-600 to-purple-600 dark:from-indigo-700 dark:to-purple-700 rounded-2xl p-8 text-center text-white">
          <h3 className="text-2xl font-bold mb-4">
            Ready to Transform Your Idea into Reality?
          </h3>
          <p className="text-indigo-100 mb-8 max-w-2xl mx-auto">
            Join successful enterprises who&apos;ve validated their ideas with our interactive prototypes. Book a free consultation today.
          </p>
          <a 
            href="https://tidycal.com/surendraganne/incepta-30-mins"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-white dark:bg-gray-800 text-indigo-600 dark:text-indigo-400 rounded-lg hover:bg-indigo-50 dark:hover:bg-gray-700 transition-colors"
          >
            Schedule Free Consultation
            <Users className="ml-2 h-5 w-5" />
          </a>
        </div>
      </div>
    </section>
  );
}