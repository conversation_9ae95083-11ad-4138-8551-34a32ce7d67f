/**
 * Refactored ToolActivityInterface component using modular architecture
 */

"use client";

import { Button } from "@/components/ui/button";
import { Loader2, X } from "lucide-react";
import { useEffect, useRef } from "react";

import ActivityItem from "./components/ActivityItem";
import AgentFilter from "./components/AgentFilter";
import { useValidation } from "./hooks/useValidation";
import { ToolActivityInterfaceProps } from "./types";
import { filterActivitiesByAgent, scrollToBottom } from "./utils";

export default function ToolActivityInterface({
  isVisible,
  ideaDescription,
  answers,
  token,
  onComplete,
  onError,
  onClose,
  onAgentActivity,
  externalSelectedAgent,
  onAgentSelect,
}: ToolActivityInterfaceProps) {
  const activitiesEndRef = useRef<HTMLDivElement>(null);

  // Use the validation hook for state management
  const {
    activities,
    currentStatus,
    isProcessing,
    validationReport,
    selectedAgent,
    availableAgents,
    showAllAgents,
    setSelectedAgent,
    setShowAllAgents
  } = useValidation({
    ideaDescription,
    answers,
    token,
    isVisible,
    externalSelectedAgent,
    onComplete,
    onError,
    onAgentActivity,
    onAgentSelect
  });

  // Auto-scroll to bottom when activities change
  useEffect(() => {
    scrollToBottom(activitiesEndRef);
  }, [activities]);

  if (!isVisible) return null;

  // Filter activities based on selected agent
  const filteredActivities = filterActivitiesByAgent(activities, selectedAgent);

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="flex flex-col h-full overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">AI Validation in Progress</h2>
              <p className="text-indigo-100 mt-1">
                Our AI agents are analyzing your idea using advanced tools and market research
              </p>
            </div>
            {onClose && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onClose}
                className="text-white hover:bg-white/20"
              >
                <X className="w-5 h-5" />
              </Button>
            )}
          </div>

          {/* Status Bar */}
          <div className="mt-4 flex items-center gap-3">
            {isProcessing && <Loader2 className="w-5 h-5 animate-spin" />}
            <span className="text-sm">{currentStatus}</span>
          </div>

          {/* Progress Indicator */}
          {availableAgents.length > 0 && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-sm text-indigo-100 mb-1">
                <span>Agents Active</span>
                <span>{availableAgents.length} agents working</span>
              </div>
              <div className="w-full bg-indigo-400/30 rounded-full h-2">
                <div 
                  className="bg-white h-2 rounded-full transition-all duration-500"
                  style={{ 
                    width: `${Math.min((availableAgents.length / 12) * 100, 100)}%` 
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Agent Filter */}
        <div className="p-4 border-b bg-gray-50">
          <AgentFilter
            selectedAgent={selectedAgent}
            availableAgents={availableAgents}
            showAllAgents={showAllAgents}
            onAgentSelect={setSelectedAgent}
            onToggleShowAll={() => setShowAllAgents(!showAllAgents)}
          />
        </div>

        {/* Activities List */}
        <div
          className="flex-1 overflow-y-scroll p-4 custom-scrollbar"
          style={{
            maxHeight: 'calc(100vh - 300px)',
            minHeight: '300px'
          }}
        >
          <div className="space-y-3">
            {filteredActivities.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-500">
                  {selectedAgent === "all" 
                    ? "Waiting for agent activities..." 
                    : `No activities from ${selectedAgent} yet...`
                  }
                </div>
              </div>
            ) : (
              filteredActivities.map((activity) => (
                <ActivityItem
                  key={activity.id}
                  activity={activity}
                  isVisible={true}
                />
              ))
            )}
            
            {/* Scroll anchor */}
            <div ref={activitiesEndRef} />
          </div>
        </div>

        {/* Footer */}
        <div className="border-t bg-gray-50 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {activities.length} total activities • {filteredActivities.length} visible
            </div>
            
            {validationReport && (
              <div className="text-sm text-green-600 font-medium">
                ✅ Validation completed successfully
              </div>
            )}
            
            {!isProcessing && !validationReport && (
              <div className="text-sm text-orange-600">
                ⚠️ Validation stopped unexpectedly
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
