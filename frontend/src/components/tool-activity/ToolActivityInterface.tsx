/**
 * Refactored ToolActivityInterface component using modular architecture
 */

"use client";

import { Button } from "@/components/ui/button";
import { Loader2, X } from "lucide-react";
import { useEffect, useRef } from "react";

import ActivityItem from "./components/ActivityItem";
import { useValidation } from "./hooks/useValidation";
import { ToolActivityInterfaceProps } from "./types";
import { scrollToBottom } from "./utils";

export default function ToolActivityInterface({
  isVisible,
  ideaDescription,
  answers,
  token,
  onComplete,
  onError,
  onClose,
  onAgentActivity,
  externalSelectedAgent,
  onAgentSelect,
}: ToolActivityInterfaceProps) {
  const activitiesEndRef = useRef<HTMLDivElement>(null);

  // Use the validation hook for state management
  const {
    activities,
    currentStatus,
    isProcessing,
    validationReport
  } = useValidation({
    ideaDescription,
    answers,
    token,
    isVisible,
    externalSelectedAgent,
    onComplete,
    onError,
    onAgentActivity,
    onAgentSelect
  });

  // Auto-scroll to bottom when activities change
  useEffect(() => {
    scrollToBottom(activitiesEndRef);
  }, [activities]);

  if (!isVisible) return null;

  // Show all activities (no filtering needed)

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="flex flex-col h-full overflow-hidden">
        {/* Compact Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isProcessing && <Loader2 className="w-4 h-4 animate-spin" />}
              <div>
                <h2 className="text-lg font-semibold">AI Validation</h2>
                <p className="text-xs text-indigo-100 truncate max-w-xs">
                  {currentStatus}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* Progress Indicator */}
              {activities.length > 0 && (
                <div className="flex items-center gap-2 text-xs text-indigo-100">
                  <span>{activities.length} activities</span>
                  <div className="w-16 bg-indigo-400/30 rounded-full h-1">
                    <div
                      className="bg-white h-1 rounded-full transition-all duration-500"
                      style={{
                        width: `${Math.min((activities.length / 50) * 100, 100)}%`
                      }}
                    />
                  </div>
                </div>
              )}

              {onClose && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>



        {/* Activities List */}
        <div
          className="flex-1 overflow-y-auto p-3 custom-scrollbar text-sm"
          style={{
            height: 'calc(100vh - 180px)', // Fixed height calculation
            minHeight: '400px'
          }}
        >
          <div className="space-y-2">
            {activities.length === 0 ? (
              <div className="text-center py-6">
                <div className="text-gray-500 text-sm">
                  Waiting for agent activities...
                </div>
              </div>
            ) : (
              activities.map((activity) => (
                <ActivityItem
                  key={activity.id}
                  activity={activity}
                  isVisible={true}
                />
              ))
            )}

            {/* Scroll anchor */}
            <div ref={activitiesEndRef} />
          </div>
        </div>

        {/* Compact Footer */}
        <div className="border-t bg-gray-50 px-3 py-2 flex-shrink-0">
          <div className="flex items-center justify-between text-xs">
            <div className="text-gray-600">
              📊 {activities.length} activities
            </div>

            {validationReport && (
              <div className="text-green-600 font-medium">
                ✅ Complete
              </div>
            )}

            {!isProcessing && !validationReport && (
              <div className="text-orange-600">
                ⚠️ Stopped
              </div>
            )}

            {isProcessing && (
              <div className="text-blue-600">
                🔄 Processing...
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
