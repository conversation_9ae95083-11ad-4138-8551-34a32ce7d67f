/**
 * Refactored ToolActivityInterface component using modular architecture
 */

"use client";

import { Button } from "@/components/ui/button";
import { Loader2, X } from "lucide-react";
import { useEffect, useRef } from "react";

import ActivityItem from "./components/ActivityItem";
import { useValidation } from "./hooks/useValidation";
import { ToolActivityInterfaceProps } from "./types";
import { scrollToBottom } from "./utils";

export default function ToolActivityInterface({
  isVisible,
  ideaDescription,
  answers,
  token,
  onComplete,
  onError,
  onClose,
  onAgentActivity,
  externalSelectedAgent,
  onAgentSelect,
}: ToolActivityInterfaceProps) {
  const activitiesEndRef = useRef<HTMLDivElement>(null);

  // Use the validation hook for state management
  const {
    activities,
    currentStatus,
    isProcessing,
    validationReport,
    selectedAgent,
    availableAgents,
    setSelectedAgent
  } = useValidation({
    ideaDescription,
    answers,
    token,
    isVisible,
    externalSelectedAgent,
    onComplete,
    onError,
    onAgentActivity,
    onAgentSelect
  });

  // Auto-scroll to bottom when activities change
  useEffect(() => {
    scrollToBottom(activitiesEndRef);
  }, [activities]);

  if (!isVisible) return null;

  // Filter activities based on selected agent
  console.log('🔍 Debug - Total activities:', activities.length);
  console.log('🔍 Debug - Selected agent:', selectedAgent);
  console.log('🔍 Debug - Activities:', activities.map(a => ({ type: a.type, content: a.content, provider: a.provider })));

  const filteredActivities = selectedAgent === "all"
    ? activities
    : activities.filter(activity => activity.provider === selectedAgent);

  console.log('🔍 Debug - Filtered activities:', filteredActivities.length);

  return (
    <div className="h-full flex flex-col bg-white relative" style={{ maxHeight: 'calc(100vh - 120px)' }}>
      <div className="flex flex-col h-full min-h-0">
        {/* Compact Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isProcessing && <Loader2 className="w-4 h-4 animate-spin" />}
              <div>
                <h2 className="text-lg font-semibold">AI Validation</h2>
                <p className="text-xs text-indigo-100 truncate max-w-xs">
                  {currentStatus}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* All Agents Button - Show when filtering */}
              {selectedAgent !== "all" && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedAgent("all")}
                  className="text-white hover:bg-white/20 text-xs px-2 py-1 h-6"
                >
                  All Agents
                </Button>
              )}

              {/* Agent Filter Indicator - Compact */}
              {selectedAgent !== "all" && (
                <div className="text-xs text-white bg-white/20 px-2 py-1 rounded max-w-24 truncate">
                  {selectedAgent.split(' ')[0]}
                </div>
              )}

              {onClose && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>



        {/* Activities List */}
        <div
          className="flex-1 overflow-y-auto p-3 custom-scrollbar text-sm"
          style={{
            height: 'calc(100% - 140px)', // Account for header (80px) + footer (60px)
            minHeight: '300px'
          }}
        >
          <div className="space-y-2">
            {filteredActivities.length === 0 ? (
              <div className="text-center py-6">
                <div className="text-gray-500 text-sm">
                  {selectedAgent === "all"
                    ? "Waiting for agent activities..."
                    : `No activities from ${selectedAgent} yet...`
                  }
                </div>
              </div>
            ) : (
              filteredActivities.map((activity) => (
                <ActivityItem
                  key={activity.id}
                  activity={activity}
                  isVisible={true}
                />
              ))
            )}

            {/* Scroll anchor */}
            <div ref={activitiesEndRef} />
          </div>
        </div>

        {/* Fixed Footer with Progress - Always Visible */}
        <div className="border-t bg-white shadow-sm px-3 py-3 flex-shrink-0 min-h-[60px]">
          <div className="flex items-center justify-between text-xs mb-2">
            <div className="text-gray-600 font-medium">
              📊 {activities.length} total • {filteredActivities.length} visible
            </div>

            {validationReport && (
              <div className="text-green-600 font-semibold">
                ✅ Validation completed successfully!
              </div>
            )}

            {!isProcessing && !validationReport && activities.length > 0 && (
              <div className="text-orange-600 font-medium">
                ⚠️ Processing stopped
              </div>
            )}

            {isProcessing && (
              <div className="text-blue-600 font-medium">
                🔄 Processing...
              </div>
            )}
          </div>

          {/* Progress Bar - Always show when there are activities */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-gradient-to-r from-[#4F46E5] to-purple-600 h-2 rounded-full transition-all duration-500"
              style={{
                width: activities.length > 0
                  ? `${Math.min((activities.length / 50) * 100, 100)}%`
                  : '0%'
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
