/**
 * Refactored ToolActivityInterface component using modular architecture
 */

"use client";

import { Button } from "@/components/ui/button";
import { Loader2, X } from "lucide-react";
import { useEffect, useRef } from "react";

import ActivityItem from "./components/ActivityItem";
import { useValidation } from "./hooks/useValidation";
import { ToolActivityInterfaceProps } from "./types";
import { scrollToBottom } from "./utils";

export default function ToolActivityInterface({
  isVisible,
  ideaDescription,
  answers,
  token,
  onComplete,
  onError,
  onClose,
  onAgentActivity,
  externalSelectedAgent,
  onAgentSelect,
}: ToolActivityInterfaceProps) {
  const activitiesEndRef = useRef<HTMLDivElement>(null);

  // Use the validation hook for state management
  const {
    activities,
    currentStatus,
    isProcessing,
    validationReport,
    selectedAgent,
    availableAgents,
    setSelectedAgent
  } = useValidation({
    ideaDescription,
    answers,
    token,
    isVisible,
    externalSelectedAgent,
    onComplete,
    onError,
    onAgentActivity,
    onAgentSelect
  });

  // Auto-scroll to bottom when activities change
  useEffect(() => {
    scrollToBottom(activitiesEndRef);
  }, [activities]);

  if (!isVisible) return null;

  // Filter activities based on selected agent
  const filteredActivities = selectedAgent === "all"
    ? activities
    : activities.filter(activity => activity.provider === selectedAgent);

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="flex flex-col h-full overflow-hidden relative">
        {/* Compact Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {isProcessing && <Loader2 className="w-4 h-4 animate-spin" />}
              <div>
                <h2 className="text-lg font-semibold">AI Validation</h2>
                <p className="text-xs text-indigo-100 truncate max-w-xs">
                  {currentStatus}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* All Agents Button - Show when filtering */}
              {selectedAgent !== "all" && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedAgent("all")}
                  className="text-white hover:bg-white/20 text-xs px-2 py-1 h-6"
                >
                  All Agents
                </Button>
              )}

              {/* Agent Filter Indicator */}
              {selectedAgent !== "all" && (
                <div className="text-xs text-indigo-100 bg-white/20 px-2 py-1 rounded">
                  {selectedAgent}
                </div>
              )}

              {onClose && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="text-white hover:bg-white/20 h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>



        {/* Activities List */}
        <div
          className="flex-1 overflow-y-auto p-3 custom-scrollbar text-sm pb-16"
          style={{
            height: 'calc(100vh - 180px)', // Back to original since footer is fixed
            minHeight: '400px'
          }}
        >
          <div className="space-y-2">
            {filteredActivities.length === 0 ? (
              <div className="text-center py-6">
                <div className="text-gray-500 text-sm">
                  {selectedAgent === "all"
                    ? "Waiting for agent activities..."
                    : `No activities from ${selectedAgent} yet...`
                  }
                </div>
              </div>
            ) : (
              filteredActivities.map((activity) => (
                <ActivityItem
                  key={activity.id}
                  activity={activity}
                  isVisible={true}
                />
              ))
            )}

            {/* Scroll anchor */}
            <div ref={activitiesEndRef} />
          </div>
        </div>

        {/* Fixed Footer with Progress */}
        <div className="border-t bg-gray-50 px-3 py-2 flex-shrink-0 absolute bottom-0 left-0 right-0">
          <div className="flex items-center justify-between text-xs mb-1">
            <div className="text-gray-600">
              📊 {activities.length} total • {filteredActivities.length} visible
            </div>

            {validationReport && (
              <div className="text-green-600 font-medium">
                ✅ Complete
              </div>
            )}

            {!isProcessing && !validationReport && (
              <div className="text-orange-600">
                ⚠️ Stopped
              </div>
            )}

            {isProcessing && (
              <div className="text-blue-600">
                🔄 Processing...
              </div>
            )}
          </div>

          {/* Progress Bar */}
          {activities.length > 0 && (
            <div className="w-full bg-gray-200 rounded-full h-1">
              <div
                className="bg-gradient-to-r from-[#4F46E5] to-purple-600 h-1 rounded-full transition-all duration-500"
                style={{
                  width: `${Math.min((activities.length / 50) * 100, 100)}%`
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
