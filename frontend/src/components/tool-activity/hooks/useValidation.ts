/**
 * Custom hook for validation logic and state management
 */

import { useCallback, useEffect, useState } from "react";
import { SSEMessage, ToolActivity, ValidationHookProps, ValidationState } from "../types";
import {
    buildSSEUrl,
    cleanAgentName,
    generateActivityId,
    globalValidationInProgress,
    isDuplicateActivity,
    parseSSELine,
    setGlobalValidationInProgress
} from "../utils";

export const useValidation = ({
  ideaDescription,
  answers,
  token,
  isVisible,
  externalSelectedAgent,
  onComplete,
  onError,
  onAgentActivity,
  onAgentSelect
}: ValidationHookProps) => {
  const [state, setState] = useState<ValidationState>({
    activities: [],
    currentStatus: "Initializing AI validation tools...",
    isProcessing: false,
    validationReport: null,
    selectedAgent: externalSelectedAgent || "all",
    availableAgents: [],
    hasStartedValidation: false,
    showAllAgents: false
  });

  // Sync with external selected agent
  useEffect(() => {
    if (externalSelectedAgent && externalSelectedAgent !== state.selectedAgent) {
      setState(prev => ({ ...prev, selectedAgent: externalSelectedAgent }));
    }
  }, [externalSelectedAgent, state.selectedAgent]);

  const addActivity = useCallback((activity: Omit<ToolActivity, "id" | "timestamp">) => {
    const newActivity: ToolActivity = {
      ...activity,
      id: generateActivityId(),
      timestamp: new Date()
    };

    setState(prev => {
      // Prevent duplicate activities
      if (isDuplicateActivity(activity, prev.activities)) {
        console.log('🚫 Duplicate activity detected, skipping:', activity.content);
        return prev;
      }

      console.log('✅ Adding new activity:', newActivity.type, '-', newActivity.content);
      return {
        ...prev,
        activities: [...prev.activities, newActivity]
      };
    });
  }, []);

  const updateActivity = useCallback((provider: string, updates: Partial<ToolActivity>) => {
    setState(prev => ({
      ...prev,
      activities: prev.activities.map(activity => {
        // Only update the AI processing task (first task) - leave external tools as separate cards
        if (activity.provider === provider && activity.type === "ai_processing") {
          console.log(`🔄 Updating activity for ${provider}:`, updates);
          return { ...activity, ...updates };
        }
        return activity;
      })
    }));
  }, []);

  const completeExternalTools = useCallback((provider: string) => {
    // Complete Tavily and Firecrawl tasks after AI completion
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        activities: prev.activities.map(activity => {
          if (activity.provider === provider && 
              (activity.tool === "Tavily" || activity.tool === "Firecrawl")) {
            return { ...activity, status: "completed" as const };
          }
          return activity;
        })
      }));
    }, 1000);
  }, []);

  const startValidation = useCallback(async () => {
    if (state.isProcessing || state.hasStartedValidation || globalValidationInProgress) {
      console.log('🚫 Validation already in progress, skipping duplicate start');
      return;
    }

    console.log('🚀 Starting comprehensive validation...');
    setGlobalValidationInProgress(true);
    
    setState(prev => ({
      ...prev,
      isProcessing: true,
      hasStartedValidation: true,
      activities: [], // Clear any existing activities
      currentStatus: "🤖 Assembling elite AI specialist team..."
    }));

    // Show engaging startup messages that rotate every 2 seconds
    const startupMessages = [
      '🤖 Assembling elite AI specialist team...',
      '⚡ Deploying 11 expert validation agents...',
      '💡 AI experts ready to validate your idea...'
    ];

    let messageIndex = 0;

    // Add initial activity
    addActivity({
      type: "status",
      content: startupMessages[0],
      status: "processing"
    });

    const messageInterval = setInterval(() => {
      messageIndex = (messageIndex + 1) % startupMessages.length;

      // Update status
      setState(prev => ({
        ...prev,
        currentStatus: startupMessages[messageIndex]
      }));

      // Replace the startup activity with new message
      setState(prev => ({
        ...prev,
        activities: [{
          id: 'startup-message',
          type: "status" as const,
          content: startupMessages[messageIndex],
          status: "processing" as const,
          timestamp: new Date()
        }]
      }));
    }, 2000); // Change message every 2 seconds

    try {
      // Build SSE URL
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      console.log('🔧 Environment API URL:', process.env.NEXT_PUBLIC_API_URL);
      console.log('🔧 Using base URL:', baseUrl);
      const url = buildSSEUrl(baseUrl, token, ideaDescription, answers, false);

      console.log('🔗 Connecting to:', url);

      // Connect to SSE stream with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log('⏰ Stream timeout after 10 minutes, aborting...');
        controller.abort();
      }, 600000); // 10 minute timeout (enough for all 11 agents)

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      clearTimeout(timeoutId);
      console.log('🔗 Infrastructure response:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No data stream available from infrastructure');
      }

      // Clear startup messages interval once connected
      clearInterval(messageInterval);

      setState(prev => ({
        ...prev,
        activities: [], // Clear startup messages
        currentStatus: "Connected! Processing validation request..."
      }));

      let chunkCount = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('🏁 Data stream completed, total chunks:', chunkCount);
          break;
        }

        chunkCount++;
        const chunk = decoder.decode(value);

        console.log(`📡 Data chunk ${chunkCount}:`, chunk.substring(0, 200) + (chunk.length > 200 ? '...' : ''));

        // Process each line in the chunk
        const lines = chunk.split('\n');
        for (const line of lines) {
          const data = parseSSELine(line);
          if (data) {
            await processSSEMessage(data);
          }
        }
      }

    } catch (error) {
      console.error('❌ Validation failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Clear startup messages interval on error
      clearInterval(messageInterval);

      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStatus: `Error: ${errorMessage}`
      }));

      addActivity({
        type: "error",
        content: `Validation failed: ${errorMessage}`,
        status: "error"
      });

      onError(errorMessage);
    } finally {
      setGlobalValidationInProgress(false);
    }
  }, [state.isProcessing, state.hasStartedValidation, token, ideaDescription, answers, addActivity, onError]);

  const processSSEMessage = useCallback(async (data: SSEMessage) => {
    console.log('📊 Processing data:', data.type, '-', data.message || data.analyst || 'N/A');

    // Handle different message types
    switch (data.type) {
      case "agent_start":
        {
          const agentName = data.agent || data.analyst || data.message || "Unknown Agent";
          const cleanName = cleanAgentName(agentName);

          console.log('🧹 Clean Agent Name:', cleanName);

          // Add agent to filter immediately when they start
          setState(prev => {
            const newAgents = prev.availableAgents.includes(cleanName)
              ? prev.availableAgents
              : [...prev.availableAgents, cleanName];

            return {
              ...prev,
              availableAgents: newAgents,
              currentStatus: `${agentName} is analyzing your idea...`
            };
          });

          // Add AI processing activity
          console.log('🎯 About to call addActivity for agent_start');
          addActivity({
            type: "ai_processing",
            provider: cleanName,
            content: `${agentName} started analysis`,
            status: "processing"
          });
          console.log('✅ addActivity call completed for agent_start');

          if (onAgentActivity) {
            onAgentActivity({ type: 'agent_start', agent: cleanName });
          }
        }
        break;
      case "agent_complete":
        {
          const agentName = data.agent || data.analyst || "Unknown Agent";
          const cleanName = cleanAgentName(agentName);

          updateActivity(cleanName, {
            content: `${agentName} completed analysis`,
            status: "completed"
          });

          completeExternalTools(cleanName);

          if (onAgentActivity) {
            onAgentActivity({ type: 'agent_complete', agent: cleanName });
          }
        }
        break;
      case "phase_start":
        {
          console.log(`🚀 Phase ${data.phase} started with ${data.agents?.length || 0} agents`);

          setState(prev => ({
            ...prev,
            currentStatus: `Phase ${data.phase}/${data.total_phases}: Starting ${data.agents?.join(', ') || 'agents'}...`
          }));

          // Add phase start activity
          console.log('🎯 About to call addActivity for phase_start');
          addActivity({
            type: "ai_processing",
            content: `Phase ${data.phase}/${data.total_phases}: ${data.agents?.join(', ') || 'Starting agents'}`,
            status: "processing"
          });
          console.log('✅ addActivity call completed for phase_start');
        }
        break;
      case "phase_complete":
        {
          console.log(`✅ Phase ${data.phase} completed with ${data.completed_agents} agents`);

          setState(prev => ({
            ...prev,
            currentStatus: `Phase ${data.phase} completed. Proceeding to next phase...`
          }));

          // Add phase complete activity
          addActivity({
            type: "analysis",
            content: `Phase ${data.phase} completed with ${data.completed_agents || 'all'} agents`,
            status: "completed"
          });
        }
        break;
      case "validation_complete":
        {
          console.log('🎉 Validation completed!');

          setState(prev => ({
            ...prev,
            isProcessing: false,
            currentStatus: "Validation completed successfully!"
          }));

          if (data.report) {
            setState(prev => ({ ...prev, validationReport: data.report! }));
            onComplete(data.report);
          }
        }
        break;
      case "final_result":
        {
          console.log('🏆 Final result received');

          setState(prev => ({
            ...prev,
            isProcessing: false,
            currentStatus: "Analysis complete!"
          }));

          if (data.result) {
            setState(prev => ({ ...prev, validationReport: data.result! }));
            onComplete(data.result);
          }
        }
        break;
      case "error":
        {
          const errorMessage = data.message || data.error || 'Unknown error occurred';
          console.error('❌ Validation error:', errorMessage);

          setState(prev => ({
            ...prev,
            isProcessing: false,
            currentStatus: `Error: ${errorMessage}`
          }));

          addActivity({
            type: "error",
            content: `Error: ${errorMessage}`,
            status: "error"
          });

          onError(errorMessage);
        }
        break;
      case "connected":
        console.log('🔗 Stream connected:', data.message);
        setState(prev => ({
          ...prev,
          currentStatus: 'Connected to validation service...'
        }));
        break;
      case "status":
        console.log('📊 Status:', data.message);
        setState(prev => ({
          ...prev,
          currentStatus: data.message || 'Processing...'
        }));
        break;
      case "warning":
        console.log('⚠️ Warning:', data.message);
        setState(prev => ({
          ...prev,
          currentStatus: `⚠️ ${data.message}`
        }));
        addActivity({
          type: "status",
          content: `Warning: ${data.message}`,
          status: "warning"
        });
        break;
      case "heartbeat":
        // Ignore heartbeat messages
        break;
      default:
        console.log('🔍 Unknown message type:', data.type);
    }
  }, [addActivity, updateActivity, completeExternalTools, onAgentActivity, onComplete, onError]);





  // Start validation when component becomes visible
  useEffect(() => {
    if (isVisible && !state.isProcessing && !state.hasStartedValidation) {
      startValidation();
    }
  }, [isVisible, state.isProcessing, state.hasStartedValidation, startValidation]);

  const setSelectedAgent = useCallback((agent: string) => {
    setState(prev => ({ ...prev, selectedAgent: agent }));
    if (onAgentSelect) {
      onAgentSelect(agent);
    }
  }, [onAgentSelect]);

  const setShowAllAgents = useCallback((show: boolean) => {
    setState(prev => ({ ...prev, showAllAgents: show }));
  }, []);

  return {
    ...state,
    setSelectedAgent,
    setShowAllAgents,
    startValidation
  };
};
