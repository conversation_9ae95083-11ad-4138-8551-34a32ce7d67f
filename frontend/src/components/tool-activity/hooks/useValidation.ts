/**
 * Custom hook for validation logic and state management
 */

import { useCallback, useEffect, useState } from "react";
import { SSEMessage, ToolActivity, ValidationHookProps, ValidationState } from "../types";
import {
    buildSSEUrl,
    cleanAgentName,
    generateActivityId,
    globalValidationInProgress,
    isDuplicateActivity,
    parseSSELine,
    setGlobalValidationInProgress
} from "../utils";

export const useValidation = ({
  ideaDescription,
  answers,
  token,
  isVisible,
  externalSelectedAgent,
  onComplete,
  onError,
  onAgentActivity,
  onAgentSelect
}: ValidationHookProps) => {
  const [state, setState] = useState<ValidationState>({
    activities: [],
    currentStatus: "Initializing AI validation tools...",
    isProcessing: false,
    validationReport: null,
    selectedAgent: externalSelectedAgent || "all",
    availableAgents: [],
    hasStartedValidation: false,
    showAllAgents: false
  });

  // Sync with external selected agent
  useEffect(() => {
    if (externalSelectedAgent && externalSelectedAgent !== state.selectedAgent) {
      setState(prev => ({ ...prev, selectedAgent: externalSelectedAgent }));
    }
  }, [externalSelectedAgent, state.selectedAgent]);

  const addActivity = useCallback((activity: Omit<ToolActivity, "id" | "timestamp">) => {
    const newActivity: ToolActivity = {
      ...activity,
      id: generateActivityId(),
      timestamp: new Date()
    };

    setState(prev => {
      // Prevent duplicate activities
      if (isDuplicateActivity(activity, prev.activities)) {
        console.log('🚫 Duplicate activity detected, skipping:', activity.content);
        return prev;
      }

      console.log('✅ Adding new activity:', newActivity.type, '-', newActivity.content);
      return {
        ...prev,
        activities: [...prev.activities, newActivity]
      };
    });
  }, []);

  const updateActivity = useCallback((provider: string, updates: Partial<ToolActivity>) => {
    setState(prev => ({
      ...prev,
      activities: prev.activities.map(activity => {
        // Only update the AI processing task (first task) - leave external tools as separate cards
        if (activity.provider === provider && activity.type === "ai_processing") {
          console.log(`🔄 Updating activity for ${provider}:`, updates);
          return { ...activity, ...updates };
        }
        return activity;
      })
    }));
  }, []);

  const completeExternalTools = useCallback((provider: string) => {
    // Complete Tavily and Firecrawl tasks after AI completion
    setTimeout(() => {
      setState(prev => ({
        ...prev,
        activities: prev.activities.map(activity => {
          if (activity.provider === provider && 
              (activity.tool === "Tavily" || activity.tool === "Firecrawl")) {
            return { ...activity, status: "completed" as const };
          }
          return activity;
        })
      }));
    }, 1000);
  }, []);

  const startValidation = useCallback(async () => {
    if (state.isProcessing || state.hasStartedValidation || globalValidationInProgress) {
      console.log('🚫 Validation already in progress, skipping duplicate start');
      return;
    }

    console.log('🚀 Starting comprehensive validation...');
    setGlobalValidationInProgress(true);
    
    setState(prev => ({
      ...prev,
      isProcessing: true,
      hasStartedValidation: true,
      currentStatus: "Connecting to AI validation infrastructure..."
    }));

    try {
      // Build SSE URL
      const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      console.log('🔧 Environment API URL:', process.env.NEXT_PUBLIC_API_URL);
      console.log('🔧 Using base URL:', baseUrl);
      const url = buildSSEUrl(baseUrl, token, ideaDescription, answers, false);

      console.log('🔗 Connecting to:', url);

      // Connect to SSE stream with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log('⏰ Stream timeout after 30 seconds, aborting...');
        controller.abort();
      }, 30000); // 30 second timeout

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      clearTimeout(timeoutId);
      console.log('🔗 Infrastructure response:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No data stream available from infrastructure');
      }

      setState(prev => ({
        ...prev,
        currentStatus: "Connected! Processing validation request..."
      }));

      let chunkCount = 0;

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          console.log('🏁 Data stream completed, total chunks:', chunkCount);
          break;
        }

        chunkCount++;
        const chunk = decoder.decode(value);

        console.log(`📡 Data chunk ${chunkCount}:`, chunk.substring(0, 200) + (chunk.length > 200 ? '...' : ''));

        // Process each line in the chunk
        const lines = chunk.split('\n');
        for (const line of lines) {
          const data = parseSSELine(line);
          if (data) {
            await processSSEMessage(data);
          }
        }
      }

    } catch (error) {
      console.error('❌ Validation failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      
      setState(prev => ({
        ...prev,
        isProcessing: false,
        currentStatus: `Error: ${errorMessage}`
      }));

      addActivity({
        type: "error",
        content: `Validation failed: ${errorMessage}`,
        status: "error"
      });

      onError(errorMessage);
    } finally {
      setGlobalValidationInProgress(false);
    }
  }, [state.isProcessing, state.hasStartedValidation, token, ideaDescription, answers, addActivity, onError]);

  const processSSEMessage = useCallback(async (data: SSEMessage) => {
    console.log('📊 Processing data:', data.type, '-', data.message || data.analyst || 'N/A');

    // Handle different message types
    switch (data.type) {
      case "agent_start":
        handleAgentStart(data);
        break;
      case "agent_complete":
        handleAgentComplete(data);
        break;
      case "phase_start":
        handlePhaseStart(data);
        break;
      case "phase_complete":
        handlePhaseComplete(data);
        break;
      case "validation_complete":
        handleValidationComplete(data);
        break;
      case "final_result":
        handleFinalResult(data);
        break;
      case "error":
        handleError(data);
        break;
      case "status":
        console.log('📊 Status:', data.message);
        setState(prev => ({
          ...prev,
          currentStatus: data.message || 'Processing...'
        }));
        break;
      case "heartbeat":
        // Ignore heartbeat messages
        break;
      default:
        console.log('🔍 Unknown message type:', data.type);
    }
  }, []);

  const handleAgentStart = useCallback((data: SSEMessage) => {
    const agentName = data.agent || data.analyst || data.message || "Unknown Agent";
    const cleanName = cleanAgentName(agentName);

    console.log('🧹 Clean Agent Name:', cleanName);

    // Add agent to filter immediately when they start
    setState(prev => {
      const newAgents = prev.availableAgents.includes(cleanName) 
        ? prev.availableAgents 
        : [...prev.availableAgents, cleanName];

      return {
        ...prev,
        availableAgents: newAgents,
        currentStatus: `${agentName} is analyzing your idea...`
      };
    });

    // Add AI processing activity
    addActivity({
      type: "ai_processing",
      provider: cleanName,
      content: `${agentName} started analysis`,
      status: "processing"
    });

    // Add external tool activities
    addActivity({
      type: "tool_call",
      tool: "Tavily",
      provider: cleanName,
      content: "Searching market data and trends",
      status: "processing"
    });

    addActivity({
      type: "tool_call", 
      tool: "Firecrawl",
      provider: cleanName,
      content: "Gathering competitive intelligence",
      status: "processing"
    });

    if (onAgentActivity) {
      onAgentActivity({ type: 'agent_start', agent: cleanName });
    }
  }, [addActivity, onAgentActivity]);

  const handlePhaseStart = useCallback((data: SSEMessage) => {
    console.log(`🚀 Phase ${data.phase} started with ${data.agents?.length || 0} agents`);

    setState(prev => ({
      ...prev,
      currentStatus: `Phase ${data.phase}/${data.total_phases}: Starting ${data.agents?.join(', ') || 'agents'}...`
    }));

    // Add activities for agents in this phase
    if (data.agents) {
      data.agents.forEach((agentName: string) => {
        addActivity({
          agent: agentName,
          type: 'agent_start',
          status: 'running',
          message: `Starting ${agentName} analysis...`
        });
      });
    }
  }, [addActivity]);

  const handlePhaseComplete = useCallback((data: SSEMessage) => {
    console.log(`✅ Phase ${data.phase} completed with ${data.completed_agents} agents`);

    setState(prev => ({
      ...prev,
      currentStatus: `Phase ${data.phase} completed. Proceeding to next phase...`
    }));
  }, []);

  const handleAgentComplete = useCallback((data: SSEMessage) => {
    const agentName = data.agent || data.analyst || "Unknown Agent";
    const cleanName = cleanAgentName(agentName);

    updateActivity(cleanName, {
      content: `${agentName} completed analysis`,
      status: "completed"
    });

    completeExternalTools(cleanName);

    if (onAgentActivity) {
      onAgentActivity({ type: 'agent_complete', agent: cleanName });
    }
  }, [updateActivity, completeExternalTools, onAgentActivity]);

  const handleValidationComplete = useCallback((data: SSEMessage) => {
    console.log('🎉 Validation completed!');
    
    setState(prev => ({
      ...prev,
      isProcessing: false,
      currentStatus: "Validation completed successfully!"
    }));

    if (data.report) {
      setState(prev => ({ ...prev, validationReport: data.report! }));
      onComplete(data.report);
    }
  }, [onComplete]);

  const handleFinalResult = useCallback((data: SSEMessage) => {
    console.log('🏆 Final result received');
    
    setState(prev => ({
      ...prev,
      isProcessing: false,
      currentStatus: "Analysis complete!"
    }));

    if (data.result) {
      setState(prev => ({ ...prev, validationReport: data.result! }));
      onComplete(data.result);
    }
  }, [onComplete]);

  const handleError = useCallback((data: SSEMessage) => {
    console.error('❌ Validation error:', data.error);
    
    setState(prev => ({
      ...prev,
      isProcessing: false,
      currentStatus: `Error: ${data.error}`
    }));

    addActivity({
      type: "error",
      content: `Error: ${data.error}`,
      status: "error"
    });

    onError(data.error || 'Unknown error occurred');
  }, [addActivity, onError]);

  // Start validation when component becomes visible
  useEffect(() => {
    if (isVisible && !state.isProcessing && !state.hasStartedValidation) {
      startValidation();
    }
  }, [isVisible, state.isProcessing, state.hasStartedValidation, startValidation]);

  const setSelectedAgent = useCallback((agent: string) => {
    setState(prev => ({ ...prev, selectedAgent: agent }));
    if (onAgentSelect) {
      onAgentSelect(agent);
    }
  }, [onAgentSelect]);

  const setShowAllAgents = useCallback((show: boolean) => {
    setState(prev => ({ ...prev, showAllAgents: show }));
  }, []);

  return {
    ...state,
    setSelectedAgent,
    setShowAllAgents,
    startValidation
  };
};
