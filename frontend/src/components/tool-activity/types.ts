/**
 * Types and interfaces for tool activity components
 */

export interface ToolActivity {
  id: string;
  type: "tool_call" | "ai_processing" | "data_collection" | "analysis" | "error" | "status";
  tool?: string;
  provider?: string;
  content: string;
  timestamp: Date;
  status?: "pending" | "processing" | "completed" | "error";
}

export interface ToolActivityInterfaceProps {
  isVisible: boolean;
  ideaDescription: string;
  answers: Record<string, string>;
  token: string;
  onComplete: (report: unknown) => void;
  onError: (error: string) => void;
  onClose?: () => void;
  onAgentActivity?: (activity: Record<string, unknown>) => void;
  externalSelectedAgent?: string;
  onAgentSelect?: (agent: string) => void;
}

export interface ActivityItemProps {
  activity: ToolActivity;
  isVisible: boolean;
}

export interface AgentFilterProps {
  selectedAgent: string;
  availableAgents: string[];
  showAllAgents: boolean;
  onAgentSelect: (agent: string) => void;
  onToggleShowAll: () => void;
}

export interface ValidationState {
  activities: ToolActivity[];
  currentStatus: string;
  isProcessing: boolean;
  validationReport: Record<string, unknown> | null;
  selectedAgent: string;
  availableAgents: string[];
  hasStartedValidation: boolean;
  showAllAgents: boolean;
}

export interface SSEMessage {
  type: string;
  message?: string;
  analyst?: string;
  provider?: string;
  tool?: string;
  content?: string;
  status?: string;
  report?: Record<string, unknown>;
  result?: Record<string, unknown>;
  error?: string;
}

export interface ValidationHookProps {
  ideaDescription: string;
  answers: Record<string, string>;
  token: string;
  isVisible: boolean;
  externalSelectedAgent?: string;
  onComplete: (report: unknown) => void;
  onError: (error: string) => void;
  onAgentActivity?: (activity: Record<string, unknown>) => void;
  onAgentSelect?: (agent: string) => void;
}
