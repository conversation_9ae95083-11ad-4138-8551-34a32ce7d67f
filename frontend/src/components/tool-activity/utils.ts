/**
 * Utility functions for tool activity components
 */

import { Brain, CheckCircle, Database, Loader2, Search, XCircle, Zap } from "lucide-react";
import { ToolActivity } from "./types";

// Global validation state to prevent multiple simultaneous validations
export let globalValidationInProgress = false;

export const setGlobalValidationInProgress = (value: boolean) => {
  globalValidationInProgress = value;
};

/**
 * Get icon component for activity type
 */
export const getTypeIcon = (type: string) => {
  switch (type) {
    case "tool_call": return Search;
    case "ai_processing": return Brain;
    case "data_collection": return Database;
    case "analysis": return Zap;
    default: return Brain;
  }
};

/**
 * Get icon component for activity status
 */
export const getStatusIcon = (status?: string) => {
  switch (status) {
    case "completed": return CheckCircle;
    case "error": return XCircle;
    case "processing": return Loader2;
    default: return Loader2;
  }
};

/**
 * Get status color classes
 */
export const getStatusColor = (status?: string) => {
  switch (status) {
    case "completed": return "text-green-500";
    case "error": return "text-red-500";
    case "processing": return "text-blue-500";
    default: return "text-gray-500";
  }
};

/**
 * Get activity background color based on type
 */
export const getActivityBackground = (type: string) => {
  switch (type) {
    case "tool_call": return "bg-blue-50 border-blue-200";
    case "ai_processing": return "bg-purple-50 border-purple-200";
    case "data_collection": return "bg-green-50 border-green-200";
    case "analysis": return "bg-orange-50 border-orange-200";
    case "error": return "bg-red-50 border-red-200";
    default: return "bg-gray-50 border-gray-200";
  }
};

/**
 * Check if activity is a duplicate
 */
export const isDuplicateActivity = (
  newActivity: Omit<ToolActivity, "id" | "timestamp">,
  existingActivities: ToolActivity[]
): boolean => {
  // Only consider it duplicate if it's the exact same content from the same agent within 2 seconds
  // This allows different agents to use the same tools legitimately
  const twoSecondsAgo = Date.now() - 2000;

  return existingActivities.some(existing => {
    // Must match: content, type, tool, AND agent (all must be identical)
    const contentMatch = existing.content === newActivity.content;
    const typeMatch = existing.type === newActivity.type;
    const toolMatch = existing.tool === newActivity.tool;
    const agentMatch = existing.agent === newActivity.agent;
    const recentMatch = existing.timestamp.getTime() > twoSecondsAgo;

    // For tool_call activities, require agent match to prevent blocking legitimate parallel tool usage
    if (newActivity.type === 'tool_call') {
      return contentMatch && typeMatch && toolMatch && agentMatch && recentMatch;
    }

    // For other activities, be more lenient but still require agent match
    return contentMatch && typeMatch && agentMatch && recentMatch;
  });
};

/**
 * Generate unique activity ID
 */
export const generateActivityId = (): string => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

/**
 * Clean agent name by removing "Agent" prefix
 */
export const cleanAgentName = (agentName: string): string => {
  return agentName?.replace(/^Agent\s+/, '') || agentName;
};

/**
 * Format timestamp for display
 */
export const formatTimestamp = (timestamp: Date): string => {
  return timestamp.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};



/**
 * Build SSE URL with parameters
 */
export const buildSSEUrl = (
  baseUrl: string,
  token: string,
  ideaDescription: string,
  answers: Record<string, string>,
  useCelery: boolean = false
): string => {
  const url = new URL(`${baseUrl}/api/v1/validation/comprehensive-validation-stream`);
  url.searchParams.append('token', token);
  url.searchParams.append('idea', ideaDescription);
  url.searchParams.append('answers', JSON.stringify(answers));
  url.searchParams.append('use_celery', useCelery.toString());
  return url.toString();
};

/**
 * Parse SSE data line
 */
export const parseSSELine = (line: string): any | null => {
  if (line.startsWith('data: ') && line.trim() !== 'data: ') {
    try {
      const jsonStr = line.substring(6);
      return JSON.parse(jsonStr);
    } catch (error) {
      console.error('Failed to parse SSE line:', line, error);
      return null;
    }
  }
  return null;
};

/**
 * Get visible agents for display (with truncation)
 */
export const getVisibleAgents = (
  availableAgents: string[],
  showAllAgents: boolean,
  maxVisible: number = 4
): { visibleAgents: string[]; hasMore: boolean } => {
  const visibleAgents = showAllAgents ? availableAgents : availableAgents.slice(0, maxVisible);
  const hasMore = !showAllAgents && availableAgents.length > maxVisible;
  
  return { visibleAgents, hasMore };
};

/**
 * Scroll to bottom of activities
 */
export const scrollToBottom = (elementRef: React.RefObject<HTMLDivElement>) => {
  elementRef.current?.scrollIntoView({ behavior: "smooth" });
};
