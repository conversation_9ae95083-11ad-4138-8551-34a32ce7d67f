/**
 * Agent filter component for selecting which agent activities to display
 */

import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";
import { AgentFilterProps } from "../types";
import { getVisibleAgents } from "../utils";

export default function AgentFilter({
  selectedAgent,
  availableAgents,
  showAllAgents,
  onAgentSelect,
  onToggleShowAll
}: AgentFilterProps) {
  if (availableAgents.length === 0) {
    return null;
  }

  const { visibleAgents, hasMore } = getVisibleAgents(availableAgents, showAllAgents, 4);

  return (
    <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
      <div className="flex items-center gap-2 mb-2">
        <Eye className="w-4 h-4 text-gray-600" />
        <span className="text-sm font-medium text-gray-700">Filter by Agent:</span>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {/* All Agents Button */}
        <Button
          variant={selectedAgent === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => onAgentSelect("all")}
          className="text-xs"
        >
          All Agents ({availableAgents.length})
        </Button>

        {/* Individual Agent Buttons */}
        {visibleAgents.map((agent) => (
          <Button
            key={agent}
            variant={selectedAgent === agent ? "default" : "outline"}
            size="sm"
            onClick={() => onAgentSelect(agent)}
            className="text-xs"
          >
            {agent}
          </Button>
        ))}

        {/* Show More/Less Button */}
        {hasMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleShowAll}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            +{availableAgents.length - 4} more
          </Button>
        )}

        {showAllAgents && availableAgents.length > 4 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleShowAll}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            Show less
          </Button>
        )}
      </div>

      {/* Selected Agent Info */}
      {selectedAgent !== "all" && (
        <div className="mt-2 text-xs text-gray-600">
          Showing activities from: <span className="font-medium">{selectedAgent}</span>
        </div>
      )}
    </div>
  );
}
