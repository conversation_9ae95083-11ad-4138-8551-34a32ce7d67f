/**
 * Activity item component for displaying individual tool activities
 */

import { ActivityItemProps } from "../types";
import { formatTimestamp, getActivityBackground, getStatusColor, getStatusIcon, getTypeIcon } from "../utils";

export default function ActivityItem({ activity, isVisible, onActivityClick }: ActivityItemProps) {
  const TypeIcon = getTypeIcon(activity.type);
  const StatusIcon = getStatusIcon(activity.status);
  const statusColor = getStatusColor(activity.status);
  const backgroundClass = getActivityBackground(activity.type);

  if (!isVisible) return null;

  const handleClick = () => {
    if (onActivityClick) {
      onActivityClick(activity);
    }
  };

  return (
    <div
      className={`p-2 rounded border ${backgroundClass} transition-all duration-200 hover:shadow-sm cursor-pointer hover:bg-blue-50 hover:border-blue-200`}
      onClick={handleClick}
    >
      <div className="flex items-start gap-2">
        {/* Type Icon */}
        <div className="flex-shrink-0 mt-0.5">
          <TypeIcon className="w-3 h-3 text-gray-600" />
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            {/* Provider/Tool Info */}
            <div className="flex items-center gap-1 text-xs">
              {activity.provider && (
                <span className="font-medium text-gray-900">
                  {activity.provider}
                </span>
              )}
              {activity.tool && (
                <span className="text-gray-500">
                  • {activity.tool}
                </span>
              )}
            </div>

            {/* Status and Timestamp */}
            <div className="flex items-center gap-1 text-xs text-gray-500">
              <span>{formatTimestamp(activity.timestamp)}</span>
              <StatusIcon className={`w-3 h-3 ${statusColor}`} />
            </div>
          </div>

          {/* Activity Content */}
          <p className="text-xs text-gray-700 leading-snug">
            {activity.content}
          </p>

          {/* Activity Type Badge */}
          <div className="mt-1">
            <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
              {activity.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
