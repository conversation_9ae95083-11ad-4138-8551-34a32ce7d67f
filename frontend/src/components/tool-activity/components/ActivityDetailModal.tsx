/**
 * Activity Detail Modal - Shows real-time streaming details for a selected activity
 */

import { useEffect, useState, useRef } from "react";
import { X, Loader2, Play, Pause, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ToolActivity } from "../types";
import { formatTimestamp, getStatusColor, getStatusIcon, getTypeIcon } from "../utils";

interface ActivityDetailModalProps {
  activity: ToolActivity | null;
  isVisible: boolean;
  onClose: () => void;
}

interface StreamingUpdate {
  id: string;
  timestamp: Date;
  type: "progress" | "data" | "result" | "error";
  content: string;
  metadata?: Record<string, unknown>;
}

export default function ActivityDetailModal({ activity, isVisible, onClose }: ActivityDetailModalProps) {
  const [streamingUpdates, setStreamingUpdates] = useState<StreamingUpdate[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingStatus, setStreamingStatus] = useState("Initializing...");
  const updatesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when updates change
  useEffect(() => {
    if (updatesEndRef.current) {
      updatesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [streamingUpdates]);

  // Start streaming when activity changes
  useEffect(() => {
    if (activity && isVisible) {
      startActivityStreaming();
    } else {
      stopActivityStreaming();
    }

    return () => stopActivityStreaming();
  }, [activity, isVisible]);

  const startActivityStreaming = () => {
    if (!activity) return;

    setIsStreaming(true);
    setStreamingUpdates([]);
    setStreamingStatus("Connecting to activity stream...");

    // Simulate real-time streaming updates for the activity
    simulateActivityStreaming();
  };

  const stopActivityStreaming = () => {
    setIsStreaming(false);
    setStreamingStatus("Stream stopped");
  };

  const simulateActivityStreaming = () => {
    if (!activity) return;

    // Add initial update
    addStreamingUpdate({
      type: "progress",
      content: `Starting ${activity.type} for ${activity.provider || 'system'}...`
    });

    // Simulate different types of updates based on activity type
    const updateSequence = getUpdateSequenceForActivity(activity);
    
    updateSequence.forEach((update, index) => {
      setTimeout(() => {
        addStreamingUpdate(update);
        
        if (index === updateSequence.length - 1) {
          setIsStreaming(false);
          setStreamingStatus("Stream completed");
        }
      }, (index + 1) * 1500); // 1.5 second intervals
    });
  };

  const getUpdateSequenceForActivity = (activity: ToolActivity): Omit<StreamingUpdate, 'id' | 'timestamp'>[] => {
    switch (activity.type) {
      case "tool_call":
        return [
          { type: "progress", content: `Initializing ${activity.tool || 'external tool'} connection...` },
          { type: "data", content: `Sending query: "${activity.content}"` },
          { type: "progress", content: "Processing external API response..." },
          { type: "data", content: "Received 247 data points from external source" },
          { type: "result", content: "Successfully extracted relevant insights" }
        ];
      
      case "ai_processing":
        return [
          { type: "progress", content: "Loading AI model and context..." },
          { type: "data", content: "Processing input tokens: 1,247" },
          { type: "progress", content: "Generating analysis using advanced reasoning..." },
          { type: "data", content: "Generated response tokens: 892" },
          { type: "result", content: "AI analysis completed with high confidence score" }
        ];
      
      case "data_collection":
        return [
          { type: "progress", content: "Scanning available data sources..." },
          { type: "data", content: "Found 12 relevant data sources" },
          { type: "progress", content: "Extracting and normalizing data..." },
          { type: "data", content: "Processed 3,456 data records" },
          { type: "result", content: "Data collection completed successfully" }
        ];
      
      case "analysis":
        return [
          { type: "progress", content: "Preparing analysis framework..." },
          { type: "data", content: "Applying statistical models and algorithms" },
          { type: "progress", content: "Cross-referencing with market data..." },
          { type: "data", content: "Identified 8 key insights and 3 risk factors" },
          { type: "result", content: "Comprehensive analysis report generated" }
        ];
      
      default:
        return [
          { type: "progress", content: "Processing task..." },
          { type: "data", content: "Executing workflow steps" },
          { type: "result", content: "Task completed successfully" }
        ];
    }
  };

  const addStreamingUpdate = (update: Omit<StreamingUpdate, 'id' | 'timestamp'>) => {
    const newUpdate: StreamingUpdate = {
      ...update,
      id: `update-${Date.now()}-${Math.random()}`,
      timestamp: new Date()
    };

    setStreamingUpdates(prev => [...prev, newUpdate]);
  };

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case "progress": return Loader2;
      case "data": return Play;
      case "result": return CheckCircle;
      case "error": return AlertCircle;
      default: return Play;
    }
  };

  const getUpdateColor = (type: string) => {
    switch (type) {
      case "progress": return "text-blue-600";
      case "data": return "text-gray-600";
      case "result": return "text-green-600";
      case "error": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  if (!isVisible || !activity) return null;

  const TypeIcon = getTypeIcon(activity.type);
  const StatusIcon = getStatusIcon(activity.status);
  const statusColor = getStatusColor(activity.status);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-[#4F46E5] to-purple-600 text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <TypeIcon className="w-5 h-5" />
              <div>
                <h2 className="text-lg font-semibold">Activity Details</h2>
                <p className="text-sm text-indigo-100">
                  {activity.provider && `${activity.provider} • `}
                  {activity.tool && `${activity.tool} • `}
                  {formatTimestamp(activity.timestamp)}
                </p>
              </div>
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onClose}
              className="text-white hover:bg-white/20"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Activity Summary */}
        <div className="border-b bg-gray-50 p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-gray-700">Status:</span>
              <div className="flex items-center gap-1">
                <StatusIcon className={`w-4 h-4 ${statusColor}`} />
                <span className={`text-sm font-medium ${statusColor}`}>
                  {activity.status?.charAt(0).toUpperCase() + activity.status?.slice(1)}
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Stream:</span>
              <span className={isStreaming ? "text-blue-600" : "text-gray-500"}>
                {streamingStatus}
              </span>
            </div>
          </div>
          <p className="text-sm text-gray-700">{activity.content}</p>
        </div>

        {/* Streaming Updates */}
        <div className="flex-1 overflow-y-auto p-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Real-time Activity Stream</h3>
          
          {streamingUpdates.length === 0 ? (
            <div className="text-center py-8">
              <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-blue-500" />
              <p className="text-sm text-gray-500">Waiting for streaming updates...</p>
            </div>
          ) : (
            <div className="space-y-3">
              {streamingUpdates.map((update) => {
                const UpdateIcon = getUpdateIcon(update.type);
                const updateColor = getUpdateColor(update.type);
                
                return (
                  <div key={update.id} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 mt-0.5">
                      <UpdateIcon className={`w-4 h-4 ${updateColor} ${update.type === 'progress' ? 'animate-spin' : ''}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className={`text-xs font-medium ${updateColor}`}>
                          {update.type.toUpperCase()}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(update.timestamp)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700">{update.content}</p>
                    </div>
                  </div>
                );
              })}
              <div ref={updatesEndRef} />
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t bg-gray-50 p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {streamingUpdates.length} updates received
            </div>
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
