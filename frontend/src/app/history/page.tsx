"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Eye, Calendar, TrendingUp, Brain, LogOut } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import ProtectedRoute from "@/components/ProtectedRoute";
import { useAuthStore } from "@/store/authStore";
import { apiClient } from "@/lib/api";

interface ValidationReportSummary {
  id: string;
  title: string;
  description: string;
  created_at: string;
  report_data: {
    validation_summary?: {
      overall_score?: number;
      recommendation?: string;
    };
  };
}

function HistoryContent() {
  const { user, logout, token } = useAuthStore();
  const router = useRouter();
  const [reports, setReports] = useState<ValidationReportSummary[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadReports = async () => {
      if (!token) {
        setLoading(false);
        return;
      }

      try {
        // Try to fetch from database first
        const dbReports = await apiClient.getUserValidationReports(token);
        setReports(dbReports);
        console.log('✅ Loaded reports from database:', dbReports.length);
        
      } catch (error) {
        console.warn('Failed to load from database, using localStorage:', error);
        
        // Fallback to localStorage
        const savedReports = JSON.parse(localStorage.getItem('validation_reports') || '[]');
        const transformedReports = savedReports.map((report: any) => ({
          id: report.id,
          title: report.ideaTitle,
          description: report.ideaDescription,
          created_at: report.createdAt,
          report_data: {
            validation_summary: {
              overall_score: report.report?.validation_summary?.overall_score || 0,
              recommendation: report.report?.validation_summary?.recommendation || "Assessment Complete"
            }
          }
        }));
        setReports(transformedReports);
        console.log('✅ Loaded reports from localStorage:', transformedReports.length);
      }
      
      setLoading(false);
    };
    
    loadReports();
  }, [token]);

  const handleLogout = () => {
    logout();
  };

  const getScoreColor = (score: number) => {
    if (score >= 8) return "text-green-600 bg-green-100";
    if (score >= 6) return "text-yellow-600 bg-yellow-100";
    if (score >= 4) return "text-orange-600 bg-orange-100";
    return "text-red-600 bg-red-100";
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading validation history...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center">
              <img src="/logo-site.png" alt="Incepta" className="h-8 w-auto" />
            </Link>
            
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                Welcome, {user?.full_name || user?.email}
              </span>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <LogOut className="w-4 h-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="outline"
              onClick={() => router.push('/validate-idea')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Validate Ideas
            </Button>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Your Validation History
          </h1>
          <p className="text-xl text-gray-600">
            Track and review all your validated ideas in one place.
          </p>
        </div>

        {/* Reports Grid */}
        {reports.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🚀</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">No validations yet</h2>
            <p className="text-gray-600 mb-6">
              Start validating your ideas to see your history here.
            </p>
            <Button
              onClick={() => router.push('/validate-idea')}
              className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
            >
              <Brain className="w-5 h-5 mr-2" />
              Validate Your First Idea
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {reports.map((report) => {
              const score = report.report_data?.validation_summary?.overall_score || 0;
              const recommendation = report.report_data?.validation_summary?.recommendation || "Assessment Complete";
              
              return (
                <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-purple-600 transition-colors">
                          {report.title}
                        </CardTitle>
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {report.description}
                        </p>
                      </div>
                      <div className={`ml-4 px-3 py-1 rounded-full text-sm font-semibold ${getScoreColor(score)}`}>
                        {score}/10
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Calendar className="w-4 h-4" />
                        {formatDate(report.created_at)}
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <TrendingUp className="w-4 h-4" />
                        {recommendation}
                      </div>
                      
                      <div className="pt-2">
                        <Button
                          onClick={() => router.push(`/reports/${report.id}`)}
                          variant="outline"
                          size="sm"
                          className="w-full group-hover:bg-purple-50 group-hover:border-purple-300 transition-colors"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          View Report
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {/* Quick Actions */}
        {reports.length > 0 && (
          <div className="mt-12 text-center">
            <Button
              onClick={() => router.push('/validate-idea')}
              size="lg"
              className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-medium px-8 py-3"
            >
              <Brain className="w-5 h-5 mr-2" />
              Validate Another Idea
            </Button>
          </div>
        )}
      </main>
    </div>
  );
}

export default function HistoryPage() {
  return (
    <ProtectedRoute>
      <HistoryContent />
    </ProtectedRoute>
  );
}