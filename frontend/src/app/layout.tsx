import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Incepta - AI-Powered Idea Validation Platform",
  description: "Validate your startup ideas with comprehensive AI analysis, market research, and competitive intelligence.",
  openGraph: {
    title: "Incepta - AI-Powered Idea Validation",
    description: "Validate your startup ideas with comprehensive AI analysis",
    url: "https://incepta.ai",
    siteName: "Incepta",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "Incepta - AI-Powered Idea Validation Platform",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Incepta - AI-Powered Idea Validation",
    description: "Validate your startup ideas with comprehensive AI analysis",
    images: ["/og-image.png"],
  },
  robots: "index, follow",
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/icon.ico" type="image/x-icon" />
        <link rel="apple-touch-icon" href="/Incepta.png" />
        <meta name="theme-color" content="#4F46E5" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
