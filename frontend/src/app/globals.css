@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Hide scrollbars for horizontal scrolling tabs */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 249 46% 62%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 249 46% 62%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 249 46% 62%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 249 46% 62%;
  }
}

@layer base {
  *,
  *::before,
  *::after {
    box-sizing: border-box;
  }
  body {
    @apply bg-white text-gray-900 font-inter antialiased;
  }
  .dark body {
    @apply bg-gray-900 text-gray-100;
  }
}

.glass-card {
  @apply bg-white/80 backdrop-blur-lg border border-white/20 shadow-xl;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

.fade-in {
  animation: fadeIn 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* New colorful UI elements */
.gradient-card {
  @apply relative overflow-hidden rounded-xl border border-primary/10 bg-gradient-to-br from-white to-primary-50 shadow-md transition-all hover:shadow-lg dark:from-gray-900 dark:to-gray-950 dark:border-primary/20;
}

.gradient-purple {
  @apply bg-gradient-to-r from-violet-500 to-purple-500;
}

.gradient-blue {
  @apply bg-gradient-to-r from-blue-400 to-indigo-500;
}

.gradient-green {
  @apply bg-gradient-to-r from-green-400 to-emerald-500;
}

.gradient-amber {
  @apply bg-gradient-to-r from-amber-400 to-orange-500;
}

.gradient-rose {
  @apply bg-gradient-to-r from-rose-400 to-pink-500;
}

.gradient-text {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-primary-500 to-primary-700;
}

.icon-bg {
  @apply flex items-center justify-center w-10 h-10 rounded-full p-2 transition-transform hover:scale-110;
}

.animated-bg {
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.hover-lift {
  @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
}

.section-title {
  @apply text-2xl font-bold mb-6 gradient-text;
}

.page-header {
  @apply pb-6 mb-6 border-b border-primary/10;
}

/* Phase tags */
.phase-badge {
  @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold;
}

.phase-interactive {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300;
}

.phase-poc {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300;
}

.phase-mvp {
  @apply bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300;
}

.phase-production {
  @apply bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}