"use client";

import DynamicQuestionForm from "@/components/DynamicQuestionForm";
import ProtectedRoute from "@/components/ProtectedRoute";
import ToolActivityInterface from "@/components/ToolActivityInterface";
import { Button } from "@/components/ui/button";
import { apiClient } from "@/lib/api";
import { useAuthStore } from "@/store/authStore";
import { Brain, History, LogOut } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface Question {
  id: string;
  question: string;
  type: "radio" | "dropdown" | "textarea" | "text";
  options?: string[];
  placeholder?: string;
  required: boolean;
}

function ValidateIdeaContent() {
  const { user, logout, token } = useAuthStore();
  const [idea, setIdea] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [showQuestions, setShowQuestions] = useState(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isSubmittingAnswers, setIsSubmittingAnswers] = useState(false);
  const [showStreaming, setShowStreaming] = useState(false);
  const [validationReport, setValidationReport] = useState<any>(null);
  const [agentActivities, setAgentActivities] = useState<any[]>([]);
  const [validationStatus, setValidationStatus] = useState<string>("Starting validation...");
  const [selectedToolAgent, setSelectedToolAgent] = useState<string>("all");

  const handleLogout = () => {
    logout();
  };

  const handleValidateIdea = async () => {
    if (!idea.trim()) {
      alert("Please describe your idea first!");
      return;
    }

    if (!token) {
      alert("Authentication required!");
      return;
    }

    setIsValidating(true);
    
    try {
      const response = await apiClient.generateQuestions(idea, token) as { questions: Question[] };
      setQuestions(response.questions);
      setShowQuestions(true);
      setIsValidating(false);
    } catch (error) {
      console.error("Error generating questions:", error);
      alert("Failed to generate questions. Please try again.");
      setIsValidating(false);
    }
  };

  const handleQuestionSubmit = async (answers: Record<string, string>) => {
    if (!token) {
      alert("Authentication required!");
      return;
    }

    setIsSubmittingAnswers(true);
    setShowStreaming(true);
    
    // Store answers and reset state
    setValidationReport({ answers });
    setAgentActivities([]);
    setValidationStatus("Assembling AI agent team...");
  };

  const handleAgentActivity = (activity: any) => {
    if (activity.type === 'agent_start') {
      // Add new agent or update existing one
      setAgentActivities(prev => {
        const existingIndex = prev.findIndex(agent => agent.agent === activity.agent);
        if (existingIndex >= 0) {
          // Update existing agent
          const updated = [...prev];
          updated[existingIndex] = {
            ...updated[existingIndex],
            agent: activity.agent,
            title: activity.title,
            task: activity.task,
            background: activity.background,
            icon: activity.icon,
            status: 'processing'
          };
          return updated;
        } else {
          // Add new agent
          return [...prev, {
            agent: activity.agent,
            title: activity.title,
            task: activity.task,
            background: activity.background,
            icon: activity.icon,
            status: 'processing'
          }];
        }
      });
      setValidationStatus(`${activity.agent} starting analysis...`);
    } else if (activity.type === 'agent_complete') {
      // Update the same agent card to completed status
      setAgentActivities(prev => prev.map(agent => 
        agent.agent === activity.agent 
          ? { ...agent, status: 'completed', completedAt: new Date() }
          : agent
      ));
      setValidationStatus(`${activity.agent} completed analysis`);
    }
  };

  const handleStreamingComplete = async (report: Record<string, unknown>) => {
    console.log('📊 Received validation report:', report);
    
    if (!token) {
      console.error('No auth token available for saving report');
      return;
    }
    
    try {
      // Prepare report data for database
      const reportData = {
        title: idea.substring(0, 100) + (idea.length > 100 ? '...' : ''),
        description: idea,
        user_answers: (validationReport as { answers?: Record<string, string> })?.answers || {},
        report_data: report,
        agent_activities: agentActivities,
        processing_metadata: {
          completed_at: new Date().toISOString(),
          agents_count: agentActivities.length,
          idea_length: idea.length
        }
      };

      console.log('🔍 DEBUG: Attempting to save report to database...');
      console.log('🔍 DEBUG: Token exists:', !!token);
      console.log('🔍 DEBUG: Report data keys:', Object.keys(reportData));
      console.log('🔍 DEBUG: Report data size:', JSON.stringify(reportData).length);

      // Save to database via API
      const savedReport = await apiClient.createValidationReport(reportData, token);
      console.log('✅ Report saved to database:', savedReport.id);

      setValidationReport(report);
      setShowStreaming(false);
      setIsSubmittingAnswers(false);

      // Navigate to full results page
      window.location.href = `/reports/${savedReport.id}`;

    } catch (error) {
      console.error('❌ Failed to save report to database:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        token: !!token,
        reportDataSize: JSON.stringify(reportData).length
      });
      
      // Fallback to localStorage if database save fails
      const reportId = Date.now().toString();
      const fallbackData = {
        id: reportId,
        ideaTitle: idea.substring(0, 100) + (idea.length > 100 ? '...' : ''),
        ideaDescription: idea,
        userAnswers: (validationReport as { answers?: Record<string, string> })?.answers || {},
        report: report,
        createdAt: new Date().toISOString()
      };
      
      const savedReports = JSON.parse(localStorage.getItem('validation_reports') || '[]');
      savedReports.unshift(fallbackData);
      savedReports.splice(10);
      localStorage.setItem('validation_reports', JSON.stringify(savedReports));
      
      setValidationReport(report);
      setShowStreaming(false);
      setIsSubmittingAnswers(false);
      
      // Navigate to full results page with localStorage fallback
      window.location.href = `/reports/${reportId}`;
    }
  };

  const handleStreamingError = (error: string) => {
    setShowStreaming(false);
    setIsSubmittingAnswers(false);
    alert(`Validation failed: ${error}`);
  };

  const handleCloseStreaming = () => {
    setShowStreaming(false);
    setIsSubmittingAnswers(false);
  };

  const handleBackToIdea = () => {
    setShowQuestions(false);
    setQuestions([]);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link href="/" className="flex items-center">
              <img src="/logo-site.png" alt="Incepta" className="h-8 w-auto" />
            </Link>
            
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Welcome, {user?.full_name || user?.email}
              </span>
              <Link href="/history">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                >
                  <History className="w-4 h-4" />
                  History
                </Button>
              </Link>
              <Button
                onClick={handleLogout}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <LogOut className="w-4 h-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Validate Your Idea
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Welcome to the idea validation dashboard. Start building your next successful project with AI-powered insights.
          </p>
        </div>

        {/* Split Screen Layout when streaming, otherwise normal flow */}
        {showStreaming ? (
          /* 50/50 Split Screen Layout - Fixed Height */
          <div className="fixed inset-0 top-24 left-0 right-0 bottom-0 bg-gray-100 z-40">
            <div className="h-full grid grid-cols-2 gap-4 p-4">
              {/* Left side - Your Idea & Validation Progress */}
              <div className="bg-white rounded-lg border border-gray-200 shadow-lg flex flex-col h-full">
                <div className="p-4 border-b border-gray-200 flex-shrink-0">
                  <h2 className="text-lg font-bold text-gray-900">Your Idea & Validation Progress</h2>
                </div>
                <div 
                  className="flex-1 overflow-y-scroll p-4 space-y-4 custom-scrollbar"
                  style={{
                    maxHeight: 'calc(100vh - 180px)',
                    minHeight: '400px'
                  }}
                >
                  {/* Show the original idea */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-2">💡 Original Idea:</h3>
                    <div className="p-3 bg-blue-50 rounded-lg text-sm text-gray-800 border border-blue-200">
                      {idea}
                    </div>
                  </div>
                  
                  {/* Show the answered questions */}
                  {questions.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-3">📝 Your Context:</h3>
                      <div className="space-y-2">
                        {questions.map((question: unknown) => {
                          const q = question as Question;
                          const answer = (validationReport as { answers?: Record<string, string> })?.answers?.[q.id];
                          return (
                            <div key={q.id} className="p-2 bg-gray-50 rounded-lg border">
                              <div className="text-xs font-medium text-gray-600 mb-1">{q.question}</div>
                              <div className="text-sm text-gray-800 font-medium">{answer || 'No answer provided'}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Agent Activities - Real-time */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-700 mb-3">🤖 Agent Activities:</h3>
                    <div className="space-y-2">
                      {agentActivities.length === 0 ? (
                        <div className="flex items-center p-2 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="w-3 h-3 bg-blue-500 rounded-full mr-3 animate-pulse"></div>
                          <span className="text-sm text-blue-800">{validationStatus}</span>
                        </div>
                      ) : (
                        agentActivities.map((activity, index) => (
                          <div 
                            key={index} 
                            className={`group p-2 rounded-lg border text-xs cursor-pointer transition-all hover:shadow-md hover:scale-[1.02] ${
                              activity.status === 'completed' 
                                ? 'bg-green-50 border-green-200 hover:bg-green-100' 
                                : activity.status === 'error'
                                ? 'bg-red-50 border-red-200 hover:bg-red-100'
                                : 'bg-blue-50 border-blue-200 hover:bg-blue-100'
                            }`}
                            onClick={() => {
                              // Extract agent name and set it as selected in tools section
                              const agentName = activity.agent.replace(/^Agent\s+/, '');
                              setSelectedToolAgent(agentName);
                            }}
                          >
                            <div className="flex items-center mb-1">
                              <span className="mr-2">{activity.icon}</span>
                              <span className="font-medium text-gray-800">{activity.agent}</span>
                              {activity.status === 'processing' && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 animate-pulse"></div>
                              )}
                              {activity.status === 'completed' && (
                                <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                              )}
                            </div>
                            <div className="text-gray-600">{activity.title}</div>
                            <div className="text-gray-500 mt-1">{activity.task}</div>
                            {activity.background && (
                              <div className="text-gray-400 text-xs mt-1 italic">{activity.background}</div>
                            )}
                            <div className="text-xs text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              Click to view tasks in AI Infrastructure & Tools →
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>

                </div>
              </div>
              
              {/* Right side - AI Infrastructure & Tools Progress */}
              <div className="bg-white rounded-lg border border-gray-200 shadow-lg h-full">
                <ToolActivityInterface
                  isVisible={showStreaming}
                  ideaDescription={idea}
                  answers={(validationReport as { answers?: Record<string, string> })?.answers || {}}
                  token={token || ""}
                  onComplete={handleStreamingComplete}
                  onError={handleStreamingError}
                  onClose={handleCloseStreaming}
                  onAgentActivity={handleAgentActivity}
                  externalSelectedAgent={selectedToolAgent}
                  onAgentSelect={setSelectedToolAgent}
                />
              </div>
            </div>
          </div>
        ) : (
          /* Normal Flow - Conditional rendering based on current step */
          <>
            {!showQuestions ? (
              /* Idea Input Section */
              <div className="bg-white rounded-2xl p-8 border border-gray-200 shadow-lg mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Describe Your Idea</h2>
                <div className="space-y-6">
                  <div>
                    <label htmlFor="idea" className="block text-sm font-medium text-gray-700 mb-2">
                      What&apos;s your idea? Describe it in detail...
                    </label>
                    <textarea
                      id="idea"
                      rows={6}
                      value={idea}
                      onChange={(e) => setIdea(e.target.value)}
                      className="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 resize-none"
                      placeholder="Describe your idea, what problem it solves, and who it's for. The more details you provide, the better our AI can analyze it..."
                    />
                  </div>
                  <div className="flex justify-end">
                    <Button 
                      size="lg" 
                      onClick={handleValidateIdea}
                      disabled={isValidating}
                      className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white font-medium px-8 py-3"
                    >
                      <Brain className="w-5 h-5 mr-2" />
                      {isValidating ? "Analyzing..." : "Validate Idea"}
                    </Button>
                  </div>
                </div>
              </div>
            ) : (
              /* Dynamic Questions Form */
              <DynamicQuestionForm
                questions={questions}
                onSubmit={handleQuestionSubmit}
                onBack={handleBackToIdea}
                isSubmitting={isSubmittingAnswers}
              />
            )}
          </>
        )}
      </main>

    </div>
  );
}

export default function ValidateIdeaPage() {
  return (
    <ProtectedRoute>
      <ValidateIdeaContent />
    </ProtectedRoute>
  );
}