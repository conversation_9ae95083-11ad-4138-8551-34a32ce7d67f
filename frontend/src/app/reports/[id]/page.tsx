/**
 * Reports page - Re-export from modular structure
 * This maintains backward compatibility while using the new modular architecture
 */

"use client";

import { useParams } from "next/navigation";
import { ReportPage } from "@/components/reports";

export default function ReportPageWrapper() {
  const params = useParams();
  const reportId = params.id as string;

  return <ReportPage reportId={reportId} />;
}
