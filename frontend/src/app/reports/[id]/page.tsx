"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON>Left, CheckCircle, Clock, TrendingUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { apiClient } from "@/lib/api";

interface ValidationReport {
  id: string;
  title: string;
  description: string;
  user_answers: Record<string, string>;
  report_data: any;
  created_at: string;
}

// Tab configuration matching proto design exactly
const tabs = [
  { id: "executive", label: "Executive Summary" },
  { id: "concept", label: "Project Concept" },
  { id: "users", label: "Target Users" },
  { id: "features", label: "Features" },
  { id: "market", label: "Market Analysis" },
  { id: "competitors", label: "Competitors" },
  { id: "business", label: "Business Model" },
  { id: "challenges", label: "Challenges & Opportunities" },
  { id: "financial", label: "Financial" },
  { id: "implementation", label: "Implementation" },
  { id: "sources", label: "Sources" },
  { id: "next", label: "Next Steps" },
];

// Helper function to safely render content
const safeRender = (content: any): string => {
  if (typeof content === 'string') return content;
  if (typeof content === 'number') return content.toString();
  if (typeof content === 'boolean') return content.toString();
  if (content === null || content === undefined) return '';
  if (Array.isArray(content)) return content.map(item => safeRender(item)).join(', ');
  if (typeof content === 'object') return JSON.stringify(content);
  return String(content);
};

function ReportPageContent() {
  const params = useParams();
  const router = useRouter();
  const [report, setReport] = useState<ValidationReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("executive");

  useEffect(() => {
    const loadReport = async () => {
      const reportId = params.id as string;
      
      try {
        // Try to fetch from database first
        const dbReport = await apiClient.getValidationReport(reportId);
        setReport(dbReport);
        console.log('✅ Loaded report from database:', reportId);
        
      } catch (error) {
        console.warn('Failed to load from database, trying localStorage:', error);
        
        // Fallback to localStorage
        const savedReports = JSON.parse(localStorage.getItem('validation_reports') || '[]');
        const foundReport = savedReports.find((r: any) => r.id === reportId);
        
        if (foundReport) {
          // Transform localStorage format to match database format
          const transformedReport = {
            id: foundReport.id,
            title: foundReport.ideaTitle,
            description: foundReport.ideaDescription,
            user_answers: foundReport.userAnswers,
            report_data: foundReport.report,
            created_at: foundReport.createdAt
          };
          setReport(transformedReport);
          console.log('✅ Loaded report from localStorage:', reportId);
        } else {
          console.error('❌ Report not found in database or localStorage:', reportId);
        }
      }
      
      setLoading(false);
    };
    
    loadReport();
  }, [params.id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading validation report...</p>
        </div>
      </div>
    );
  }

  if (!report) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="text-6xl mb-4">📊</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Report Not Found</h1>
          <p className="text-gray-600 mb-6">The validation report you're looking for could not be found.</p>
          <Button onClick={() => router.push('/validate-idea')} className="bg-purple-600 hover:bg-purple-700">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Validate Ideas
          </Button>
        </div>
      </div>
    );
  }

  const reportData = report.report_data;
  
  // Debug logging
  console.log('🔍 REPORT DEBUG - Full report:', report);
  console.log('🔍 REPORT DEBUG - Report data:', reportData);
  console.log('🔍 REPORT DEBUG - Available keys:', Object.keys(reportData || {}));
  console.log('🔍 REPORT DEBUG - Validation summary:', reportData?.validation_summary);
  console.log('🔍 REPORT DEBUG - Project concept:', reportData?.project_concept);
  console.log('🔍 REPORT DEBUG - Market analysis:', reportData?.market_analysis);
  
  const score = reportData?.validation_summary?.overall_score || 0;
  const recommendation = reportData?.validation_summary?.recommendation || "Assessment Complete";

  const getScoreColor = (score: number) => {
    if (score >= 8) return "bg-green-500";
    if (score >= 6) return "bg-yellow-500";
    if (score >= 4) return "bg-orange-500";
    return "bg-red-500";
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "executive":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Executive Summary</h2>
            <div className="space-y-4">
              <p className="text-gray-700 leading-relaxed">
                {safeRender(reportData?.executive_summary) || "Comprehensive analysis completed using multi-analyst approach with market research, financial modeling, technical assessment, and risk evaluation."}
              </p>
              
              {reportData?.validation_summary?.key_strengths && (
                <div>
                  <h3 className="text-lg font-semibold text-green-800 mb-2 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Key Strengths
                  </h3>
                  <ul className="space-y-1">
                    {reportData.validation_summary.key_strengths.map((strength: any, index: number) => (
                      <li key={index} className="text-gray-700">• {safeRender(strength)}</li>
                    ))}
                  </ul>
                </div>
              )}

              {reportData?.validation_summary?.key_concerns && (
                <div>
                  <h3 className="text-lg font-semibold text-orange-800 mb-2">Key Concerns</h3>
                  <ul className="space-y-1">
                    {reportData.validation_summary.key_concerns.map((concern: any, index: number) => (
                      <li key={index} className="text-gray-700">• {safeRender(concern)}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        );

      case "concept":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Project Concept</h2>
            {reportData?.project_concept ? (
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">💡 Problem Statement & Solution</h3>
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-gray-900">Problem Statement</h4>
                      <p className="text-gray-700">{safeRender(reportData.project_concept.problem_statement)}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Solution Overview</h4>
                      <p className="text-gray-700">{safeRender(reportData.project_concept.solution_overview)}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Value Proposition</h4>
                      <p className="text-gray-700">{safeRender(reportData.project_concept.value_proposition)}</p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-gray-600">Project concept analysis not available in this report.</p>
            )}
          </div>
        );

      case "users":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Target Users & Market</h2>
            {reportData?.target_users ? (
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">👥 User Personas & Segmentation</h3>
                  {reportData.target_users.primary_personas && (
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-semibold text-gray-900">Primary Personas</h4>
                        {Array.isArray(reportData.target_users.primary_personas) ? 
                          <ul className="space-y-1">
                            {reportData.target_users.primary_personas.map((persona: any, index: number) => (
                              <li key={index} className="text-gray-700">• {safeRender(persona)}</li>
                            ))}
                          </ul> : 
                          <p className="text-gray-700">{safeRender(reportData.target_users.primary_personas)}</p>
                        }
                      </div>
                      {reportData.target_users.user_needs && (
                        <div>
                          <h4 className="font-semibold text-gray-900">User Needs</h4>
                          {Array.isArray(reportData.target_users.user_needs) ?
                            <ul className="space-y-1">
                              {reportData.target_users.user_needs.map((need: any, index: number) => (
                                <li key={index} className="text-gray-700">• {safeRender(need)}</li>
                              ))}
                            </ul> :
                            <p className="text-gray-700">{safeRender(reportData.target_users.user_needs)}</p>
                          }
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <p className="text-gray-600">Target user analysis not available in this report.</p>
            )}
          </div>
        );

      case "features":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Features</h2>
            {reportData?.features ? (
              <div className="space-y-4">
                {reportData.features.core_features && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🎯 Core Features</h3>
                    <ul className="space-y-1">
                      {reportData.features.core_features.map((feature: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(feature)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.features.must_have_features && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">⭐ Must-Have Features</h3>
                    <ul className="space-y-1">
                      {reportData.features.must_have_features.map((feature: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(feature)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.features.nice_to_have_features && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">💡 Nice-to-Have Features</h3>
                    <ul className="space-y-1">
                      {reportData.features.nice_to_have_features.map((feature: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(feature)}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Feature analysis not available in this report.</p>
            )}
          </div>
        );

      case "market":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Market Analysis</h2>
            {reportData?.market_analysis ? (
              <div className="space-y-4">
                {reportData.market_analysis.market_size && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">📊 Market Size</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {reportData.market_analysis.market_size.tam && (
                        <div className="p-3 bg-blue-50 rounded-lg">
                          <div className="text-sm font-medium text-blue-900">TAM</div>
                          <div className="text-lg font-bold text-blue-800">{safeRender(reportData.market_analysis.market_size.tam)}</div>
                        </div>
                      )}
                      {reportData.market_analysis.market_size.sam && (
                        <div className="p-3 bg-green-50 rounded-lg">
                          <div className="text-sm font-medium text-green-900">SAM</div>
                          <div className="text-lg font-bold text-green-800">{safeRender(reportData.market_analysis.market_size.sam)}</div>
                        </div>
                      )}
                      {reportData.market_analysis.market_size.som && (
                        <div className="p-3 bg-purple-50 rounded-lg">
                          <div className="text-sm font-medium text-purple-900">SOM</div>
                          <div className="text-lg font-bold text-purple-800">{safeRender(reportData.market_analysis.market_size.som)}</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                {reportData.market_analysis.market_trends && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">📈 Market Trends</h3>
                    <ul className="space-y-1">
                      {reportData.market_analysis.market_trends.map((trend: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(trend)}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Market analysis not available in this report.</p>
            )}
          </div>
        );

      case "competitors":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Competitive Landscape</h2>
            {reportData?.competitive_landscape ? (
              <div className="space-y-4">
                {reportData.competitive_landscape.direct_competitors && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🎯 Direct Competitors</h3>
                    <ul className="space-y-1">
                      {reportData.competitive_landscape.direct_competitors.map((competitor: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(competitor)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.competitive_landscape.competitive_advantages && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">⚡ Competitive Advantages</h3>
                    <ul className="space-y-1">
                      {reportData.competitive_landscape.competitive_advantages.map((advantage: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(advantage)}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Competitive analysis not available in this report.</p>
            )}
          </div>
        );

      case "business":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Business Model</h2>
            {reportData?.business_model ? (
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">💰 Revenue Streams</h3>
                  {reportData.business_model.revenue_streams && (
                    <ul className="space-y-1">
                      {reportData.business_model.revenue_streams.map((stream: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(stream)}</li>
                      ))}
                    </ul>
                  )}
                </div>
                {reportData.business_model.pricing_strategy && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">💲 Pricing Strategy</h3>
                    <p className="text-gray-700">{safeRender(reportData.business_model.pricing_strategy)}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Business model analysis not available in this report.</p>
            )}
          </div>
        );

      case "challenges":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Challenges & Opportunities</h2>
            {reportData?.challenges_and_opportunities ? (
              <div className="space-y-4">
                {reportData.challenges_and_opportunities.market_challenges && (
                  <div>
                    <h3 className="text-lg font-semibold text-red-800 mb-2">⚠️ Market Challenges</h3>
                    <ul className="space-y-1">
                      {reportData.challenges_and_opportunities.market_challenges.map((challenge: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(challenge)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.challenges_and_opportunities.growth_opportunities && (
                  <div>
                    <h3 className="text-lg font-semibold text-green-800 mb-2">🚀 Growth Opportunities</h3>
                    <ul className="space-y-1">
                      {reportData.challenges_and_opportunities.growth_opportunities.map((opportunity: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(opportunity)}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Challenges and opportunities analysis not available in this report.</p>
            )}
          </div>
        );

      case "financial":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Financial Projections</h2>
            {reportData?.financial_projections ? (
              <div className="space-y-4">
                {reportData.financial_projections.investment_requirements && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">💵 Investment Requirements</h3>
                    <p className="text-gray-700">{safeRender(reportData.financial_projections.investment_requirements)}</p>
                  </div>
                )}
                {reportData.financial_projections.break_even_timeline && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">📈 Break-Even Timeline</h3>
                    <p className="text-gray-700">{safeRender(reportData.financial_projections.break_even_timeline)}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Financial projections not available in this report.</p>
            )}
          </div>
        );

      case "implementation":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Implementation Roadmap</h2>
            {reportData?.implementation_roadmap ? (
              <div className="space-y-4">
                {reportData.implementation_roadmap.phases && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🗓️ Development Phases</h3>
                    <ul className="space-y-1">
                      {reportData.implementation_roadmap.phases.map((phase: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(phase)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.implementation_roadmap.timeline && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">⏰ Timeline</h3>
                    <p className="text-gray-700">{safeRender(reportData.implementation_roadmap.timeline)}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Implementation roadmap not available in this report.</p>
            )}
          </div>
        );

      case "sources":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Sources & Citations</h2>
            {reportData?.citations_and_sources ? (
              <div className="space-y-4">
                {reportData.citations_and_sources.research_sources && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">📚 Research Sources</h3>
                    <ul className="space-y-1">
                      {reportData.citations_and_sources.research_sources.map((source: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(source)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.citations_and_sources.methodology && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🔬 Methodology</h3>
                    <p className="text-gray-700">{safeRender(reportData.citations_and_sources.methodology)}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Sources and citations not available in this report.</p>
            )}
          </div>
        );

      case "next":
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Next Steps</h2>
            {reportData?.next_steps ? (
              <div className="space-y-4">
                {reportData.next_steps.immediate_actions && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🎯 Immediate Actions</h3>
                    <ul className="space-y-1">
                      {reportData.next_steps.immediate_actions.map((action: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(action)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.next_steps.short_term_goals && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">📅 Short-term Goals</h3>
                    <ul className="space-y-1">
                      {reportData.next_steps.short_term_goals.map((goal: any, index: number) => (
                        <li key={index} className="text-gray-700">• {safeRender(goal)}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {reportData.next_steps.long_term_vision && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">🌟 Long-term Vision</h3>
                    <p className="text-gray-700">{safeRender(reportData.next_steps.long_term_vision)}</p>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-600">Next steps not available in this report.</p>
            )}
          </div>
        );

      default:
        return (
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {tabs.find(tab => tab.id === activeTab)?.label || "Section"}
            </h2>
            <p className="text-gray-600">This section is available in the full report. Debug: {JSON.stringify(Object.keys(reportData || {}))}</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Purple Gradient Header - EXACT Proto Match */}
      <div className="bg-gradient-to-r from-purple-600 via-purple-700 to-indigo-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Title */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">{report.title}</h1>
            <p className="text-purple-100 text-lg">{report.description}</p>
          </div>

          {/* Score Metrics - 4 Cards Matching Proto EXACTLY */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${getScoreColor(score)} text-white font-bold text-xl mb-3`}>
                {score}
              </div>
              <div className="text-sm text-purple-200 mb-1">Overall Score</div>
              <div className="font-semibold text-lg">{score}/10</div>
            </div>
            <div className="text-center">
              <CheckCircle className="w-16 h-16 mx-auto mb-3 text-green-300" />
              <div className="text-sm text-purple-200 mb-1">Recommendation</div>
              <div className="font-semibold text-sm">{recommendation}</div>
            </div>
            <div className="text-center">
              <Clock className="w-16 h-16 mx-auto mb-3 text-blue-300" />
              <div className="text-sm text-purple-200 mb-1">Timeline</div>
              <div className="font-semibold">{reportData?.validation_summary?.time_to_market || reportData?.recommendations?.timeline_to_market || "6-12 months"}</div>
            </div>
            <div className="text-center">
              <TrendingUp className="w-16 h-16 mx-auto mb-3 text-emerald-300" />
              <div className="text-sm text-purple-200 mb-1">Market Size</div>
              <div className="font-semibold">{reportData?.market_analysis?.market_size?.tam || "$1.2B+"}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Simple Horizontal Tabs - EXACT Proto Match */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto scrollbar-hide py-4">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`whitespace-nowrap py-2 px-4 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? "text-purple-600 border-b-2 border-purple-600"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Simple Content - EXACT Proto Match */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderTabContent()}
      </div>

      {/* Bottom Navigation - Matching Proto */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <Button
            variant="outline"
            onClick={() => router.push('/validate-idea')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Idea
          </Button>
          <Button
            onClick={() => router.push('/validate-idea')}
            className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white"
          >
            🤖 Start AI Validation
          </Button>
        </div>
      </div>

      {/* Bottom padding to account for fixed navigation */}
      <div className="h-20"></div>
    </div>
  );
}

export default function ReportPage() {
  return <ReportPageContent />;
}