import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, LoginResponse, User } from '@/lib/api';

interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: string | null;
  error: string | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, confirmPassword: string, fullName?: string) => Promise<boolean>;
  logout: () => void;
  setUser: (user: User) => void;
  clearError: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      token: null,
      error: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const loginResponse: LoginResponse = await apiClient.login({ email, password });
          const user: User = await apiClient.getCurrentUser(loginResponse.access_token);
          
          set({ 
            isAuthenticated: true, 
            user,
            token: loginResponse.access_token,
            isLoading: false,
            error: null
          });
          return true;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          console.error('Login error:', error);
          set({ 
            isLoading: false, 
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            token: null
          });
          return false;
        }
      },

      register: async (email: string, password: string, confirmPassword: string, fullName?: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const registerData = {
            email,
            password,
            confirm_password: confirmPassword,
            full_name: fullName || undefined,
          };
          
          await apiClient.register(registerData);
          
          // After successful registration, automatically log the user in
          const loginResponse: LoginResponse = await apiClient.login({ email, password });
          const userData: User = await apiClient.getCurrentUser(loginResponse.access_token);
          
          set({ 
            isAuthenticated: true, 
            user: userData,
            token: loginResponse.access_token,
            isLoading: false,
            error: null
          });
          return true;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          console.error('Registration error:', error);
          set({ 
            isLoading: false, 
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            token: null
          });
          return false;
        }
      },

      logout: () => {
        set({ 
          isAuthenticated: false, 
          user: null,
          token: null,
          error: null
        });
      },

      setUser: (user: User) => {
        set({ 
          isAuthenticated: true, 
          user 
        });
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        isAuthenticated: state.isAuthenticated, 
        user: state.user,
        token: state.token
      }),
    }
  )
);