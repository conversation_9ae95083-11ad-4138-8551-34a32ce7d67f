#!/usr/bin/env python3
"""Quick test script to verify API setup"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend'))

from app.main import app
from app.core.config import settings

def test_config():
    """Test configuration loading"""
    print(f"✓ App Name: {settings.app_name}")
    print(f"✓ Database URL: {settings.database_url}")
    print(f"✓ Debug Mode: {settings.debug}")
    
def test_app_creation():
    """Test FastAPI app creation"""
    print(f"✓ FastAPI app created: {app.title}")
    print(f"✓ API version: {app.version}")

if __name__ == "__main__":
    print("🚀 Testing Incepta API Setup...")
    print("\n1. Testing Configuration:")
    test_config()
    
    print("\n2. Testing App Creation:")
    test_app_creation()
    
    print("\n✅ Basic setup verification complete!")
    print("\nTo start the development server:")
    print("  uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("\nAPI Documentation will be available at:")
    print("  http://localhost:8000/api/docs")