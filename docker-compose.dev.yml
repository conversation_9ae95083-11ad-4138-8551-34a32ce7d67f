services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: incepta-postgres
    environment:
      POSTGRES_DB: incepta
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis
  redis:
    image: redis:7-alpine
    container_name: incepta-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # MinIO
  minio:
    image: minio/minio:latest
    container_name: incepta-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Celery Worker
  celery-worker:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: incepta-celery-worker
    command: celery -A app.worker worker --loglevel=info --queues=ai_analysis,external_tools,consolidation --concurrency=4
    environment:
      - DATABASE_URL=********************************************/incepta
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    restart: unless-stopped

  # Celery Flower (Optional - for monitoring)
  celery-flower:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: incepta-celery-flower
    command: celery -A app.worker flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - celery-worker
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  minio_data: