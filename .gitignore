# Python
  __pycache__/
  *.py[cod]
  *$py.class
  *.so
  .Python
  build/
  develop-eggs/
  dist/
  downloads/
  eggs/
  .eggs/
  lib/
  lib64/
  parts/
  sdist/
  var/
  wheels/
  *.egg-info/
  .installed.cfg
  *.egg

  # Virtual environments
  .env
  .venv
  env/
  venv/
  ENV/
  env.bak/
  venv.bak/

  # Database
  *.db
  *.sqlite3
  test.db

  # IDE
  .vscode/
  .idea/
  *.swp
  *.swo
  *~

  # OS
  .DS_Store
  .DS_Store?
  ._*
  .Spotlight-V100
  .Trashes
  ehthumbs.db
  Thumbs.db

  # Logs
  *.log
  logs/

  # Node modules (for future frontend)
  node_modules/
  npm-debug.log*
  yarn-debug.log*
  yarn-error.log*

  # Docker
  .dockerignore

  # Temporary files
  tmp/
  temp/
