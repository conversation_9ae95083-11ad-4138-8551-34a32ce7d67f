#!/usr/bin/env python3
"""
Comprehensive test script to verify the Incepta setup
"""

import sys
import os
import subprocess
import json
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

def test_directory_structure():
    """Test if all required directories and files exist"""
    print("🔍 Testing Directory Structure...")
    
    required_paths = [
        "backend/app",
        "backend/app/api",
        "backend/app/core", 
        "backend/app/models",
        "backend/app/schemas",
        "backend/app/services",
        "backend/alembic",
        "backend/pyproject.toml",
        "backend/alembic.ini",
        "docker/docker-compose.yml",
        "docker/Dockerfile",
        ".env"
    ]
    
    missing_paths = []
    for path in required_paths:
        if not os.path.exists(path):
            missing_paths.append(path)
    
    if missing_paths:
        print(f"❌ Missing paths: {missing_paths}")
        return False
    else:
        print("✅ All required directories and files exist")
        return True

def test_python_imports():
    """Test if core Python modules can be imported"""
    print("\n🐍 Testing Python Imports...")
    
    try:
        # Test basic imports
        from app.core.config import settings
        print("✅ Config module imported successfully")
        
        from app.main import app
        print("✅ FastAPI app imported successfully")
        
        from app.models.user import User
        print("✅ User model imported successfully")
        
        from app.schemas.auth import LoginRequest
        print("✅ Auth schemas imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_environment_config():
    """Test environment configuration"""
    print("\n⚙️  Testing Environment Configuration...")
    
    try:
        from app.core.config import settings
        
        print(f"✅ App Name: {settings.app_name}")
        print(f"✅ Database URL: {settings.database_url}")
        print(f"✅ Debug Mode: {settings.debug}")
        print(f"✅ CORS Origins: {settings.cors_origins}")
        
        # Check if required env vars are set (even if placeholders)
        if settings.secret_key and len(settings.secret_key) > 10:
            print("✅ Secret key configured")
        else:
            print("⚠️  Secret key needs to be properly configured")
            
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_docker_config():
    """Test Docker configuration files"""
    print("\n🐳 Testing Docker Configuration...")
    
    try:
        # Check docker-compose.yml
        with open("docker/docker-compose.yml", "r") as f:
            compose_content = f.read()
            
        if "backend/app" in compose_content:
            print("✅ Docker compose references correct backend path")
        else:
            print("❌ Docker compose has incorrect path references")
            return False
            
        # Check Dockerfile
        with open("docker/Dockerfile", "r") as f:
            dockerfile_content = f.read()
            
        if "backend/pyproject.toml" in dockerfile_content:
            print("✅ Dockerfile references correct backend path")
        else:
            print("❌ Dockerfile has incorrect path references")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Docker config error: {e}")
        return False

def test_poetry_config():
    """Test Poetry configuration"""
    print("\n📦 Testing Poetry Configuration...")
    
    try:
        with open("backend/pyproject.toml", "r") as f:
            content = f.read()
            
        if "incepta-backend" in content:
            print("✅ Poetry project name is correct")
        else:
            print("❌ Poetry project name needs fixing")
            return False
            
        if "fastapi" in content and "sqlalchemy" in content:
            print("✅ Key dependencies are listed")
        else:
            print("❌ Missing key dependencies")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Poetry config error: {e}")
        return False

def generate_docker_test_commands():
    """Generate Docker test commands"""
    print("\n🐳 Docker Test Commands:")
    print("=" * 50)
    
    commands = [
        "# Start database services only",
        "docker-compose -f docker/docker-compose.yml up postgres redis minio -d",
        "",
        "# Check service status", 
        "docker-compose -f docker/docker-compose.yml ps",
        "",
        "# View logs",
        "docker-compose -f docker/docker-compose.yml logs postgres",
        "",
        "# Test database connection",
        "docker-compose -f docker/docker-compose.yml exec postgres psql -U postgres -d incepta -c '\\l'",
        "",
        "# Stop services",
        "docker-compose -f docker/docker-compose.yml down",
        "",
        "# Build and run full stack",
        "docker-compose -f docker/docker-compose.yml up --build",
    ]
    
    for cmd in commands:
        print(cmd)

def generate_local_dev_commands():
    """Generate local development commands"""
    print("\n🚀 Local Development Commands:")
    print("=" * 50)
    
    commands = [
        "# Install dependencies",
        "cd backend && poetry install",
        "",
        "# Start just PostgreSQL (if you have it locally)",
        "# Or use SQLite by keeping DATABASE_URL=sqlite:///./test.db in .env",
        "",
        "# Run database migrations",
        "cd backend && poetry run alembic upgrade head",
        "",
        "# Start the FastAPI server",
        "cd backend && poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000",
        "",
        "# In another terminal - start Celery worker",
        "cd backend && poetry run celery -A app.worker worker --loglevel=info --queues=ai_analysis,external_tools,consolidation",
        "",
        "# Test API endpoints",
        "curl http://localhost:8000/",
        "curl http://localhost:8000/api/v1/ping",
        "",
        "# View API docs",
        "open http://localhost:8000/api/docs",
    ]
    
    for cmd in commands:
        print(cmd)

def main():
    print("🧪 Incepta Setup Verification Test")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Run all tests
    tests = [
        test_directory_structure,
        test_python_imports, 
        test_environment_config,
        test_docker_config,
        test_poetry_config,
    ]
    
    for test in tests:
        if not test():
            all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! Setup looks good.")
        print("\n📋 Next Steps:")
        generate_local_dev_commands()
        generate_docker_test_commands()
    else:
        print("❌ Some tests failed. Please fix the issues above.")
    
    print("\n💡 Recommendation:")
    print("1. Test local development first (easier debugging)")
    print("2. Then test with Docker once local works")
    print("3. Use SQLite for initial testing, PostgreSQL for production")

if __name__ == "__main__":
    main()