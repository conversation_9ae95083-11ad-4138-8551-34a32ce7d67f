api.ts:127 Making request without auth (temporary test)
api.ts:128 Request body: {idea_description: 'AI powered solution to increase the performance of a fund manager'}
useValidation.ts:104 🚀 Starting comprehensive validation...
useValidation.ts:117 🔧 Environment API URL: http://localhost:8010
useValidation.ts:118 🔧 Using base URL: http://localhost:8010
useValidation.ts:121 🔗 Connecting to: http://localhost:8010/api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTE1NDk3NjgsInN1YiI6IjUifQ.zUp2r5rvhlGDUhKATYQCRh0aPZ_9yTCYaaSiLg_gSGA&idea=AI+powered+solution+to+increase+the+performance+of+a+fund+manager&answers=%7B%22geographic_scope%22%3A%22MENA%22%2C%22tech_involvement%22%3A%22Web+App%22%2C%22target_market%22%3A%22Consumers%22%7D&use_celery=false
useValidation.ts:100 🚫 Validation already in progress, skipping duplicate start
useValidation.ts:139 🔗 Infrastructure response: 200 OK
useValidation.ts:169 📡 Data chunk 1: data: {"type": "connected", "message": "Stream connected successfully"}

data: {"type": "status", "message": "Starting validation with use_celery=False"}


useValidation.ts:204 📊 Processing data: connected - Stream connected successfully
useValidation.ts:230 🔗 Stream connected: Stream connected successfully
useValidation.ts:204 📊 Processing data: status - Starting validation with use_celery=False
useValidation.ts:237 📊 Status: Starting validation with use_celery=False
useValidation.ts:169 📡 Data chunk 2: data: {"type": "phase_start", "phase": 1, "total_phases": 7, "agents": ["Project Concept Specialist"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 1 started with 1 agents
useValidation.ts:169 📡 Data chunk 3: data: {"type": "agent_start", "agent": "Project Concept Specialist", "icon": "\ud83c\udfaf"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Project Concept Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 4: data: {"type": "phase_complete", "phase": 1, "completed_agents": 1}


useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:328 ✅ Phase 1 completed with 1 agents
useValidation.ts:169 📡 Data chunk 5: data: {"type": "phase_start", "phase": 2, "total_phases": 7, "agents": ["Target Users Specialist", "Features Categorization Specialist", "Senior Technical Architect"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 2 started with 3 agents
useValidation.ts:169 📡 Data chunk 6: data: {"type": "agent_start", "agent": "Target Users Specialist", "icon": "\ud83d\udc65"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Users Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 7: data: {"type": "agent_start", "agent": "Features Categorization Specialist", "icon": "\u26a1"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Features Categorization Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 8: data: {"type": "agent_start", "agent": "Senior Technical Architect", "icon": "\ud83d\udd27"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Technical Architect
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 9: data: {"type": "phase_complete", "phase": 2, "completed_agents": 3}

data: {"type": "phase_start", "phase": 3, "total_phases": 7, "agents": ["Senior Market Research Analyst", "Target Market Specialist...
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:328 ✅ Phase 2 completed with 3 agents
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 3 started with 3 agents
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Market Research Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Market Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Financial Analyst
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 10: data: {"type": "heartbeat"}


useValidation.ts:204 📊 Processing data: heartbeat - N/A
useValidation.ts:169 📡 Data chunk 11: data: {"type": "heartbeat"}


useValidation.ts:204 📊 Processing data: heartbeat - N/A
useValidation.ts:169 📡 Data chunk 12: data: {"type": "phase_complete", "phase": 3, "completed_agents": 3}


useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:328 ✅ Phase 3 completed with 3 agents
useValidation.ts:169 📡 Data chunk 13: data: {"type": "phase_start", "phase": 4, "total_phases": 7, "agents": ["Value Proposition Specialist", "Monetization Strategy Analyst"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 4 started with 2 agents
useValidation.ts:169 📡 Data chunk 14: data: {"type": "agent_start", "agent": "Value Proposition Specialist", "icon": "\ud83d\udc8e"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Value Proposition Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 15: data: {"type": "agent_start", "agent": "Monetization Strategy Analyst", "icon": "\ud83d\udcb0"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Monetization Strategy Analyst
useValidation.ts:169 📡 Data chunk 16: data: {"type": "phase_complete", "phase": 4, "completed_agents": 2}

data: {"type": "phase_start", "phase": 5, "total_phases": 7, "agents": ["Competitive Intelligence Specialist", "Market Opportunitie...
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:328 ✅ Phase 4 completed with 2 agents
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 5 started with 3 agents
useValidation.ts:60 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 17: data: {"type": "agent_start", "agent": "Competitive Intelligence Specialist", "icon": "\ud83c\udfc6"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Competitive Intelligence Specialist
useValidation.ts:169 📡 Data chunk 18: data: {"type": "agent_start", "agent": "Market Opportunities Specialist", "icon": "\ud83d\ude80"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Opportunities Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 19: data: {"type": "agent_start", "agent": "Market Challenges Analyst", "icon": "\u26a1"}

data: {"type": "phase_complete", "phase": 5, "completed_agents": 3}

data: {"type": "phase_start", "phase": 6, "t...
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Challenges Analyst
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:328 ✅ Phase 5 completed with 3 agents
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 6 started with 1 agents
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Risk Assessment Specialist
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:328 ✅ Phase 6 completed with 1 agents
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 7 started with 1 agents
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Citations & Sources Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 20: data: {"type": "heartbeat"}


useValidation.ts:204 📊 Processing data: heartbeat - N/A
useValidation.ts:162 🏁 Data stream completed, total chunks: 20
