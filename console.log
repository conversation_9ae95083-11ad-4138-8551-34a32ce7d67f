main-app.js?v=1751540738773:2314 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
ProtectedRoute.tsx:18  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
eval @ prefetch-cache-utils.js:197
task @ promise-queue.js:30
processNext @ promise-queue.js:81
enqueue @ promise-queue.js:45
createLazyPrefetchEntry @ prefetch-cache-utils.js:197
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.js:144
navigateReducer @ navigate-reducer.js:163
clientReducer @ router-reducer.js:25
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
dispatchNavigateAction @ app-router-instance.js:207
eval @ app-router-instance.js:260
exports.startTransition @ react.development.js:1129
push @ app-router-instance.js:258
ProtectedRoute.useEffect @ ProtectedRoute.tsx:18
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
eval @ react-dom-client.development.js:15505
performWorkUntilDeadline @ scheduler.development.js:45
<ProtectedRoute>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
ValidateIdeaPage @ page.tsx:426
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<ValidateIdeaPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1031
resolveModel @ react-server-dom-webpack-client.browser.development.js:1599
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2288
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751540738773:160
options.factory @ webpack.js?v=1751540738773:712
__webpack_require__ @ webpack.js?v=1751540738773:37
fn @ webpack.js?v=1751540738773:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751540738773:182
options.factory @ webpack.js?v=1751540738773:712
__webpack_require__ @ webpack.js?v=1751540738773:37
__webpack_exec__ @ main-app.js?v=1751540738773:2824
(anonymous) @ main-app.js?v=1751540738773:2825
webpackJsonpCallback @ webpack.js?v=1751540738773:1388
(anonymous) @ main-app.js?v=1751540738773:9
api.ts:127 Making request without auth (temporary test)
api.ts:128 Request body: {idea_description: 'AI powered solution for dispute management system'}
useValidation.ts:104 🚀 Starting comprehensive validation...
useValidation.ts:117 🔧 Environment API URL: http://localhost:8010
useValidation.ts:118 🔧 Using base URL: http://localhost:8010
useValidation.ts:121 🔗 Connecting to: http://localhost:8010/api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTE1NDI1NDQsInN1YiI6IjUifQ.QMcJCfK4YNT0QCskGPCLL5Gfg_c8detCf98LQY8tE9I&idea=AI+powered+solution+for+dispute+management+system&answers=%7B%22geographic_scope%22%3A%22MENA%22%2C%22tech_involvement%22%3A%22Web+App%22%2C%22target_market%22%3A%22Consumers%22%7D&use_celery=false
useValidation.ts:100 🚫 Validation already in progress, skipping duplicate start
useValidation.ts:139 🔗 Infrastructure response: 200 OK
useValidation.ts:169 📡 Data chunk 1: data: {"type": "connected", "message": "Stream connected successfully"}

data: {"type": "status", "message": "Starting validation with use_celery=False"}


useValidation.ts:204 📊 Processing data: connected - Stream connected successfully
useValidation.ts:230 🔗 Stream connected: Stream connected successfully
useValidation.ts:204 📊 Processing data: status - Starting validation with use_celery=False
useValidation.ts:237 📊 Status: Starting validation with use_celery=False
useValidation.ts:169 📡 Data chunk 2: data: {"type": "phase_start", "phase": 1, "total_phases": 7, "agents": ["Project Concept Specialist"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 1 started with 1 agents
useValidation.ts:169 📡 Data chunk 3: data: {"type": "agent_start", "agent": "Project Concept Specialist", "icon": "\ud83c\udfaf"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Project Concept Specialist
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 4: data: {"type": "phase_complete", "phase": 1, "completed_agents": 1}


useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 1 completed with 1 agents
useValidation.ts:169 📡 Data chunk 5: data: {"type": "phase_start", "phase": 2, "total_phases": 7, "agents": ["Target Users Specialist", "Features Categorization Specialist", "Senior Technical Architect"]}

data: {"type": "agent_start", "...
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 2 started with 3 agents
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Users Specialist
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 6: data: {"type": "agent_start", "agent": "Features Categorization Specialist", "icon": "\u26a1"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Features Categorization Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 7: data: {"type": "agent_start", "agent": "Senior Technical Architect", "icon": "\ud83d\udd27"}

data: {"type": "phase_complete", "phase": 2, "completed_agents": 3}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Technical Architect
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 2 completed with 3 agents
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 8: data: {"type": "phase_start", "phase": 3, "total_phases": 7, "agents": ["Senior Market Research Analyst", "Target Market Specialist", "Senior Financial Analyst"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 3 started with 3 agents
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:169 📡 Data chunk 9: data: {"type": "agent_start", "agent": "Senior Market Research Analyst", "icon": "\ud83d\udcca"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Market Research Analyst
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 352ms
react-server-dom-webpack-client.browser.development.js:2669  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
hmrRefreshReducerImpl @ hmr-refresh-reducer.js:34
clientReducer @ router-reducer.js:41
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
eval @ app-router-instance.js:274
exports.startTransition @ react.development.js:1129
hmrRefresh @ app-router-instance.js:273
eval @ hot-reloader-client.js:296
exports.startTransition @ react.development.js:1129
processMessage @ hot-reloader-client.js:295
handler @ hot-reloader-client.js:473
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 227ms
react-server-dom-webpack-client.browser.development.js:2669  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
hmrRefreshReducerImpl @ hmr-refresh-reducer.js:34
clientReducer @ router-reducer.js:41
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
eval @ app-router-instance.js:274
exports.startTransition @ react.development.js:1129
hmrRefresh @ app-router-instance.js:273
eval @ hot-reloader-client.js:296
exports.startTransition @ react.development.js:1129
processMessage @ hot-reloader-client.js:295
handler @ hot-reloader-client.js:473
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 263ms
react-server-dom-webpack-client.browser.development.js:2669  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
hmrRefreshReducerImpl @ hmr-refresh-reducer.js:34
clientReducer @ router-reducer.js:41
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
eval @ app-router-instance.js:274
exports.startTransition @ react.development.js:1129
hmrRefresh @ app-router-instance.js:273
eval @ hot-reloader-client.js:296
exports.startTransition @ react.development.js:1129
processMessage @ hot-reloader-client.js:295
handler @ hot-reloader-client.js:473
useValidation.ts:169 📡 Data chunk 10: data: {"type": "agent_start", "agent": "Target Market Specialist", "icon": "\ud83c\udfaf"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Market Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 11: data: {"type": "agent_start", "agent": "Senior Financial Analyst", "icon": "\ud83d\udcb0"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Financial Analyst
useValidation.ts:169 📡 Data chunk 12: data: {"type": "phase_complete", "phase": 3, "completed_agents": 3}


useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 3 completed with 3 agents
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 305ms
react-server-dom-webpack-client.browser.development.js:2669  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
hmrRefreshReducerImpl @ hmr-refresh-reducer.js:34
clientReducer @ router-reducer.js:41
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
eval @ app-router-instance.js:274
exports.startTransition @ react.development.js:1129
hmrRefresh @ app-router-instance.js:273
eval @ hot-reloader-client.js:296
exports.startTransition @ react.development.js:1129
processMessage @ hot-reloader-client.js:295
handler @ hot-reloader-client.js:473
useValidation.ts:169 📡 Data chunk 13: data: {"type": "phase_start", "phase": 4, "total_phases": 7, "agents": ["Value Proposition Specialist", "Monetization Strategy Analyst"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 4 started with 2 agents
useValidation.ts:169 📡 Data chunk 14: data: {"type": "agent_start", "agent": "Value Proposition Specialist", "icon": "\ud83d\udc8e"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Value Proposition Specialist
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 15: data: {"type": "agent_start", "agent": "Monetization Strategy Analyst", "icon": "\ud83d\udcb0"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Monetization Strategy Analyst
useValidation.ts:60 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 16: data: {"type": "phase_complete", "phase": 4, "completed_agents": 2}


useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 4 completed with 2 agents
useValidation.ts:169 📡 Data chunk 17: data: {"type": "phase_start", "phase": 5, "total_phases": 7, "agents": ["Competitive Intelligence Specialist", "Market Opportunities Specialist", "Market Challenges Analyst"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 5 started with 3 agents
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:169 📡 Data chunk 18: data: {"type": "agent_start", "agent": "Competitive Intelligence Specialist", "icon": "\ud83c\udfc6"}

data: {"type": "agent_start", "agent": "Market Opportunities Specialist", "icon": "\ud83d\ude80"}...
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Competitive Intelligence Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Opportunities Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Challenges Analyst
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 5 completed with 3 agents
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 6 started with 1 agents
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Risk Assessment Specialist
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 6 completed with 1 agents
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 7 started with 1 agents
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Citations & Sources Specialist
useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:337 ✅ Phase 7 completed with 1 agents
useValidation.ts:204 📊 Processing data: validation_complete - N/A
useValidation.ts:362 🎉 Validation completed!
page.tsx:122 📊 Received validation report: {validation_summary: {…}, agent_results: {…}, failed_agents: {…}, methodology: {…}}
page.tsx:144 🔍 DEBUG: Attempting to save report to database...
page.tsx:145 🔍 DEBUG: Token exists: true
page.tsx:146 🔍 DEBUG: Report data keys: (6) ['title', 'description', 'user_answers', 'report_data', 'agent_activities', 'processing_metadata']
page.tsx:147 🔍 DEBUG: Report data size: 65230
api.ts:155 🔍 API: Creating validation report...
api.ts:156 🔍 API: Token length: 119
api.ts:157 🔍 API: Report data keys: (6) ['title', 'description', 'user_answers', 'report_data', 'agent_activities', 'processing_metadata']
useValidation.ts:60 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:162 🏁 Data stream completed, total chunks: 18
api.ts:79 
            
            
           POST http://localhost:8010/api/v1/validation/reports 500 (Internal Server Error)
request @ api.ts:79
createValidationReport @ api.ts:160
handleStreamingComplete @ page.tsx:150
useValidation.useCallback[handleValidationComplete] @ useValidation.ts:372
useValidation.useCallback[processSSEMessage] @ useValidation.ts:221
useValidation.useCallback[startValidation] @ useValidation.ts:176
api.ts:84 API Error: {detail: 'Failed to create validation report: 1 validation e…it https://errors.pydantic.dev/2.11/v/string_type'}
error @ intercept-console-error.js:50
request @ api.ts:84
await in request
createValidationReport @ api.ts:160
handleStreamingComplete @ page.tsx:150
useValidation.useCallback[handleValidationComplete] @ useValidation.ts:372
useValidation.useCallback[processSSEMessage] @ useValidation.ts:221
useValidation.useCallback[startValidation] @ useValidation.ts:176
api.ts:96 Failed to parse error response: Error: Failed to create validation report: 1 validation error for ValidationReportResponse
updated_at
  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.11/v/string_type
    at ApiClient.request (api.ts:94:15)
    at async ApiClient.createValidationReport (api.ts:160:22)
    at async handleStreamingComplete (page.tsx:150:27)
error @ intercept-console-error.js:50
request @ api.ts:96
await in request
createValidationReport @ api.ts:160
handleStreamingComplete @ page.tsx:150
useValidation.useCallback[handleValidationComplete] @ useValidation.ts:372
useValidation.useCallback[processSSEMessage] @ useValidation.ts:221
useValidation.useCallback[startValidation] @ useValidation.ts:176
api.ts:170 ❌ API: Failed to create report: Error: HTTP 500: Internal Server Error
    at ApiClient.request (api.ts:97:15)
    at async ApiClient.createValidationReport (api.ts:160:22)
    at async handleStreamingComplete (page.tsx:150:27)
error @ intercept-console-error.js:50
createValidationReport @ api.ts:170
await in createValidationReport
handleStreamingComplete @ page.tsx:150
useValidation.useCallback[handleValidationComplete] @ useValidation.ts:372
useValidation.useCallback[processSSEMessage] @ useValidation.ts:221
useValidation.useCallback[startValidation] @ useValidation.ts:176
page.tsx:161 ❌ Failed to save report to database: Error: HTTP 500: Internal Server Error
    at ApiClient.request (api.ts:97:15)
    at async ApiClient.createValidationReport (api.ts:160:22)
    at async handleStreamingComplete (page.tsx:150:27)
error @ intercept-console-error.js:50
handleStreamingComplete @ page.tsx:161
await in handleStreamingComplete
useValidation.useCallback[handleValidationComplete] @ useValidation.ts:372
useValidation.useCallback[processSSEMessage] @ useValidation.ts:221
useValidation.useCallback[startValidation] @ useValidation.ts:176
page.tsx:166 Uncaught (in promise) ReferenceError: reportData is not defined
    at handleStreamingComplete (page.tsx:166:40)
handleStreamingComplete @ page.tsx:166
await in handleStreamingComplete
useValidation.useCallback[handleValidationComplete] @ useValidation.ts:372
useValidation.useCallback[processSSEMessage] @ useValidation.ts:221
useValidation.useCallback[startValidation] @ useValidation.ts:176
