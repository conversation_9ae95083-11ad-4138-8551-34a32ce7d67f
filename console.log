api.ts:127 Making request without auth (temporary test)
api.ts:128 Request body: {idea_description: 'AI powered solution for dispute management system'}
useValidation.ts:104 🚀 Starting comprehensive validation...
useValidation.ts:117 🔧 Environment API URL: http://localhost:8010
useValidation.ts:118 🔧 Using base URL: http://localhost:8010
useValidation.ts:121 🔗 Connecting to: http://localhost:8010/api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTE1NDA3NjYsInN1YiI6IjUifQ.H5jtuhqb2jABWta1plRMQSb9_o2ZoGnwWPyLNE7D4ko&idea=AI+powered+solution+for+dispute+management+system&answers=%7B%22geographic_scope%22%3A%22MENA%22%2C%22tech_involvement%22%3A%22Web+app%22%2C%22target_market%22%3A%22consumers%22%7D&use_celery=false
useValidation.ts:100 🚫 Validation already in progress, skipping duplicate start
useValidation.ts:139 🔗 Infrastructure response: 200 OK
useValidation.ts:169 📡 Data chunk 1: data: {"type": "connected", "message": "Stream connected successfully"}

data: {"type": "status", "message": "Starting validation with use_celery=False"}


useValidation.ts:204 📊 Processing data: connected - Stream connected successfully
useValidation.ts:230 🔗 Stream connected: Stream connected successfully
useValidation.ts:204 📊 Processing data: status - Starting validation with use_celery=False
useValidation.ts:237 📊 Status: Starting validation with use_celery=False
useValidation.ts:169 📡 Data chunk 2: data: {"type": "phase_start", "phase": 1, "total_phases": 1, "agents": ["Project Concept Specialist", "Target Users Specialist", "Features Categorization Specialist", "Senior Technical Architect", "Se...
useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:316 🚀 Phase 1 started with 14 agents
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:169 📡 Data chunk 3: data: {"type": "agent_start", "agent": "Project Concept Specialist", "icon": "\ud83c\udfaf"}

data: {"type": "agent_start", "agent": "Target Users Specialist", "icon": "\ud83d\udc65"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Project Concept Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Users Specialist
useValidation.ts:169 📡 Data chunk 4: data: {"type": "agent_start", "agent": "Features Categorization Specialist", "icon": "\u26a1"}

data: {"type": "agent_start", "agent": "Senior Technical Architect", "icon": "\ud83d\udd27"}

data: {"ty...
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Features Categorization Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Technical Architect
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Market Research Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Market Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Financial Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Value Proposition Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 5: data: {"type": "agent_start", "agent": "Monetization Strategy Analyst", "icon": "\ud83d\udcb0"}

data: {"type": "agent_start", "agent": "Competitive Intelligence Specialist", "icon": "\ud83c\udfc6"}

...
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Monetization Strategy Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Competitive Intelligence Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Opportunities Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Challenges Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Risk Assessment Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Citations & Sources Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Project Concept Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Users Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Features Categorization Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Technical Architect
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Market Research Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Target Market Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Financial Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Value Proposition Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Monetization Strategy Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Competitive Intelligence Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Opportunities Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Market Challenges Analyst
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Senior Risk Assessment Specialist
useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:267 🧹 Clean Agent Name: Citations & Sources Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Project Concept Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Project Concept Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Target Users Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Target Users Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Features Categorization Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Features Categorization Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Technical Architect started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Technical Architect started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Market Research Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Market Research Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Target Market Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Target Market Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Financial Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Financial Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Value Proposition Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Value Proposition Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Monetization Strategy Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Monetization Strategy Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Competitive Intelligence Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Competitive Intelligence Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Market Opportunities Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Market Opportunities Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Market Challenges Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Market Challenges Analyst started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Risk Assessment Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Senior Risk Assessment Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Citations & Sources Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Citations & Sources Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
