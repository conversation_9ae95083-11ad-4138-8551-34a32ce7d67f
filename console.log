page.tsx:30  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
eval @ prefetch-cache-utils.js:197
task @ promise-queue.js:30
processNext @ promise-queue.js:81
enqueue @ promise-queue.js:45
createLazyPrefetchEntry @ prefetch-cache-utils.js:197
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.js:144
navigateReducer @ navigate-reducer.js:163
clientReducer @ router-reducer.js:25
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
dispatchNavigateAction @ app-router-instance.js:207
eval @ app-router-instance.js:260
exports.startTransition @ react.development.js:1129
push @ app-router-instance.js:258
handleSubmit @ page.tsx:30
await in handleSubmit
executeDispatch @ react-dom-client.development.js:16502
runWithFiberInDEV @ react-dom-client.development.js:845
processDispatchQueue @ react-dom-client.development.js:16552
eval @ react-dom-client.development.js:17150
batchedUpdates$1 @ react-dom-client.development.js:3263
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16706
dispatchEvent @ react-dom-client.development.js:20816
dispatchDiscreteEvent @ react-dom-client.development.js:20784
<form>
exports.jsxDEV @ react-jsx-dev-runtime.development.js:346
LoginPage @ page.tsx:88
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10556
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
<LoginPage>
exports.jsx @ react-jsx-runtime.development.js:339
ClientPageRoot @ client-page.js:20
react-stack-bottom-frame @ react-dom-client.development.js:22974
renderWithHooksAgain @ react-dom-client.development.js:6767
renderWithHooks @ react-dom-client.development.js:6679
updateFunctionComponent @ react-dom-client.development.js:8931
beginWork @ react-dom-client.development.js:10505
runWithFiberInDEV @ react-dom-client.development.js:845
performUnitOfWork @ react-dom-client.development.js:15258
workLoopConcurrentByScheduler @ react-dom-client.development.js:15252
renderRootConcurrent @ react-dom-client.development.js:15227
performWorkOnRoot @ react-dom-client.development.js:14525
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16350
performWorkUntilDeadline @ scheduler.development.js:45
"use client"
eval @ react-server-dom-webpack-client.browser.development.js:2354
initializeModelChunk @ react-server-dom-webpack-client.browser.development.js:1054
resolveModelChunk @ react-server-dom-webpack-client.browser.development.js:1031
resolveModel @ react-server-dom-webpack-client.browser.development.js:1599
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2288
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751533692681:160
options.factory @ webpack.js?v=1751533692681:712
__webpack_require__ @ webpack.js?v=1751533692681:37
fn @ webpack.js?v=1751533692681:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751533692681:182
options.factory @ webpack.js?v=1751533692681:712
__webpack_require__ @ webpack.js?v=1751533692681:37
__webpack_exec__ @ main-app.js?v=1751533692681:2824
(anonymous) @ main-app.js?v=1751533692681:2825
webpackJsonpCallback @ webpack.js?v=1751533692681:1388
(anonymous) @ main-app.js?v=1751533692681:9
page.tsx:30  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
eval @ prefetch-cache-utils.js:197
task @ promise-queue.js:30
processNext @ promise-queue.js:81
enqueue @ promise-queue.js:45
createLazyPrefetchEntry @ prefetch-cache-utils.js:197
getOrCreatePrefetchCacheEntry @ prefetch-cache-utils.js:144
navigateReducer @ navigate-reducer.js:163
clientReducer @ router-reducer.js:25
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
dispatchNavigateAction @ app-router-instance.js:207
eval @ app-router-instance.js:260
exports.startTransition @ react.development.js:1129
push @ app-router-instance.js:258
handleSubmit @ page.tsx:30
await in handleSubmit
executeDispatch @ react-dom-client.development.js:16502
runWithFiberInDEV @ react-dom-client.development.js:845
processDispatchQueue @ react-dom-client.development.js:16552
eval @ react-dom-client.development.js:17150
batchedUpdates$1 @ react-dom-client.development.js:3263
dispatchEventForPluginEventSystem @ react-dom-client.development.js:16706
dispatchEvent @ react-dom-client.development.js:20816
dispatchDiscreteEvent @ react-dom-client.development.js:20784
api.ts:127 Making request without auth (temporary test)
api.ts:128 Request body: {idea_description: 'AI powered solution to increase the performance of a fund manager'}
useValidation.ts:104 🚀 Starting comprehensive validation...
useValidation.ts:117 🔧 Environment API URL: http://localhost:8010
useValidation.ts:118 🔧 Using base URL: http://localhost:8010
useValidation.ts:121 🔗 Connecting to: http://localhost:8010/api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTE1MzU3MjIsInN1YiI6IjUifQ.jc3wpTcZ45wkIuU7H8ZFaoqyhaLU_kAgCsG7i7Lty8k&idea=AI+powered+solution+to+increase+the+performance+of+a+fund+manager&answers=%7B%22geographic_scope%22%3A%22MENA%22%2C%22tech_involvement%22%3A%22Web+App%22%2C%22target_market%22%3A%22Consumers%22%7D&use_celery=false
useValidation.ts:100 🚫 Validation already in progress, skipping duplicate start
useValidation.ts:139 🔗 Infrastructure response: 200 OK
useValidation.ts:169 📡 Data chunk 1: data: {"type": "connected", "message": "Stream connected successfully"}

data: {"type": "status", "message": "Starting validation with use_celery=False"}


useValidation.ts:204 📊 Processing data: connected - Stream connected successfully
useValidation.ts:230 🔗 Stream connected: Stream connected successfully
useValidation.ts:204 📊 Processing data: status - Starting validation with use_celery=False
useValidation.ts:237 📊 Status: Starting validation with use_celery=False
useValidation.ts:169 📡 Data chunk 2: data: {"type": "phase_start", "phase": 1, "total_phases": 7, "agents": ["Project Concept Specialist"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:301 🚀 Phase 1 started with 1 agents
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:60 ✅ Adding new activity: agent_start - undefined
useValidation.ts:169 📡 Data chunk 3: data: {"type": "agent_start", "agent": "Project Concept Specialist", "icon": "\ud83c\udfaf"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:255 🧹 Clean Agent Name: Project Concept Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Searching market data and trends
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:60 ✅ Adding new activity: tool_call - Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 4: data: {"type": "agent_complete", "agent": "Project Concept Specialist", "agent_key": "project_concept", "success": true, "phase": 1}


useValidation.ts:204 📊 Processing data: agent_complete - N/A
useValidation.ts:74 🔄 Updating activity for Project Concept Specialist: {content: 'Project Concept Specialist completed analysis', status: 'completed'}
useValidation.ts:74 🔄 Updating activity for Project Concept Specialist: {content: 'Project Concept Specialist completed analysis', status: 'completed'}
useValidation.ts:169 📡 Data chunk 5: data: {"type": "phase_complete", "phase": 1, "completed_agents": 1}


useValidation.ts:204 📊 Processing data: phase_complete - N/A
useValidation.ts:322 ✅ Phase 1 completed with 1 agents
useValidation.ts:169 📡 Data chunk 6: data: {"type": "phase_start", "phase": 2, "total_phases": 7, "agents": ["Target Users Specialist", "Features Categorization Specialist", "Senior Technical Architect"]}


useValidation.ts:204 📊 Processing data: phase_start - N/A
useValidation.ts:301 🚀 Phase 2 started with 3 agents
useValidation.ts:56 🚫 Duplicate activity detected, skipping: undefined
useValidation.ts:56 🚫 Duplicate activity detected, skipping: undefined
useValidation.ts:56 🚫 Duplicate activity detected, skipping: undefined
useValidation.ts:56 🚫 Duplicate activity detected, skipping: undefined
useValidation.ts:56 🚫 Duplicate activity detected, skipping: undefined
useValidation.ts:56 🚫 Duplicate activity detected, skipping: undefined
useValidation.ts:169 📡 Data chunk 7: data: {"type": "agent_start", "agent": "Target Users Specialist", "icon": "\ud83d\udc65"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:255 🧹 Clean Agent Name: Target Users Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
utils.ts:118 🔍 Filtering out activity from undefined, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from Target Users Specialist, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from Target Users Specialist, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from Target Users Specialist, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from Target Users Specialist, looking for Project Concept Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
useValidation.ts:169 📡 Data chunk 8: data: {"type": "agent_complete", "agent": "Target Users Specialist", "agent_key": "target_users", "success": true, "phase": 2}


useValidation.ts:204 📊 Processing data: agent_complete - N/A
useValidation.ts:74 🔄 Updating activity for Target Users Specialist: {content: 'Target Users Specialist completed analysis', status: 'completed'}
useValidation.ts:74 🔄 Updating activity for Target Users Specialist: {content: 'Target Users Specialist completed analysis', status: 'completed'}
utils.ts:118 🔍 Filtering out activity from undefined, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from undefined, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
utils.ts:118 🔍 Filtering out activity from Project Concept Specialist, looking for Target Users Specialist
useValidation.ts:169 📡 Data chunk 9: data: {"type": "agent_start", "agent": "Features Categorization Specialist", "icon": "\u26a1"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:255 🧹 Clean Agent Name: Features Categorization Specialist
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 10: data: {"type": "agent_complete", "agent": "Features Categorization Specialist", "agent_key": "features", "success": true, "phase": 2}


useValidation.ts:204 📊 Processing data: agent_complete - N/A
useValidation.ts:74 🔄 Updating activity for Features Categorization Specialist: {content: 'Features Categorization Specialist completed analysis', status: 'completed'}
useValidation.ts:74 🔄 Updating activity for Features Categorization Specialist: {content: 'Features Categorization Specialist completed analysis', status: 'completed'}
useValidation.ts:169 📡 Data chunk 11: data: {"type": "agent_start", "agent": "Senior Technical Architect", "icon": "\ud83d\udd27"}


useValidation.ts:204 📊 Processing data: agent_start - N/A
useValidation.ts:255 🧹 Clean Agent Name: Senior Technical Architect
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:60 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Searching market data and trends
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:56 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
useValidation.ts:169 📡 Data chunk 12: data: {"type": "agent_complete", "agent": "Senior Technical Architect", "agent_key": "technical_analysis", "success": true, "phase": 2}

data: {"type": "error", "message": "Validation timeout"}


useValidation.ts:204 📊 Processing data: agent_complete - N/A
useValidation.ts:204 📊 Processing data: error - Validation timeout
useValidation.ts:377 ❌ Validation error: undefined
error @ intercept-console-error.js:50
useValidation.useCallback[handleError] @ useValidation.ts:377
useValidation.useCallback[processSSEMessage] @ useValidation.ts:227
useValidation.useCallback[startValidation] @ useValidation.ts:176
await in useValidation.useCallback[startValidation]
useValidation.useEffect @ useValidation.ts:397
react-stack-bottom-frame @ react-dom-client.development.js:23055
runWithFiberInDEV @ react-dom-client.development.js:845
commitHookEffectListMount @ react-dom-client.development.js:11978
commitHookPassiveMountEffects @ react-dom-client.development.js:12099
commitPassiveMountOnFiber @ react-dom-client.development.js:13929
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13932
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13922
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:14048
recursivelyTraversePassiveMountEffects @ react-dom-client.development.js:13902
commitPassiveMountOnFiber @ react-dom-client.development.js:13941
flushPassiveEffects @ react-dom-client.development.js:15869
flushPendingEffects @ react-dom-client.development.js:15830
flushSpawnedWork @ react-dom-client.development.js:15796
commitRoot @ react-dom-client.development.js:15529
commitRootWhenReady @ react-dom-client.development.js:14759
performWorkOnRoot @ react-dom-client.development.js:14682
performSyncWorkOnRoot @ react-dom-client.development.js:16365
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16211
processRootScheduleInMicrotask @ react-dom-client.development.js:16250
eval @ react-dom-client.development.js:16384


