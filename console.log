main-app.js?v=1751537783180:2314 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
main-app.js?v=1751537783180:160  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
eval @ app-index.js:132
(app-pages-browser)/./node_modules/next/dist/client/app-index.js @ main-app.js?v=1751537783180:160
options.factory @ webpack.js?v=1751537783180:712
__webpack_require__ @ webpack.js?v=1751537783180:37
fn @ webpack.js?v=1751537783180:369
eval @ app-next-dev.js:11
eval @ app-bootstrap.js:62
loadScriptsInSequence @ app-bootstrap.js:23
appBootstrap @ app-bootstrap.js:56
eval @ app-next-dev.js:10
(app-pages-browser)/./node_modules/next/dist/client/app-next-dev.js @ main-app.js?v=1751537783180:182
options.factory @ webpack.js?v=1751537783180:712
__webpack_require__ @ webpack.js?v=1751537783180:37
__webpack_exec__ @ main-app.js?v=1751537783180:2824
(anonymous) @ main-app.js?v=1751537783180:2825
webpackJsonpCallback @ webpack.js?v=1751537783180:1388
(anonymous) @ main-app.js?v=1751537783180:9
api.ts:127 Making request without auth (temporary test)
api.ts:128 Request body: {idea_description: 'AI powered solution to increase the performance of a fund manager'}
VM2707 useValidation.ts:119 🚀 Starting comprehensive validation...
VM2707 useValidation.ts:133 🔧 Environment API URL: http://localhost:8010
VM2707 useValidation.ts:134 🔧 Using base URL: http://localhost:8010
VM2707 useValidation.ts:136 🔗 Connecting to: http://localhost:8010/api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTE1Mzk1ODksInN1YiI6IjUifQ.MHGnzIVBRKJ30KGBzWQ81qN5Tcsi31LO-3QzI9dDLu4&idea=AI+powered+solution+to+increase+the+performance+of+a+fund+manager&answers=%7B%22geographic_scope%22%3A%22MENA%22%2C%22tech_involvement%22%3A%22Web+App%22%2C%22target_market%22%3A%22Consumers%22%7D&use_celery=false
VM2707 useValidation.ts:116 🚫 Validation already in progress, skipping duplicate start
VM2707 useValidation.ts:153 🔗 Infrastructure response: 200 OK
VM2707 useValidation.ts:177 📡 Data chunk 1: data: {"type": "connected", "message": "Stream connected successfully"}

data: {"type": "status", "message": "Starting validation with use_celery=False"}


VM2707 useValidation.ts:218 📊 Processing data: connected - Stream connected successfully
VM2707 useValidation.ts:243 🔗 Stream connected: Stream connected successfully
VM2707 useValidation.ts:218 📊 Processing data: status - Starting validation with use_celery=False
VM2707 useValidation.ts:252 📊 Status: Starting validation with use_celery=False
VM2707 useValidation.ts:177 📡 Data chunk 2: data: {"type": "phase_start", "phase": 1, "total_phases": 7, "agents": ["Project Concept Specialist"]}


VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 1 started with 1 agents
VM2707 useValidation.ts:177 📡 Data chunk 3: data: {"type": "agent_start", "agent": "Project Concept Specialist", "icon": "\ud83c\udfaf"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Project Concept Specialist
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Project Concept Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:177 📡 Data chunk 4: data: {"type": "agent_complete", "agent": "Project Concept Specialist", "agent_key": "project_concept", "success": true, "phase": 1}


VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:75 🔄 Updating activity for Project Concept Specialist: {content: 'Project Concept Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Project Concept Specialist: {content: 'Project Concept Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:177 📡 Data chunk 5: data: {"type": "phase_complete", "phase": 1, "completed_agents": 1}

data: {"type": "phase_start", "phase": 2, "total_phases": 7, "agents": ["Target Users Specialist", "Features Categorization Special...
VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 1 completed with 1 agents
VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 2 started with 3 agents
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:177 📡 Data chunk 6: data: {"type": "agent_start", "agent": "Target Users Specialist", "icon": "\ud83d\udc65"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Target Users Specialist
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Target Users Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 308ms
react-server-dom-webpack-client.browser.development.js:2669  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
hmrRefreshReducerImpl @ hmr-refresh-reducer.js:34
clientReducer @ router-reducer.js:41
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
eval @ app-router-instance.js:274
exports.startTransition @ react.development.js:1129
hmrRefresh @ app-router-instance.js:273
eval @ hot-reloader-client.js:296
exports.startTransition @ react.development.js:1129
processMessage @ hot-reloader-client.js:295
handler @ hot-reloader-client.js:473
VM2707 useValidation.ts:177 📡 Data chunk 7: data: {"type": "agent_start", "agent": "Features Categorization Specialist", "icon": "\u26a1"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Features Categorization Specialist
VM2707 useValidation.ts:177 📡 Data chunk 8: data: {"type": "agent_start", "agent": "Senior Technical Architect", "icon": "\ud83d\udd27"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Senior Technical Architect
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Features Categorization Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Technical Architect started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
hot-reloader-client.js:197 [Fast Refresh] rebuilding
report-hmr-latency.js:14 [Fast Refresh] done in 289ms
react-server-dom-webpack-client.browser.development.js:2669  Server   ⚠ metadataBase property in metadata export is not set for resolving social open graph or twitter images, using "http://localhost:3001". See https://nextjs.org/docs/app/api-reference/functions/generate-metadata#metadatabase
react-stack-bottom-frame @ react-server-dom-webpack-client.browser.development.js:2669
resolveConsoleEntry @ react-server-dom-webpack-client.browser.development.js:2135
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2270
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
<StreamingMetadataOutlet>
buildFakeTask @ react-server-dom-webpack-client.browser.development.js:2040
initializeFakeTask @ react-server-dom-webpack-client.browser.development.js:2027
resolveDebugInfo @ react-server-dom-webpack-client.browser.development.js:2063
processFullStringRow @ react-server-dom-webpack-client.browser.development.js:2261
processFullBinaryRow @ react-server-dom-webpack-client.browser.development.js:2233
progress @ react-server-dom-webpack-client.browser.development.js:2479
"use server"
ResponseInstance @ react-server-dom-webpack-client.browser.development.js:1587
createResponseFromOptions @ react-server-dom-webpack-client.browser.development.js:2396
exports.createFromReadableStream @ react-server-dom-webpack-client.browser.development.js:2717
createFromNextReadableStream @ fetch-server-response.js:172
fetchServerResponse @ fetch-server-response.js:126
await in fetchServerResponse
hmrRefreshReducerImpl @ hmr-refresh-reducer.js:34
clientReducer @ router-reducer.js:41
action @ app-router-instance.js:156
runAction @ app-router-instance.js:66
dispatchAction @ app-router-instance.js:120
dispatch @ app-router-instance.js:154
eval @ use-action-queue.js:55
startTransition @ react-dom-client.development.js:7843
dispatch @ use-action-queue.js:54
dispatchAppRouterAction @ use-action-queue.js:37
eval @ app-router-instance.js:274
exports.startTransition @ react.development.js:1129
hmrRefresh @ app-router-instance.js:273
eval @ hot-reloader-client.js:296
exports.startTransition @ react.development.js:1129
processMessage @ hot-reloader-client.js:295
handler @ hot-reloader-client.js:473
VM2707 useValidation.ts:177 📡 Data chunk 9: data: {"type": "agent_complete", "agent": "Target Users Specialist", "agent_key": "target_users", "success": true, "phase": 2}


VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:75 🔄 Updating activity for Target Users Specialist: {content: 'Target Users Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:177 📡 Data chunk 10: data: {"type": "agent_complete", "agent": "Features Categorization Specialist", "agent_key": "features", "success": true, "phase": 2}


VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:75 🔄 Updating activity for Target Users Specialist: {content: 'Target Users Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Features Categorization Specialist: {content: 'Features Categorization Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Features Categorization Specialist: {content: 'Features Categorization Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:177 📡 Data chunk 11: data: {"type": "agent_complete", "agent": "Senior Technical Architect", "agent_key": "technical_analysis", "success": true, "phase": 2}


VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Technical Architect: {content: 'Senior Technical Architect completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Technical Architect: {content: 'Senior Technical Architect completed analysis', status: 'completed'}
VM2707 useValidation.ts:177 📡 Data chunk 12: data: {"type": "phase_complete", "phase": 2, "completed_agents": 3}


VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 2 completed with 3 agents
VM2707 useValidation.ts:177 📡 Data chunk 13: data: {"type": "phase_start", "phase": 3, "total_phases": 7, "agents": ["Senior Market Research Analyst", "Target Market Specialist", "Senior Financial Analyst"]}


VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 3 started with 3 agents
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:177 📡 Data chunk 14: data: {"type": "agent_start", "agent": "Senior Market Research Analyst", "icon": "\ud83d\udcca"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Senior Market Research Analyst
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Market Research Analyst started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:177 📡 Data chunk 15: data: {"type": "agent_start", "agent": "Target Market Specialist", "icon": "\ud83c\udfaf"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Target Market Specialist
VM2707 useValidation.ts:177 📡 Data chunk 16: data: {"type": "agent_start", "agent": "Senior Financial Analyst", "icon": "\ud83d\udcb0"}


VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Senior Financial Analyst
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Target Market Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Financial Analyst started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:177 📡 Data chunk 17: data: {"type": "agent_complete", "agent": "Senior Market Research Analyst", "agent_key": "market_analysis", "success": true, "phase": 3}


VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Market Research Analyst: {content: 'Senior Market Research Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Market Research Analyst: {content: 'Senior Market Research Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:177 📡 Data chunk 18: data: {"type": "agent_complete", "agent": "Target Market Specialist", "agent_key": "target_market", "success": true, "phase": 3}


VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:75 🔄 Updating activity for Target Market Specialist: {content: 'Target Market Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Target Market Specialist: {content: 'Target Market Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:177 📡 Data chunk 19: data: {"type": "agent_complete", "agent": "Senior Financial Analyst", "agent_key": "financial_analysis", "success": true, "phase": 3}

data: {"type": "phase_complete", "phase": 3, "completed_agents": ...
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 3 completed with 3 agents
VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 4 started with 2 agents
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Value Proposition Specialist
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Monetization Strategy Analyst
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 4 completed with 2 agents
VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 5 started with 3 agents
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Competitive Intelligence Specialist
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Market Opportunities Specialist
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Financial Analyst: {content: 'Senior Financial Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Financial Analyst: {content: 'Senior Financial Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Value Proposition Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Searching market data and trends
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: tool_call - Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Monetization Strategy Analyst started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:75 🔄 Updating activity for Value Proposition Specialist: {content: 'Value Proposition Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Value Proposition Specialist: {content: 'Value Proposition Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Monetization Strategy Analyst: {content: 'Monetization Strategy Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Monetization Strategy Analyst: {content: 'Monetization Strategy Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Competitive Intelligence Specialist started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Market Opportunities Specialist started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:177 📡 Data chunk 20: data: {"type": "agent_start", "agent": "Market Challenges Analyst", "icon": "\u26a1"}

data: {"type": "agent_complete", "agent": "Competitive Intelligence Specialist", "agent_key": "competitors", "suc...
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Market Challenges Analyst
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 5 completed with 3 agents
VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 6 started with 1 agents
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Senior Risk Assessment Specialist
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 6 completed with 1 agents
VM2707 useValidation.ts:218 📊 Processing data: phase_start - N/A
VM2707 useValidation.ts:336 🚀 Phase 7 started with 1 agents
VM2707 useValidation.ts:218 📊 Processing data: agent_start - N/A
VM2707 useValidation.ts:285 🧹 Clean Agent Name: Citations & Sources Specialist
VM2707 useValidation.ts:218 📊 Processing data: agent_complete - N/A
VM2707 useValidation.ts:218 📊 Processing data: phase_complete - N/A
VM2707 useValidation.ts:365 ✅ Phase 7 completed with 1 agents
VM2707 useValidation.ts:218 📊 Processing data: validation_complete - N/A
VM2707 useValidation.ts:397 🎉 Validation completed!
page.tsx:122 📊 Received validation report: {validation_summary: {…}, agent_results: {…}, failed_agents: {…}, methodology: {…}}
VM2707 useValidation.ts:172 🏁 Data stream completed, total chunks: 20
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Market Challenges Analyst started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:75 🔄 Updating activity for Competitive Intelligence Specialist: {content: 'Competitive Intelligence Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Competitive Intelligence Specialist: {content: 'Competitive Intelligence Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Market Opportunities Specialist: {content: 'Market Opportunities Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Market Opportunities Specialist: {content: 'Market Opportunities Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Market Challenges Analyst: {content: 'Market Challenges Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Market Challenges Analyst: {content: 'Market Challenges Analyst completed analysis', status: 'completed'}
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Senior Risk Assessment Specialist started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Risk Assessment Specialist: {content: 'Senior Risk Assessment Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Senior Risk Assessment Specialist: {content: 'Senior Risk Assessment Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: agent_start - undefined
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
VM2707 useValidation.ts:54 ✅ Adding new activity: ai_processing - Citations & Sources Specialist started analysis
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Searching market data and trends
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:51 🚫 Duplicate activity detected, skipping: Gathering competitive intelligence
VM2707 useValidation.ts:75 🔄 Updating activity for Citations & Sources Specialist: {content: 'Citations & Sources Specialist completed analysis', status: 'completed'}
VM2707 useValidation.ts:75 🔄 Updating activity for Citations & Sources Specialist: {content: 'Citations & Sources Specialist completed analysis', status: 'completed'}
