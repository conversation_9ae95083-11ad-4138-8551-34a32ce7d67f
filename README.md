# Incepta - AI-Powered Idea Validation Platform

Modern full-stack application for idea validation and project planning, rebuilt with scalable FastAPI backend and Next.js frontend.

## Project Structure

```
incepta-py/
├── backend/              # FastAPI backend API
│   ├── app/             # Application code
│   ├── alembic/         # Database migrations
│   └── pyproject.toml   # Python dependencies
├── frontend/            # Next.js frontend (coming soon)
├── docker/              # Docker configuration
├── .env                 # Environment variables
└── README.md           # This file
```

## Features

- 🤖 Multi-provider AI integration (OpenAI, Anthropic, DeepSeek, Gemini)
- 🔍 Enhanced validation with Tavily search and Firecrawl scraping
- 🎭 Playwright automation for web interactions
- 🔄 Real-time streaming with Server-Sent Events
- 📊 PostgreSQL database with Alembic migrations
- 🚀 Redis caching and Celery background tasks
- 📦 MinIO object storage
- 🔐 JWT authentication with role-based access
- 🐳 Docker containerization
- 🌐 CORS-enabled API

## Quick Start

### Prerequisites

- Python 3.11+
- Docker and Docker Compose
- Poetry (for local development)

### Environment Setup

1. Copy environment variables:
```bash
cp .env.example .env
```

2. Fill in your API keys in `.env`

### Development with Docker

1. Start all services:
```bash
cd docker
docker-compose up -d
```

2. The API will be available at: http://localhost:8000
3. API documentation: http://localhost:8000/api/docs
4. MinIO console: http://localhost:9001

### Local Development

1. Install dependencies:
```bash
cd incepta-api
poetry install
```

2. Start services (PostgreSQL, Redis, MinIO):
```bash
cd docker
docker-compose up postgres redis minio -d
```

3. Run migrations:
```bash
poetry run alembic upgrade head
```

4. Start the application:
```bash
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

5. Start Celery worker:
```bash
poetry run celery -A app.worker worker --loglevel=info --queues=ai_analysis,external_tools,consolidation
```

## Project Structure

```
app/
├── api/                 # API routes
│   └── v1/
│       ├── endpoints/   # API endpoints
│       └── deps/        # Dependencies
├── core/                # Core configuration
├── models/              # SQLAlchemy models
├── schemas/             # Pydantic schemas
├── services/            # Business logic
└── utils/               # Utilities

docker/                  # Docker configuration
tests/                   # Test suites
```

## Tech Stack

- **Backend**: FastAPI, SQLAlchemy, Alembic
- **Database**: PostgreSQL
- **Cache**: Redis
- **Queue**: Celery
- **Storage**: MinIO
- **AI**: OpenAI, Anthropic, DeepSeek, Gemini
- **Tools**: Tavily, Firecrawl, Playwright
- **Deployment**: Docker, Nginx

## API Endpoints

- `GET /` - Root endpoint
- `GET /health` - Health check
- `GET /api/v1/ping` - API ping
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/validation/idea` - Idea validation
- `GET /api/v1/validation/{id}/stream` - Streaming validation results

## License

MIT License