#!/bin/bash

echo "🧪 Incepta Setup Verification"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if file/directory exists
check_path() {
    if [ -e "$1" ]; then
        echo -e "${GREEN}✅${NC} $1"
        return 0
    else
        echo -e "${RED}❌${NC} $1 (missing)"
        return 1
    fi
}

echo -e "\n${BLUE}🔍 Checking Directory Structure...${NC}"
missing_count=0

# Check required paths
paths=(
    "backend"
    "backend/app"
    "backend/app/main.py"
    "backend/app/core"
    "backend/app/core/config.py"
    "backend/app/models"
    "backend/app/schemas"
    "backend/app/services"
    "backend/pyproject.toml"
    "backend/alembic.ini"
    "docker"
    "docker-compose.dev.yml"
    ".env"
)

for path in "${paths[@]}"; do
    if ! check_path "$path"; then
        ((missing_count++))
    fi
done

echo -e "\n${BLUE}📦 Checking Poetry Configuration...${NC}"
if [ -f "backend/pyproject.toml" ]; then
    if grep -q "incepta-backend" backend/pyproject.toml; then
        echo -e "${GREEN}✅${NC} Poetry project name is correct"
    else
        echo -e "${YELLOW}⚠️${NC} Poetry project name might need checking"
    fi
    
    if grep -q "fastapi" backend/pyproject.toml; then
        echo -e "${GREEN}✅${NC} FastAPI dependency found"
    else
        echo -e "${RED}❌${NC} FastAPI dependency missing"
        ((missing_count++))
    fi
fi

echo -e "\n${BLUE}🐳 Checking Docker Configuration...${NC}"
if [ -f "docker-compose.dev.yml" ]; then
    if grep -q "postgres" docker-compose.dev.yml; then
        echo -e "${GREEN}✅${NC} PostgreSQL service configured"
    else
        echo -e "${RED}❌${NC} PostgreSQL service missing"
        ((missing_count++))
    fi
    
    if grep -q "redis" docker-compose.dev.yml; then
        echo -e "${GREEN}✅${NC} Redis service configured"
    else
        echo -e "${RED}❌${NC} Redis service missing"
        ((missing_count++))
    fi
fi

echo -e "\n${BLUE}⚙️ Checking Environment...${NC}"
if [ -f ".env" ]; then
    if grep -q "SECRET_KEY" .env; then
        echo -e "${GREEN}✅${NC} Environment file has SECRET_KEY"
    else
        echo -e "${YELLOW}⚠️${NC} SECRET_KEY not found in .env"
    fi
    
    if grep -q "DATABASE_URL" .env; then
        echo -e "${GREEN}✅${NC} Environment file has DATABASE_URL"
    else
        echo -e "${RED}❌${NC} DATABASE_URL missing from .env"
        ((missing_count++))
    fi
fi

echo -e "\n${BLUE}🔧 System Requirements...${NC}"

# Check Python
if command -v python3 &> /dev/null; then
    python_version=$(python3 --version)
    echo -e "${GREEN}✅${NC} Python available: $python_version"
else
    echo -e "${RED}❌${NC} Python3 not found"
    ((missing_count++))
fi

# Check Poetry
if command -v poetry &> /dev/null; then
    poetry_version=$(poetry --version)
    echo -e "${GREEN}✅${NC} Poetry available: $poetry_version"
else
    echo -e "${YELLOW}⚠️${NC} Poetry not found - install with: curl -sSL https://install.python-poetry.org | python3 -"
fi

# Check Docker
if command -v docker &> /dev/null; then
    docker_version=$(docker --version)
    echo -e "${GREEN}✅${NC} Docker available: $docker_version"
else
    echo -e "${YELLOW}⚠️${NC} Docker not found - install from https://docker.com"
fi

# Summary
echo -e "\n=============================="
if [ $missing_count -eq 0 ]; then
    echo -e "${GREEN}🎉 Setup verification passed!${NC}"
    echo -e "\n${BLUE}📋 Next Steps:${NC}"
    echo "1. Install dependencies: cd backend && poetry install"
    echo "2. Start services: docker-compose -f docker-compose.dev.yml up -d"
    echo "3. Run migrations: cd backend && poetry run alembic upgrade head"
    echo "4. Start API: cd backend && poetry run uvicorn app.main:app --reload"
    echo "5. Test API: curl http://localhost:8000/"
    echo "6. View docs: http://localhost:8000/api/docs"
else
    echo -e "${RED}❌ Found $missing_count issues to fix${NC}"
    echo "Please check the missing files/configurations above"
fi

echo -e "\n${BLUE}📚 For detailed testing instructions, see: TESTING.md${NC}"