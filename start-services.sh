#!/bin/bash

echo "🚀 Starting Incepta Development Environment..."

# Stop any existing containers
echo "📦 Stopping existing containers..."
docker-compose -f docker-compose.dev.yml down

# Start all services
echo "🔄 Starting all services..."
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🔍 Checking service health..."
echo "📊 PostgreSQL: $(docker exec incepta-postgres pg_isready -U postgres)"
echo "🗄️  Redis: $(docker exec incepta-redis redis-cli ping)"
echo "📁 MinIO: $(curl -s -o /dev/null -w "%{http_code}" http://localhost:9000/minio/health/live)"

echo ""
echo "✅ Services started successfully!"
echo ""
echo "📋 Available services:"
echo "   🗄️  PostgreSQL: localhost:5432"
echo "   🗃️  Redis: localhost:6379"
echo "   📁 MinIO: localhost:9000 (admin: localhost:9001)"
echo "   🔧 Celery Flower: localhost:5555"
echo ""
echo "🎯 Next steps:"
echo "   1. Start Celery worker: cd backend && celery -A app.worker:celery_app worker --loglevel=info --queues=ai_analysis,external_tools,consolidation"
echo "   2. Start FastAPI backend: cd backend && uvicorn app.main:app --reload --port 8000"
echo "   3. Start Next.js frontend: cd frontend && npm run dev"
echo ""
echo "📊 Monitor Celery workers at: http://localhost:5555"