FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Copy Poetry files
COPY backend/pyproject.toml backend/poetry.lock* ./

# Configure Poetry
RUN poetry config virtualenvs.create false

# Install dependencies
RUN poetry install --no-dev

# Copy application code
COPY backend/app /app

# Install Playwright browsers
RUN playwright install --with-deps chromium

# Run Celery worker
CMD ["celery", "-A", "app.worker", "worker", "--loglevel=info", "--queues=ai_analysis,external_tools,consolidation"]