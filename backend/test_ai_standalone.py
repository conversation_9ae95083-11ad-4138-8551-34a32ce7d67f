"""
Standalone test for AI service mixture-of-agents functionality
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.ai_service import AIService, AIProvider

async def test_mixture_of_agents():
    """Test the mixture-of-agents functionality"""
    print("🤖 Testing AI Service - Mixture of Agents Implementation")
    print("=" * 60)
    
    # Initialize AI service
    ai_service = AIService()
    
    # Check available providers
    available_providers = ai_service._get_available_providers()
    print(f"Available providers: {[p.value for p in available_providers]}")
    
    if not available_providers:
        print("❌ No AI providers configured. Please check your .env file.")
        return
    
    # Test mixture-of-agents if available
    if AIProvider.MIXTURE_OF_AGENTS in available_providers:
        print(f"\n✅ Mixture-of-agents is available!")
        print(f"✅ Required providers detected: {len([p for p in available_providers if p != AIProvider.MIXTURE_OF_AGENTS])}")
        
        try:
            print("\n🧪 Testing mixture-of-agents with a simple query...")
            test_prompt = """
            Analyze this business idea:
            Title: AI-powered Personal Finance Assistant
            Description: A mobile app that uses AI to analyze spending patterns, provide personalized financial advice, and help users achieve their savings goals through intelligent recommendations.
            
            Please provide a brief analysis in JSON format with:
            {"viability": "high/medium/low", "key_strengths": [], "potential_challenges": []}
            """
            
            print("⏳ Calling mixture-of-agents...")
            result = await ai_service._call_ai_provider(
                prompt=test_prompt,
                provider=AIProvider.MIXTURE_OF_AGENTS,
                max_tokens=500,
                use_external_tools=False  # Disable external tools for this test
            )
            
            print(f"\n✅ Success! Mixture-of-agents response received:")
            print("-" * 40)
            print(result[:500] + "..." if len(result) > 500 else result)
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ Error testing mixture-of-agents: {str(e)}")
            
    else:
        print(f"\n⚠️  Mixture-of-agents not available.")
        print(f"Requires at least 2 AI providers + Anthropic for consolidation")
        
        # Test individual providers
        for provider in available_providers:
            if provider != AIProvider.MIXTURE_OF_AGENTS:
                print(f"\n🧪 Testing {provider.value}...")
                try:
                    result = await ai_service._call_ai_provider(
                        prompt="Say hello and confirm you're working",
                        provider=provider,
                        max_tokens=50
                    )
                    print(f"✅ {provider.value}: {result[:100]}...")
                except Exception as e:
                    print(f"❌ {provider.value}: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_mixture_of_agents())