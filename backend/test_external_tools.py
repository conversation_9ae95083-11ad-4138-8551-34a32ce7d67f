"""
Test external tools integration (<PERSON><PERSON>, Firecrawl)
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.ai_service import AIService, AIProvider

async def test_external_tools():
    """Test Tavily and Firecrawl integration"""
    print("🔧 Testing External Tools Integration")
    print("=" * 50)
    
    # Initialize AI service
    ai_service = AIService()
    
    # Test Tavily search
    print("\n🔍 Testing Tavily Search Integration...")
    if ai_service.tavily_client:
        try:
            search_result = await ai_service._search_with_tavily("AI fitness apps market trends 2024", max_results=3)
            print(f"✅ Tavily search successful!")
            print(f"📊 Results count: {len(search_result.get('results', []))}")
            
            if search_result.get('results'):
                for i, result in enumerate(search_result['results'][:2], 1):
                    print(f"  {i}. {result.get('title', 'No title')[:60]}...")
                    print(f"     URL: {result.get('url', 'No URL')}")
            
        except Exception as e:
            print(f"❌ Tavily search failed: {str(e)}")
    else:
        print("⚠️  Tavily client not configured (check TAVILY_API_KEY)")
    
    # Test Firecrawl scraping
    print("\n🕸️  Testing Firecrawl Scraping Integration...")
    if ai_service.firecrawl_client:
        try:
            test_urls = ["https://example.com"]
            scrape_result = await ai_service._scrape_with_firecrawl(test_urls)
            print(f"✅ Firecrawl scraping successful!")
            print(f"📄 Scraped {len(scrape_result)} URLs")
            
            for result in scrape_result:
                status = "✅ Success" if not result.get('error') else f"❌ Error: {result.get('error')}"
                print(f"  URL: {result['url']} - {status}")
                
        except Exception as e:
            print(f"❌ Firecrawl scraping failed: {str(e)}")
    else:
        print("⚠️  Firecrawl client not configured (check FIRECRAWL_API_KEY)")
    
    # Test mixture-of-agents with external tools enabled
    print("\n🤖 Testing Mixture-of-Agents WITH External Tools...")
    available_providers = ai_service._get_available_providers()
    
    if AIProvider.MIXTURE_OF_AGENTS in available_providers:
        try:
            test_prompt = """
            Analyze the market opportunity for:
            Title: AI-powered Fitness Coach App
            Description: A mobile app that uses AI to create personalized workout plans and track progress
            
            Provide market analysis in JSON format with:
            {"market_size": "estimate", "key_trends": [], "opportunities": []}
            """
            
            print("⏳ Running mixture-of-agents with external tools...")
            result = await ai_service._call_ai_provider(
                prompt=test_prompt,
                provider=AIProvider.MIXTURE_OF_AGENTS,
                max_tokens=800,
                use_external_tools=True  # Enable external tools
            )
            
            print(f"✅ Success! Enhanced analysis with external data:")
            print("-" * 40)
            print(result[:500] + "..." if len(result) > 500 else result)
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ Error testing enhanced mixture-of-agents: {str(e)}")
    else:
        print("⚠️  Mixture-of-agents not available")
    
    print("\n📋 External Tools Integration Summary:")
    print(f"  🔍 Tavily: {'✅ Available' if ai_service.tavily_client else '❌ Not configured'}")
    print(f"  🕸️  Firecrawl: {'✅ Available' if ai_service.firecrawl_client else '❌ Not configured'}")
    print(f"  🤖 Enhanced AI: {'✅ Ready' if ai_service.tavily_client or ai_service.firecrawl_client else '❌ Limited'}")

if __name__ == "__main__":
    asyncio.run(test_external_tools())