# Incepta Backend API

FastAPI-based backend for the Incepta idea validation platform.

## Project Structure

```
backend/
├── app/                    # Main application code
│   ├── api/               # API routes and endpoints
│   │   ├── deps/         # Dependency injection modules
│   │   └── v1/           # API version 1
│   ├── core/             # Core configuration and utilities
│   ├── models/           # SQLAlchemy database models
│   ├── schemas/          # Pydantic schemas for API
│   └── services/         # Business logic and external integrations
├── alembic/              # Database migrations
├── pyproject.toml        # Poetry dependencies and project config
└── alembic.ini          # Alembic configuration
```

## Quick Start

1. **Install dependencies:**
   ```bash
   cd backend
   poetry install
   ```

2. **Set up environment:**
   ```bash
   cp ../.env.example ../.env
   # Edit .env with your configuration
   ```

3. **Run database migrations:**
   ```bash
   poetry run alembic upgrade head
   ```

4. **Start the development server:**
   ```bash
   poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## API Documentation

Once running, visit:
- **Interactive docs:** http://localhost:8000/api/docs
- **ReDoc:** http://localhost:8000/api/redoc

## Development Commands

```bash
# Install dependencies
poetry install

# Run development server
poetry run uvicorn app.main:app --reload

# Run Celery worker
poetry run celery -A app.services.celery_app worker --loglevel=info

# Database migrations
poetry run alembic revision --autogenerate -m "Description"
poetry run alembic upgrade head

# Code formatting
poetry run black app/
poetry run isort app/

# Type checking
poetry run mypy app/

# Tests
poetry run pytest
```