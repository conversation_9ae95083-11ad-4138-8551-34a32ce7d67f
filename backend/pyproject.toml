[tool.poetry]
name = "incepta-backend"
version = "0.1.0"
description = "AI-powered idea validation platform - Backend API"
authors = ["Incepta Team"]
readme = "README.md"
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.23"
alembic = "^1.12.1"
psycopg2-binary = "^2.9.9"
redis = "^5.0.1"
celery = "^5.3.4"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
pydantic-settings = "^2.0.3"
pydantic = {extras = ["email"], version = "^2.0.0"}
httpx = "^0.25.2"
openai = "^1.3.7"
anthropic = "^0.37.0"
python-dotenv = "^1.0.0"
playwright = "^1.40.0"
minio = "^7.2.0"
tavily-python = "^0.3.3"
firecrawl-py = "^0.0.5"
google-generativeai = "^0.8.5"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true