INFO:     Will watch for changes in these directories: ['/Users/<USER>/repos/incepta-py/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [33875] using WatchFiles
INFO:     Started server process [33895]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:54841 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:55031 - "POST /api/v1/auth/register HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:55229 - "POST /api/v1/auth/register HTTP/1.1" 422 Unprocessable Entity
(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     127.0.0.1:55385 - "POST /api/v1/auth/register HTTP/1.1" 200 OK
INFO:     127.0.0.1:55598 - "POST /api/v1/auth/register HTTP/1.1" 200 OK
INFO:     127.0.0.1:55754 - "POST /api/v1/auth/login HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:56017 - "POST /api/v1/auth/login HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:56192 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:56435 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.1JzfUNtvXbX3VDqLsinz-mMYC43p98c-b3ROA421ZGI&idea=AI%20powered%20symptom%20checker%20app&answers=%7B%22target_users%22%3A%22Healthcare%20consumers%22%7D&use_celery=false HTTP/1.1" 200 OK
🔄 SSE Stream: Top-level stream error: cannot access local variable 'json' where it is not associated with a value
🔄 SSE Stream: Full top-level error traceback:
Traceback (most recent call last):
  File "/Users/<USER>/repos/incepta-py/backend/app/api/v1/endpoints/validation.py", line 401, in generate_stream
    yield f"data: {json.dumps({'type': 'status', 'message': 'Validation stream connected'})}\n\n"
                   ^^^^
UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/routing.py", line 69, in app
    await response(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/responses.py", line 270, in __call__
    async with anyio.create_task_group() as task_group:
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 597, in __aexit__
    raise exceptions[0]
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/responses.py", line 273, in wrap
    await func()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/responses.py", line 262, in stream_response
    async for chunk in self.body_iterator:
  File "/Users/<USER>/repos/incepta-py/backend/app/api/v1/endpoints/validation.py", line 511, in generate_stream
    yield f"data: {json.dumps({'type': 'close', 'message': 'Stream ended'})}\n\n"
                   ^^^^
UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/validation.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [33895]
INFO:     Started server process [35450]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:56947 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.1JzfUNtvXbX3VDqLsinz-mMYC43p98c-b3ROA421ZGI&idea=AI%20powered%20symptom%20checker%20app&answers=%7B%22target_users%22%3A%22Healthcare%20consumers%22%7D&use_celery=false HTTP/1.1" 200 OK
🔄 SSE Stream: Top-level stream error: cannot access local variable 'json' where it is not associated with a value
🔄 SSE Stream: Full top-level error traceback:
Traceback (most recent call last):
  File "/Users/<USER>/repos/incepta-py/backend/app/api/v1/endpoints/validation.py", line 401, in generate_stream
    yield f"data: {json.dumps({'type': 'status', 'message': 'Validation stream connected'})}\n\n"
                   ^^^^
UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/routing.py", line 69, in app
    await response(scope, receive, send)
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/responses.py", line 270, in __call__
    async with anyio.create_task_group() as task_group:
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/anyio/_backends/_asyncio.py", line 597, in __aexit__
    raise exceptions[0]
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/responses.py", line 273, in wrap
    await func()
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/starlette/responses.py", line 262, in stream_response
    async for chunk in self.body_iterator:
  File "/Users/<USER>/repos/incepta-py/backend/app/api/v1/endpoints/validation.py", line 510, in generate_stream
    yield f"data: {json.dumps({'type': 'close', 'message': 'Stream ended'})}\n\n"
                   ^^^^
UnboundLocalError: cannot access local variable 'json' where it is not associated with a value
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/validation.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [35450]
INFO:     Started server process [35728]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:57420 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.1JzfUNtvXbX3VDqLsinz-mMYC43p98c-b3ROA421ZGI&idea=AI%20powered%20symptom%20checker%20app&answers=%7B%22target_users%22%3A%22Healthcare%20consumers%22%7D&use_celery=false HTTP/1.1" 200 OK
INFO:     127.0.0.1:58049 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.1JzfUNtvXbX3VDqLsinz-mMYC43p98c-b3ROA421ZGI&idea=AI%20powered%20symptom%20checker%20app&answers=%7B%22target_users%22%3A%22Healthcare%20consumers%22%7D&use_celery=false HTTP/1.1" 200 OK
INFO:     127.0.0.1:58739 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.1JzfUNtvXbX3VDqLsinz-mMYC43p98c-b3ROA421ZGI&idea=Mobile%20fitness%20app&answers=%7B%22target_users%22%3A%22Fitness%20enthusiasts%22%7D&use_celery=false HTTP/1.1" 200 OK
INFO:     127.0.0.1:63419 - "OPTIONS /api/v1/validation/test-sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:63419 - "GET /api/v1/validation/test-sse HTTP/1.1" 200 OK
INFO:     127.0.0.1:50657 - "OPTIONS /api/v1/auth/login HTTP/1.1" 200 OK
(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     127.0.0.1:50657 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:50775 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51459 - "OPTIONS /api/v1/auth/register HTTP/1.1" 200 OK
INFO:     127.0.0.1:51459 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:51459 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:52448 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:     127.0.0.1:52448 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:52547 - "OPTIONS /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.NebU4afHufsRLGXJYos6VNSN6h3eUSid9nIEM-hdBwk&idea=Test+AI+application+for+symptom+checking&answers=%7B%22target_users%22%3A%22Healthcare+consumers%22%2C%22problem_solved%22%3A%22Quick+symptom+assessment%22%2C%22unique_value%22%3A%22AI-powered+analysis%22%7D&use_celery=false HTTP/1.1" 200 OK
INFO:     127.0.0.1:52547 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.NebU4afHufsRLGXJYos6VNSN6h3eUSid9nIEM-hdBwk&idea=Test+AI+application+for+symptom+checking&answers=%7B%22target_users%22%3A%22Healthcare+consumers%22%2C%22problem_solved%22%3A%22Quick+symptom+assessment%22%2C%22unique_value%22%3A%22AI-powered+analysis%22%7D&use_celery=false HTTP/1.1" 200 OK
INFO:     127.0.0.1:53279 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:53279 - "OPTIONS /api/v1/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:53279 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
INFO:     127.0.0.1:53412 - "OPTIONS /api/v1/validation/generate-questions HTTP/1.1" 200 OK
INFO:     127.0.0.1:53412 - "POST /api/v1/validation/generate-questions HTTP/1.1" 200 OK
INFO:     127.0.0.1:53991 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.d45n1pSn3daj5HeAyeVu4ljmQpBCHbHNTDGg9WFVZrk&idea=AI+powered+solution+to+increase+the+performance+of+a+fund+manager&answers=%7B%22geographic_scope%22%3A%22India%22%2C%22tech_involvement%22%3A%22Web+Application%22%2C%22dynamic_question_1%22%3A%22VC+Firms%22%2C%22dynamic_question_2%22%3A%22pay+per+use%22%2C%22dynamic_question_3%22%3A%22risk+reduction%22%7D&use_celery=true HTTP/1.1" 200 OK
INFO:     127.0.0.1:53991 - "OPTIONS /api/v1/validation/reports HTTP/1.1" 200 OK
Error creating validation report: (psycopg2.errors.NotNullViolation) null value in column "idea_validation_id" of relation "validation_reports" violates not-null constraint
DETAIL:  Failing row contains (455337568, null, 5, AI powered solution to increase the performance of a fund manage..., AI powered solution to increase the performance of a fund manage..., {"geographic_scope": "India", "tech_involvement": "Web Applicati..., {"validation_summary": {"overall_score": 7.5, "recommendation": ..., f, null, [], {"completed_at": "2025-07-02T18:50:45.650Z", "agents_count": 0, ..., 2025-07-02 18:50:45.657421+00, null).

[SQL: INSERT INTO validation_reports (id, idea_validation_id, user_id, title, description, user_answers, report_data, is_public, share_token, agent_activities, processing_metadata, updated_at) VALUES (%(id)s, %(idea_validation_id)s, %(user_id)s, %(title)s, %(description)s, %(user_answers)s::JSON, %(report_data)s::JSON, %(is_public)s, %(share_token)s, %(agent_activities)s::JSON, %(processing_metadata)s::JSON, %(updated_at)s) RETURNING validation_reports.created_at]
[parameters: {'id': '455337568', 'idea_validation_id': None, 'user_id': 5, 'title': 'AI powered solution to increase the performance of a fund manager', 'description': 'AI powered solution to increase the performance of a fund manager', 'user_answers': '{"geographic_scope": "India", "tech_involvement": "Web Application", "dynamic_question_1": "VC Firms", "dynamic_question_2": "pay per use", "dynamic_question_3": "risk reduction"}', 'report_data': '{"validation_summary": {"overall_score": 7.5, "recommendation": "Promising opportunity", "confidence_level": "Medium", "key_strengths": ["AI-powered  ... (4633 characters truncated) ...  simplified analysis focused on market opportunity and technical feasibility.", "providers_used": ["OpenAI", "Anthropic"], "external_tools_used": []}', 'is_public': False, 'share_token': None, 'agent_activities': '[]', 'processing_metadata': '{"completed_at": "2025-07-02T18:50:45.650Z", "agents_count": 0, "idea_length": 65}', 'updated_at': None}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
INFO:     127.0.0.1:53991 - "POST /api/v1/validation/reports HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:53991 - "OPTIONS /api/v1/validation/reports/1751482245683 HTTP/1.1" 200 OK
INFO:     127.0.0.1:53991 - "GET /api/v1/validation/reports/1751482245683 HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'alembic/versions/c9d8d39d9c91_make_idea_validation_id_nullable.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [35728]
INFO:     Started server process [50709]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'alembic/versions/c9d8d39d9c91_make_idea_validation_id_nullable.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [50709]
INFO:     Started server process [50898]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [50898]
INFO:     Started server process [51312]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [51312]
INFO:     Started server process [51434]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [51434]
INFO:     Started server process [51763]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [51763]
INFO:     Started server process [52111]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [52111]
INFO:     Started server process [52702]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [52702]
INFO:     Started server process [55471]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55471]
INFO:     Started server process [55661]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55661]
INFO:     Started server process [56289]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/services/ai_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [56289]
INFO:     Started server process [56719]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     127.0.0.1:51310 - "GET /api/v1/validation/test-sse HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/validation.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [56719]
INFO:     Started server process [4820]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'app/api/v1/endpoints/validation.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [4820]
INFO:     Started server process [5175]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
Invalid token provided: test-token...
Authentication error in SSE endpoint: 
INFO:     127.0.0.1:52241 - "GET /api/v1/validation/comprehensive-validation-stream?token=test-token&idea=Test%20AI%20app&answers=%7B%22target_users%22%3A%22Healthcare%22%7D&use_celery=false HTTP/1.1" 403 Forbidden
(trapped) error reading bcrypt version
Traceback (most recent call last):
  File "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/incepta-backend--KpDPzh5-py3.11/lib/python3.11/site-packages/passlib/handlers/bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
INFO:     127.0.0.1:53741 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:53749 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************.iOMjHSrYeAkMcMIQDhMSivY0Ff-egFfw_hlzxvzuUpY&idea=AI%20powered%20fitness%20app&answers=%7B%22target_users%22%3A%22Fitness%20enthusiasts%22%7D&use_celery=false HTTP/1.1" 200 OK
Firecrawl scrape error for https://simplicable.com/new/project-concept: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for http://ndl.ethernet.edu.et/bitstream/123456789/90290/1/Chapter%201.pdf: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://nielseniq.com/global/en/info/target-audience/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://www.userlytics.com/resources/blog/target-for-user-research/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://hogonext.com/how-to-analyze-power-system-data-for-informed-decision-making/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://www.engineeringpowersolutions.co.uk/a-step-by-step-guide-to-electrical-power-system-studies/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://www.comparables.ai/articles/role-of-big-data-in-market-analysis: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://aitools.inc/tasks/analyze-market-trends: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://hingemarketing.com/blog/story/target-market-research-for-the-professional-services: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://study.com/academy/lesson/target-market-analysis-definition-examples.html: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://ericmelillo.com/how-to-write-a-value-proposition/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://corporatefinanceinstitute.com/resources/management/value-proposition/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://verticalresponse.com/blog/top-strategies-to-monetize-your-content-effectively/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://productschool.com/blog/product-strategy/monetization-strategy: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
INFO:     127.0.0.1:58889 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:58896 - "GET /api/v1/validation/comprehensive-validation-stream?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTE0ODk2NDIsInN1YiI6IjYifQ.az4BDY4sgB5_1pHv-gPQACEtAW0NAnZZ74iVte-s1NM&idea=AI%20fitness%20app&answers=%7B%22target_users%22%3A%22Fitness%20enthusiasts%22%7D&use_celery=false HTTP/1.1" 200 OK
Firecrawl scrape error for https://simplicable.com/new/project-concept: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for http://ndl.ethernet.edu.et/bitstream/123456789/90290/1/Chapter%201.pdf: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://nielseniq.com/global/en/info/target-audience/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://www.userlytics.com/resources/blog/target-for-user-research/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://wellyx.com/blog/top-components-of-fitness-assessment/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://imhealth-erasmus.com/wp-content/uploads/2024/10/Collection-of-fitness-assessment-methods_ENG.pdf: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://www.exercise.com/grow/how-to-choose-a-target-market-for-your-gym-business/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://kimola.com/blog/complete-market-research-guide-for-fitness-centers: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://hingemarketing.com/blog/story/target-market-research-for-the-professional-services: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://study.com/academy/lesson/target-market-analysis-definition-examples.html: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://ericmelillo.com/how-to-write-a-value-proposition/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://corporatefinanceinstitute.com/resources/management/value-proposition/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://verticalresponse.com/blog/top-strategies-to-monetize-your-content-effectively/: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
Firecrawl scrape error for https://productschool.com/blog/product-strategy/monetization-strategy: Failed to scrape URL. Status code: 402. Error: Insufficient credits. For more credits, you can upgrade your plan at https://firecrawl.dev/pricing
