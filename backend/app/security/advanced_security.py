"""
Advanced security layer for validation system
"""

import hashlib
import hmac
import time
import json
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict
import re
import logging
from cryptography.fernet import Fernet
import jwt
from fastapi import HTTPException, Request
import redis

logger = logging.getLogger(__name__)

@dataclass
class SecurityEvent:
    """Security event for monitoring"""
    event_type: str
    user_id: str
    ip_address: str
    timestamp: datetime
    details: Dict[str, Any]
    severity: str  # low, medium, high, critical

class RateLimiter:
    """Advanced rate limiting with multiple strategies"""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client or redis.Redis(host='localhost', port=6379, db=3)
        self.limits = {
            'validation_requests': {'count': 10, 'window': 3600},  # 10 per hour
            'api_calls': {'count': 100, 'window': 3600},           # 100 per hour
            'login_attempts': {'count': 5, 'window': 900},         # 5 per 15 min
            'password_resets': {'count': 3, 'window': 3600}        # 3 per hour
        }
    
    async def check_rate_limit(self, user_id: str, action: str, 
                              ip_address: str = None) -> Tuple[bool, Dict[str, Any]]:
        """Check if action is within rate limits"""
        if action not in self.limits:
            return True, {}
        
        limit_config = self.limits[action]
        key = f"rate_limit:{action}:{user_id}"
        
        # Get current count
        current_count = int(self.redis.get(key) or 0)
        
        # Check limit
        if current_count >= limit_config['count']:
            ttl = self.redis.ttl(key)
            return False, {
                'limit_exceeded': True,
                'current_count': current_count,
                'limit': limit_config['count'],
                'reset_in': ttl,
                'action': action
            }
        
        # Increment counter
        pipe = self.redis.pipeline()
        pipe.incr(key)
        pipe.expire(key, limit_config['window'])
        pipe.execute()
        
        return True, {
            'limit_exceeded': False,
            'current_count': current_count + 1,
            'limit': limit_config['count'],
            'remaining': limit_config['count'] - current_count - 1
        }

class ContentValidator:
    """Validate and sanitize user content"""
    
    def __init__(self):
        # Patterns for potentially malicious content
        self.suspicious_patterns = [
            r'<script[^>]*>.*?</script>',  # Script tags
            r'javascript:',                # JavaScript URLs
            r'on\w+\s*=',                 # Event handlers
            r'eval\s*\(',                 # eval() calls
            r'document\.',                # DOM manipulation
            r'window\.',                  # Window object access
        ]
        
        # Patterns for spam/abuse
        self.spam_patterns = [
            r'(https?://[^\s]+){3,}',     # Multiple URLs
            r'(.)\1{10,}',                # Repeated characters
            r'[A-Z]{20,}',                # Excessive caps
        ]
    
    def validate_idea(self, idea: str) -> Tuple[bool, List[str]]:
        """Validate idea content for security and quality"""
        issues = []
        
        # Length checks
        if len(idea) < 10:
            issues.append("Idea too short (minimum 10 characters)")
        elif len(idea) > 5000:
            issues.append("Idea too long (maximum 5000 characters)")
        
        # Security checks
        for pattern in self.suspicious_patterns:
            if re.search(pattern, idea, re.IGNORECASE):
                issues.append("Potentially malicious content detected")
                logger.warning(f"Suspicious pattern detected: {pattern}")
                break
        
        # Spam checks
        for pattern in self.spam_patterns:
            if re.search(pattern, idea, re.IGNORECASE):
                issues.append("Content appears to be spam")
                break
        
        # Language detection (basic)
        if not self._contains_meaningful_content(idea):
            issues.append("Content does not appear to contain meaningful text")
        
        return len(issues) == 0, issues
    
    def _contains_meaningful_content(self, text: str) -> bool:
        """Check if text contains meaningful content"""
        # Remove special characters and count words
        words = re.findall(r'\b[a-zA-Z]{2,}\b', text)
        return len(words) >= 5  # At least 5 meaningful words

class AnomalyDetector:
    """Detect anomalous behavior patterns"""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client or redis.Redis(host='localhost', port=6379, db=4)
        self.user_patterns = defaultdict(list)
    
    async def analyze_user_behavior(self, user_id: str, action: str, 
                                  metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze user behavior for anomalies"""
        key = f"behavior:{user_id}"
        
        # Get recent actions
        recent_actions = self.redis.lrange(key, 0, 99)  # Last 100 actions
        
        # Add current action
        action_data = {
            'action': action,
            'timestamp': time.time(),
            'metadata': metadata
        }
        
        self.redis.lpush(key, json.dumps(action_data))
        self.redis.ltrim(key, 0, 99)  # Keep only last 100
        self.redis.expire(key, 86400 * 7)  # 7 days TTL
        
        # Analyze patterns
        anomalies = []
        
        if len(recent_actions) >= 10:
            # Check for rapid requests
            recent_timestamps = []
            for action_json in recent_actions[:10]:
                action_obj = json.loads(action_json)
                recent_timestamps.append(action_obj['timestamp'])
            
            # Check if last 10 actions were within 5 minutes
            if recent_timestamps[0] - recent_timestamps[9] < 300:
                anomalies.append({
                    'type': 'rapid_requests',
                    'severity': 'medium',
                    'description': 'Unusually rapid request pattern detected'
                })
        
        # Check for unusual timing patterns
        current_hour = datetime.now().hour
        if current_hour < 6 or current_hour > 23:  # Late night activity
            anomalies.append({
                'type': 'unusual_timing',
                'severity': 'low',
                'description': 'Activity during unusual hours'
            })
        
        return {
            'anomalies': anomalies,
            'risk_score': len(anomalies) * 0.3,  # Simple scoring
            'action_count': len(recent_actions)
        }

class EncryptionManager:
    """Manage encryption for sensitive data"""
    
    def __init__(self, key: bytes = None):
        self.key = key or Fernet.generate_key()
        self.cipher = Fernet(self.key)
    
    def encrypt_sensitive_data(self, data: Dict[str, Any]) -> str:
        """Encrypt sensitive user data"""
        json_data = json.dumps(data)
        encrypted = self.cipher.encrypt(json_data.encode())
        return encrypted.decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> Dict[str, Any]:
        """Decrypt sensitive user data"""
        decrypted = self.cipher.decrypt(encrypted_data.encode())
        return json.loads(decrypted.decode())
    
    def hash_pii(self, data: str) -> str:
        """Create irreversible hash of PII for analytics"""
        return hashlib.sha256(data.encode()).hexdigest()

class SecurityMonitor:
    """Monitor and respond to security events"""
    
    def __init__(self):
        self.events = []
        self.alert_thresholds = {
            'failed_logins': 5,
            'rate_limit_exceeded': 3,
            'suspicious_content': 1,
            'anomalous_behavior': 3
        }
    
    async def log_security_event(self, event: SecurityEvent):
        """Log security event and check for alerts"""
        self.events.append(event)
        
        # Keep only last 1000 events in memory
        if len(self.events) > 1000:
            self.events = self.events[-1000:]
        
        # Check for alert conditions
        await self._check_alert_conditions(event)
        
        logger.info(f"Security event: {event.event_type} for user {event.user_id}")
    
    async def _check_alert_conditions(self, event: SecurityEvent):
        """Check if event triggers any alerts"""
        # Count recent events of same type for same user
        recent_events = [
            e for e in self.events[-50:]  # Last 50 events
            if (e.user_id == event.user_id and 
                e.event_type == event.event_type and
                (datetime.now() - e.timestamp).total_seconds() < 3600)  # Last hour
        ]
        
        threshold = self.alert_thresholds.get(event.event_type, 10)
        
        if len(recent_events) >= threshold:
            await self._trigger_alert(event, len(recent_events))
    
    async def _trigger_alert(self, event: SecurityEvent, count: int):
        """Trigger security alert"""
        alert = {
            'type': 'security_alert',
            'event_type': event.event_type,
            'user_id': event.user_id,
            'count': count,
            'severity': event.severity,
            'timestamp': datetime.now().isoformat()
        }
        
        logger.warning(f"🚨 Security Alert: {alert}")
        
        # Here you would integrate with alerting systems
        # - Send to Slack/Discord
        # - Email security team
        # - Create incident ticket
        # - Temporarily block user if critical

# Global security instances
rate_limiter = RateLimiter()
content_validator = ContentValidator()
anomaly_detector = AnomalyDetector()
encryption_manager = EncryptionManager()
security_monitor = SecurityMonitor()
