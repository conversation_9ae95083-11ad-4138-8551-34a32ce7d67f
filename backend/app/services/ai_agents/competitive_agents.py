"""
Competitive analysis specialized agents
"""

from typing import Dict, Any
import logging
from .base import BaseAgent
from app.services.ai_providers import AIProvider

logger = logging.getLogger(__name__)


class CompetitorsAnalysisAgent(BaseAgent):
    """Competitive Intelligence Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Competitive Intelligence",
            title="Competitive Intelligence Specialist",
            background="Expert in competitor analysis and market positioning with focus on strategic advantages.",
            icon="🏆",
            preferred_provider=AIProvider.ANTHROPIC
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate competitors analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Competitive Intelligence Specialist with expertise in competitor analysis and market positioning.

TASK: Analyze competitive landscape for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive competitive analysis in JSON format:
{{
    "direct_competitors": [
        {{
            "name": "Competitor name",
            "description": "What they do",
            "market_share": "X%",
            "strengths": ["strength1", "strength2"],
            "weaknesses": ["weakness1", "weakness2"],
            "pricing": "$X/month",
            "target_market": "Their target audience"
        }}
    ],
    "indirect_competitors": [
        {{
            "name": "Competitor name",
            "category": "Alternative solution type",
            "threat_level": "High/Medium/Low"
        }}
    ],
    "competitive_positioning": {{
        "our_advantages": ["advantage1", "advantage2"],
        "our_disadvantages": ["disadvantage1", "disadvantage2"],
        "differentiation_strategy": "How to differentiate",
        "positioning_statement": "Our unique position"
    }},
    "market_gaps": {{
        "unmet_needs": ["need1", "need2"],
        "underserved_segments": ["segment1", "segment2"],
        "opportunity_areas": ["area1", "area2"]
    }}
}}

Use external research to validate competitor information.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform competitors analysis"""
        try:
            # Get external research
            external_research = await self.research_with_external_tools(idea, answers)
            
            prompt = self.get_analysis_prompt(idea, answers)
            if external_research:
                prompt += f"\n\nExternal Research:\n{external_research}"
            
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            tools_used = ["Anthropic"]
            if external_research:
                tools_used.extend(self.tools_manager.get_available_tools())
            
            return self.create_success_response(analysis, tools_used)
            
        except Exception as e:
            logger.error(f"Competitors analysis failed: {str(e)}")
            return self.create_error_response("Competitive analysis unavailable")


class OpportunitiesAnalysisAgent(BaseAgent):
    """Market Opportunities Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Market Opportunities Specialist",
            title="Market Opportunities Specialist",
            background="Expert in growth potential assessment and expansion strategy.",
            icon="🚀",
            preferred_provider=AIProvider.OPENAI
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate opportunities analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Market Opportunities Specialist with expertise in growth potential assessment and expansion strategy.

TASK: Identify market opportunities for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive opportunities analysis in JSON format:
{{
    "growth_opportunities": {{
        "short_term": [
            {{
                "opportunity": "Opportunity description",
                "potential_impact": "High/Medium/Low",
                "timeline": "X months",
                "requirements": ["requirement1", "requirement2"]
            }}
        ],
        "long_term": [
            {{
                "opportunity": "Opportunity description",
                "potential_impact": "High/Medium/Low",
                "timeline": "X years",
                "strategic_value": "Strategic importance"
            }}
        ]
    }},
    "market_expansion": {{
        "geographic_expansion": [
            {{
                "market": "Market name",
                "opportunity_size": "$X million",
                "entry_barriers": ["barrier1", "barrier2"],
                "entry_strategy": "How to enter"
            }}
        ],
        "vertical_expansion": [
            {{
                "vertical": "Industry/sector",
                "market_size": "$X million",
                "adaptation_required": ["change1", "change2"]
            }}
        ]
    }},
    "partnership_opportunities": [
        {{
            "partner_type": "Type of partner",
            "value_proposition": "What we offer",
            "expected_benefit": "What we gain",
            "partnership_model": "How partnership works"
        }}
    ],
    "innovation_opportunities": [
        {{
            "innovation_area": "Technology/Process/Business model",
            "description": "Innovation description",
            "competitive_advantage": "Advantage gained",
            "investment_required": "$X"
        }}
    ]
}}

Focus on realistic, actionable opportunities with clear value propositions.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform opportunities analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["OpenAI"])
            
        except Exception as e:
            logger.error(f"Opportunities analysis failed: {str(e)}")
            return self.create_error_response("Market opportunities analysis unavailable")


class ChallengesAnalysisAgent(BaseAgent):
    """Market Challenges Analyst"""
    
    def __init__(self):
        super().__init__(
            name="Market Challenges Analyst",
            title="Market Challenges Analyst",
            background="Expert in identifying barriers, risks, and obstacles to market success.",
            icon="⚡",
            preferred_provider=AIProvider.DEEPSEEK
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate challenges analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Market Challenges Analyst with expertise in identifying barriers, risks, and obstacles to market success.

TASK: Identify market challenges for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive challenges analysis in JSON format:
{{
    "market_barriers": {{
        "entry_barriers": [
            {{
                "barrier": "Barrier description",
                "severity": "High/Medium/Low",
                "mitigation_strategy": "How to overcome",
                "timeline_to_overcome": "X months"
            }}
        ],
        "scaling_barriers": [
            {{
                "barrier": "Barrier description",
                "impact_on_growth": "How it affects scaling",
                "mitigation_approach": "Strategy to address"
            }}
        ]
    }},
    "competitive_challenges": {{
        "established_players": [
            {{
                "competitor": "Competitor name",
                "challenge": "Specific challenge they pose",
                "competitive_response": "How to compete"
            }}
        ],
        "market_saturation": {{
            "saturation_level": "High/Medium/Low",
            "differentiation_requirements": ["requirement1", "requirement2"]
        }}
    }},
    "operational_challenges": {{
        "resource_constraints": ["constraint1", "constraint2"],
        "skill_gaps": ["gap1", "gap2"],
        "infrastructure_needs": ["need1", "need2"],
        "regulatory_hurdles": ["hurdle1", "hurdle2"]
    }},
    "customer_adoption_challenges": {{
        "adoption_barriers": ["barrier1", "barrier2"],
        "education_requirements": ["requirement1", "requirement2"],
        "behavior_change_needed": "What users need to change",
        "adoption_timeline": "Expected adoption curve"
    }}
}}

Provide actionable strategies for overcoming each identified challenge.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform challenges analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["Deepseek"])
            
        except Exception as e:
            logger.error(f"Challenges analysis failed: {str(e)}")
            return self.create_error_response("Market challenges analysis unavailable")
