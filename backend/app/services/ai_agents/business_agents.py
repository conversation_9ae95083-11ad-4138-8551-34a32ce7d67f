"""
Business analysis specialized agents
"""

from typing import Dict, Any
import logging
from .base import BaseAgent
from app.services.ai_providers import AIProvider

logger = logging.getLogger(__name__)


class ProjectConceptAgent(BaseAgent):
    """Project Concept Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Project Concept Specialist",
            title="Project Concept Specialist",
            background="Expert in defining project scope and user role modeling with focus on clear value propositions.",
            icon="🎯",
            preferred_provider=AIProvider.OPENAI
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate project concept analysis prompt"""
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Senior Project Concept Specialist with expertise in defining project scope and user role modeling.

TASK: Define comprehensive project concept for: {idea}
Context: {context}

Provide detailed project concept in JSON format:
{{
    "project_overview": {{
        "name": "Project name",
        "tagline": "One-line description",
        "description": "Detailed project description",
        "core_purpose": "Primary purpose and goal",
        "target_outcome": "What success looks like"
    }},
    "user_roles": {{
        "primary_users": [
            {{
                "role": "Role name",
                "description": "Who they are",
                "needs": ["need1", "need2"],
                "goals": ["goal1", "goal2"],
                "pain_points": ["pain1", "pain2"]
            }}
        ],
        "secondary_users": [
            {{
                "role": "Role name", 
                "description": "Who they are",
                "interaction": "How they interact with primary users"
            }}
        ]
    }},
    "value_creation": {{
        "primary_value": "Main value delivered",
        "secondary_values": ["value1", "value2"],
        "value_chain": "How value flows through the system"
    }},
    "stakeholder_map": {{
        "primary_users": ["user type 1", "user type 2"],
        "secondary_users": ["user type 3"],
        "decision_makers": ["decision maker 1"],
        "influencers": ["influencer 1"]
    }}
}}

Use mixture-of-agents approach with external market data for validation.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform project concept analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["OpenAI"])
            
        except Exception as e:
            logger.error(f"Project concept analysis failed: {str(e)}")
            return self.create_error_response("Project concept analysis unavailable")


class ValuePropositionAgent(BaseAgent):
    """Value Proposition Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Value Proposition Specialist",
            title="Value Proposition Specialist", 
            background="Expert in competitive differentiation and unique selling point identification.",
            icon="💎",
            preferred_provider=AIProvider.ANTHROPIC
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate value proposition analysis prompt"""
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Value Proposition Specialist with expertise in competitive differentiation and unique selling point identification.

TASK: Define unique value proposition for: {idea}
Context: {context}

Provide comprehensive value proposition analysis in JSON format:
{{
    "unique_value_proposition": {{
        "headline": "Main value proposition statement",
        "subheadline": "Supporting explanation",
        "key_benefits": ["benefit1", "benefit2", "benefit3"],
        "differentiation": "What makes this unique"
    }},
    "competitive_advantages": {{
        "primary_advantages": [
            {{
                "advantage": "Advantage description",
                "impact": "High/Medium/Low",
                "sustainability": "How sustainable this advantage is"
            }}
        ],
        "secondary_advantages": ["advantage1", "advantage2"]
    }},
    "value_delivery": {{
        "immediate_value": "Value delivered immediately",
        "short_term_value": "Value delivered in weeks/months",
        "long_term_value": "Value delivered over time",
        "value_metrics": ["metric1", "metric2"]
    }},
    "positioning_statement": {{
        "target_audience": "For [target audience]",
        "category": "Who [category/need]",
        "brand_promise": "Our [product] is [unique differentiator]",
        "proof_points": ["proof1", "proof2"]
    }}
}}

Focus on clear, measurable value propositions that resonate with target users.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform value proposition analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["Anthropic"])
            
        except Exception as e:
            logger.error(f"Value proposition analysis failed: {str(e)}")
            return self.create_error_response("Value proposition analysis unavailable")


class MonetizationAgent(BaseAgent):
    """Monetization Strategy Analyst"""
    
    def __init__(self):
        super().__init__(
            name="Monetization Analyst",
            title="Monetization Strategy Analyst",
            background="Expert in revenue model design and pricing optimization with focus on sustainable growth.",
            icon="💰",
            preferred_provider=AIProvider.OPENAI
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate monetization analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Monetization Strategy Analyst with expertise in revenue model design and pricing optimization.

TASK: Design monetization strategy for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive monetization analysis in JSON format:
{{
    "revenue_models": {{
        "primary_model": {{
            "type": "Subscription/Freemium/Transaction/etc",
            "description": "How it works",
            "pricing_structure": "Pricing details",
            "revenue_potential": "$X per user/month",
            "pros": ["pro1", "pro2"],
            "cons": ["con1", "con2"]
        }},
        "secondary_models": [
            {{
                "type": "Model type",
                "description": "How it works",
                "revenue_potential": "$X"
            }}
        ]
    }},
    "pricing_strategy": {{
        "pricing_model": "Freemium/Tiered/Usage-based/etc",
        "price_points": {{
            "basic": "$X/month",
            "premium": "$X/month", 
            "enterprise": "$X/month"
        }},
        "pricing_rationale": "Why these prices",
        "competitive_positioning": "How prices compare to competitors"
    }},
    "monetization_timeline": {{
        "mvp_phase": {{
            "strategy": "Free/Paid/Freemium",
            "focus": "User acquisition/Revenue/Both",
            "expected_revenue": "$X/month"
        }},
        "growth_phase": {{
            "strategy": "Strategy description",
            "new_revenue_streams": ["stream1", "stream2"],
            "expected_revenue": "$X/month"
        }},
        "scale_phase": {{
            "strategy": "Strategy description", 
            "premium_features": ["feature1", "feature2"],
            "expected_revenue": "$X/month"
        }}
    }},
    "unit_economics": {{
        "customer_acquisition_cost": "$X",
        "lifetime_value": "$X",
        "ltv_cac_ratio": "X:1",
        "payback_period": "X months",
        "gross_margin": "X%"
    }}
}}

Focus on sustainable, scalable monetization strategies aligned with user value.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform monetization analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["OpenAI"])
            
        except Exception as e:
            logger.error(f"Monetization analysis failed: {str(e)}")
            return self.create_error_response("Monetization analysis unavailable")
