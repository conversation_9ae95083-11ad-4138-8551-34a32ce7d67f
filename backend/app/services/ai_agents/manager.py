"""
Agent Manager for coordinating specialized validation agents
"""

from typing import Dict, Any, List
import logging
import asyncio
from .market_agents import MarketAnalysisAgent, FinancialAnalysisAgent, RiskAnalysisAgent
from .technical_agents import TechnicalAnalysisAgent, FeaturesAnalysisAgent
from .business_agents import ProjectConceptAgent, ValuePropositionAgent, MonetizationAgent
from .competitive_agents import CompetitorsAnalysisAgent, OpportunitiesAnalysisAgent, ChallengesAnalysisAgent
from .user_agents import TargetUsersAgent, TargetMarketAgent
from .research_agents import CitationsAgent

logger = logging.getLogger(__name__)


class AgentManager:
    """Manager for coordinating specialized validation agents"""
    
    def __init__(self):
        self.agents = self._initialize_agents()
    
    def _initialize_agents(self) -> Dict[str, Any]:
        """Initialize all specialized agents"""
        return {
            # Foundation agents (run first)
            "project_concept": ProjectConceptAgent(),
            "target_users": TargetUsersAgent(),
            "features": FeaturesAnalysisAgent(),
            
            # Market analysis agents
            "market_analysis": MarketAnalysisAgent(),
            "target_market": TargetMarketAgent(),
            "financial_analysis": FinancialAnalysisAgent(),
            
            # Technical analysis agents
            "technical_analysis": TechnicalAnalysisAgent(),
            
            # Business strategy agents
            "value_proposition": ValuePropositionAgent(),
            "monetization": MonetizationAgent(),
            
            # Competitive analysis agents
            "competitors": CompetitorsAnalysisAgent(),
            "opportunities": OpportunitiesAnalysisAgent(),
            "challenges": ChallengesAnalysisAgent(),
            
            # Risk analysis agent
            "risk_analysis": RiskAnalysisAgent(),
            
            # Research and citations agent (run last)
            "citations": CitationsAgent()
        }
    
    def get_agent_execution_phases(self) -> Dict[int, List[str]]:
        """Define agent execution phases for optimal dependency management"""
        return {
            1: ["project_concept"],  # Foundation - must run first
            2: ["target_users", "features", "technical_analysis"],  # Core analysis
            3: ["market_analysis", "target_market", "financial_analysis"],  # Market analysis
            4: ["value_proposition", "monetization"],  # Business strategy
            5: ["competitors", "opportunities", "challenges"],  # Competitive analysis
            6: ["risk_analysis"],  # Risk assessment
            7: ["citations"]  # Research validation - run last
        }
    
    async def run_comprehensive_validation(
        self,
        idea: str,
        answers: Dict[str, Any],
        streaming_callback=None,
        use_celery: bool = False
    ) -> Dict[str, Any]:
        """Run comprehensive validation using all agents in phases"""
        results = {}
        phases = self.get_agent_execution_phases()

        try:
            # Use Celery for parallel processing if enabled
            if use_celery:
                logger.info("🚀 Using Celery for distributed parallel processing of all 11 agents")
                return await self._run_with_celery(idea, answers, streaming_callback)

            # Use asyncio for parallel processing
            logger.info("🚀 Using asyncio for parallel processing of all 11 agents")
            for phase_num, agent_keys in phases.items():
                if streaming_callback:
                    await streaming_callback({
                        "type": "phase_start",
                        "phase": phase_num,
                        "total_phases": len(phases),
                        "agents": [self.agents[key].title for key in agent_keys]
                    })

                # Run agents in current phase in parallel
                phase_tasks = []
                for agent_key in agent_keys:
                    agent = self.agents[agent_key]
                    task = self._run_agent_with_callback(
                        agent, idea, answers, streaming_callback
                    )
                    phase_tasks.append((agent_key, task))
                
                # Wait for all agents in this phase to complete
                for agent_key, task in phase_tasks:
                    try:
                        result = await task
                        results[agent_key] = result
                        
                        if streaming_callback:
                            await streaming_callback({
                                "type": "agent_complete",
                                "agent": self.agents[agent_key].title,
                                "agent_key": agent_key,
                                "success": result.get("success", False),
                                "phase": phase_num
                            })
                    except Exception as e:
                        logger.error(f"Agent {agent_key} failed: {str(e)}")
                        results[agent_key] = {
                            "success": False,
                            "error": str(e),
                            "agent": self.agents[agent_key].title
                        }

                        # Still send agent complete message for failed agents
                        if streaming_callback:
                            await streaming_callback({
                                "type": "agent_complete",
                                "agent": self.agents[agent_key].title,
                                "agent_key": agent_key,
                                "success": False,
                                "error": str(e),
                                "phase": phase_num
                            })
                
                if streaming_callback:
                    await streaming_callback({
                        "type": "phase_complete",
                        "phase": phase_num,
                        "completed_agents": len(agent_keys)
                    })
            
            # Compile final report
            final_report = self._compile_final_report(results)
            
            if streaming_callback:
                await streaming_callback({
                    "type": "validation_complete",
                    "report": final_report
                })
            
            return final_report
            
        except Exception as e:
            logger.error(f"Comprehensive validation failed: {str(e)}")
            if streaming_callback:
                await streaming_callback({
                    "type": "error",
                    "message": f"Validation failed: {str(e)}"
                })
            raise
    
    async def _run_agent_with_callback(
        self,
        agent,
        idea: str,
        answers: Dict[str, Any],
        streaming_callback=None
    ):
        """Run individual agent with streaming callback"""
        if streaming_callback:
            await streaming_callback({
                "type": "agent_start",
                "agent": agent.title,
                "icon": agent.icon
            })

        # Add timeout to prevent hanging agents
        try:
            result = await asyncio.wait_for(agent.analyze(idea, answers), timeout=120.0)  # 2 minute timeout per agent
        except asyncio.TimeoutError:
            logger.error(f"⏰ Agent {agent.title} timed out after 2 minutes")
            result = {
                "error": "Agent analysis timed out",
                "agent": agent.title,
                "status": "timeout"
            }
        except Exception as e:
            logger.error(f"❌ Agent {agent.title} failed with error: {str(e)}")
            result = {
                "error": str(e),
                "agent": agent.title,
                "status": "error"
            }
        return result
    
    def _compile_final_report(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Compile final validation report from all agent results"""
        successful_analyses = {}
        failed_analyses = {}
        
        for agent_key, result in results.items():
            if result.get("success", False):
                successful_analyses[agent_key] = result
            else:
                failed_analyses[agent_key] = result
        
        # Calculate overall validation score
        total_agents = len(results)
        successful_agents = len(successful_analyses)
        validation_score = (successful_agents / total_agents) * 10 if total_agents > 0 else 0
        
        return {
            "validation_summary": {
                "overall_score": round(validation_score, 1),
                "total_agents": total_agents,
                "successful_analyses": successful_agents,
                "failed_analyses": len(failed_analyses),
                "completion_rate": f"{(successful_agents/total_agents)*100:.1f}%" if total_agents > 0 else "0%"
            },
            "agent_results": successful_analyses,
            "failed_agents": failed_analyses,
            "methodology": {
                "approach": "Multi-agent comprehensive validation",
                "agents_used": [self.agents[key].title for key in results.keys()],
                "execution_phases": len(self.get_agent_execution_phases()),
                "external_tools_used": self._get_tools_used(successful_analyses)
            }
        }
    
    def _get_tools_used(self, successful_analyses: Dict[str, Any]) -> List[str]:
        """Extract list of external tools used across all analyses"""
        tools_used = set()
        for result in successful_analyses.values():
            if "tools_used" in result:
                tools_used.update(result["tools_used"])
        return list(tools_used)
    
    async def run_specific_agents(
        self, 
        agent_keys: List[str], 
        idea: str, 
        answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run specific agents only"""
        results = {}
        
        tasks = []
        for agent_key in agent_keys:
            if agent_key in self.agents:
                agent = self.agents[agent_key]
                task = agent.analyze(idea, answers)
                tasks.append((agent_key, task))
        
        # Run all specified agents in parallel
        for agent_key, task in tasks:
            try:
                result = await task
                results[agent_key] = result
            except Exception as e:
                logger.error(f"Agent {agent_key} failed: {str(e)}")
                results[agent_key] = {
                    "success": False,
                    "error": str(e),
                    "agent": self.agents[agent_key].title
                }
        
        return results

    async def _run_with_celery(self, idea: str, answers: Dict[str, Any], streaming_callback=None) -> Dict[str, Any]:
        """Run validation using Celery workers for true parallel processing"""
        try:
            from app.tasks.validation_tasks import ai_analysis_task
            import json

            logger.info("🚀 Starting Celery-based parallel validation with all 11 agents")

            # Process agents phase by phase for better streaming
            phases = self.get_agent_execution_phases()
            results = {}

            for phase_num, agent_keys in phases.items():
                if streaming_callback:
                    await streaming_callback({
                        "type": "phase_start",
                        "phase": phase_num,
                        "total_phases": len(phases),
                        "agents": [self.agents[key].title for key in agent_keys]
                    })

                # Launch Celery tasks for this phase
                celery_tasks = []
                for agent_key in agent_keys:
                    agent = self.agents[agent_key]

                    if streaming_callback:
                        await streaming_callback({
                            "type": "agent_start",
                            "agent": agent.title,
                            "icon": getattr(agent, 'icon', '🤖')
                        })

                    # Create task configuration for Celery
                    task_config = {
                        'agent_name': agent.title,
                        'agent_key': agent_key,
                        'method_name': 'analyze',
                        'args': [idea, answers],
                        'title': f"{agent.title} Analysis"
                    }

                    # Launch Celery task
                    task = ai_analysis_task.delay(task_config, idea, answers)
                    celery_tasks.append((agent_key, agent.title, task))

                # Wait for all tasks in this phase to complete
                for agent_key, agent_name, task in celery_tasks:
                    try:
                        result_json = task.get(timeout=180)  # 3 minute timeout per agent
                        result = json.loads(result_json)

                        results[agent_key] = {
                            "success": result.get('status') == 'completed',
                            "analysis": result.get('analysis', ''),
                            "agent": agent_name
                        }

                        if streaming_callback:
                            await streaming_callback({
                                "type": "agent_complete",
                                "agent": agent_name,
                                "agent_key": agent_key,
                                "success": result.get('status') == 'completed',
                                "phase": phase_num
                            })

                        logger.info(f"✅ Completed {agent_name} via Celery")

                    except Exception as e:
                        logger.error(f"❌ Celery task failed for {agent_name}: {str(e)}")
                        results[agent_key] = {
                            "success": False,
                            "error": str(e),
                            "agent": agent_name
                        }

                # Send phase complete message
                if streaming_callback:
                    await streaming_callback({
                        "type": "phase_complete",
                        "phase": phase_num,
                        "completed_agents": len(agent_keys)
                    })


            logger.info(f"🎉 Celery validation completed with {len(results)} agent results")

            # Compile final report
            final_report = self._compile_final_report(results)

            if streaming_callback:
                await streaming_callback({
                    "type": "validation_complete",
                    "report": final_report
                })

            return final_report

        except Exception as e:
            logger.error(f"❌ Celery validation failed: {str(e)}")
            # Fallback to asyncio if Celery fails
            logger.info("🔄 Falling back to asyncio execution")
            return await self.run_comprehensive_validation(idea, answers, streaming_callback, use_celery=False)
