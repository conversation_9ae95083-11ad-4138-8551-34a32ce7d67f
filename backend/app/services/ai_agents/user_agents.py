"""
User analysis specialized agents
"""

from typing import Dict, Any
import logging
from .base import BaseAgent
from app.services.ai_providers import AIProvider

logger = logging.getLogger(__name__)


class TargetUsersAgent(BaseAgent):
    """Target Users Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Target Users Specialist",
            title="Target Users Specialist",
            background="Expert in user research and market segmentation with focus on detailed user personas.",
            icon="👥",
            preferred_provider=AIProvider.ANTHROPIC
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate target users analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Target Users Specialist with expertise in user research and market segmentation.

TASK: Define detailed target users for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive target users analysis in JSON format:
{{
    "primary_personas": [
        {{
            "persona_name": "Persona name",
            "demographics": {{
                "age_range": "X-Y years",
                "income_range": "$X-Y",
                "education": "Education level",
                "location": "Geographic location",
                "occupation": "Job title/industry"
            }},
            "psychographics": {{
                "values": ["value1", "value2"],
                "interests": ["interest1", "interest2"],
                "lifestyle": "Lifestyle description",
                "technology_adoption": "Early adopter/Mainstream/Laggard"
            }},
            "pain_points": [
                {{
                    "pain_point": "Specific problem",
                    "severity": "High/Medium/Low",
                    "frequency": "How often they experience this",
                    "current_solution": "How they solve it now"
                }}
            ],
            "goals_and_motivations": [
                {{
                    "goal": "What they want to achieve",
                    "motivation": "Why it's important",
                    "success_metrics": "How they measure success"
                }}
            ],
            "user_journey": {{
                "awareness": "How they become aware of problems",
                "consideration": "How they evaluate solutions",
                "decision": "How they make purchase decisions",
                "usage": "How they use products",
                "advocacy": "How they recommend to others"
            }},
            "market_size": "X million users",
            "accessibility": "How easy to reach this segment"
        }}
    ],
    "secondary_personas": [
        {{
            "persona_name": "Secondary persona",
            "relationship_to_primary": "How they relate to primary users",
            "influence_level": "High/Medium/Low",
            "market_size": "X million users"
        }}
    ],
    "user_segmentation": {{
        "segmentation_criteria": ["criteria1", "criteria2"],
        "segment_priorities": [
            {{
                "segment": "Segment name",
                "priority": "High/Medium/Low",
                "rationale": "Why this priority",
                "go_to_market_approach": "How to reach them"
            }}
        ]
    }},
    "user_acquisition_strategy": {{
        "primary_channels": ["channel1", "channel2"],
        "messaging_strategy": "Key messages that resonate",
        "acquisition_cost_estimate": "$X per user",
        "conversion_funnel": {{
            "awareness_to_interest": "X%",
            "interest_to_trial": "X%",
            "trial_to_paid": "X%"
        }}
    }}
}}

Use external research to validate user personas and market sizing.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform target users analysis"""
        try:
            # Get external research
            external_research = await self.research_with_external_tools(idea, answers)
            
            prompt = self.get_analysis_prompt(idea, answers)
            if external_research:
                prompt += f"\n\nExternal Research:\n{external_research}"
            
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            tools_used = ["Anthropic"]
            if external_research:
                tools_used.extend(self.tools_manager.get_available_tools())
            
            return self.create_success_response(analysis, tools_used)
            
        except Exception as e:
            logger.error(f"Target users analysis failed: {str(e)}")
            return self.create_error_response("Target users analysis unavailable")


class TargetMarketAgent(BaseAgent):
    """Target Market Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Target Market Specialist",
            title="Target Market Specialist",
            background="Expert in market positioning and go-to-market strategy with focus on market entry and expansion.",
            icon="🎯",
            preferred_provider=AIProvider.OPENAI
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate target market analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Target Market Specialist with expertise in market positioning and go-to-market strategy.

TASK: Define target market strategy for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive target market analysis in JSON format:
{{
    "market_definition": {{
        "primary_market": {{
            "description": "Primary market description",
            "size": "$X billion",
            "growth_rate": "X% annually",
            "key_characteristics": ["characteristic1", "characteristic2"]
        }},
        "secondary_markets": [
            {{
                "market": "Market name",
                "size": "$X million",
                "entry_timeline": "When to enter",
                "entry_strategy": "How to enter"
            }}
        ]
    }},
    "market_positioning": {{
        "positioning_statement": "How we position ourselves",
        "key_differentiators": ["differentiator1", "differentiator2"],
        "competitive_positioning": "Vs competitors",
        "brand_positioning": "Brand personality and values"
    }},
    "go_to_market_strategy": {{
        "market_entry_approach": "Direct/Partner/Hybrid",
        "launch_sequence": [
            {{
                "phase": "Phase name",
                "timeline": "X months",
                "target_segment": "Which segment",
                "key_activities": ["activity1", "activity2"],
                "success_metrics": ["metric1", "metric2"]
            }}
        ],
        "distribution_channels": [
            {{
                "channel": "Channel name",
                "channel_type": "Direct/Indirect/Digital",
                "target_segment": "Which users",
                "cost_structure": "Cost model",
                "expected_volume": "X% of sales"
            }}
        ]
    }},
    "market_penetration_strategy": {{
        "penetration_approach": "Strategy description",
        "pricing_strategy": "Penetration/Skimming/Competitive",
        "promotional_strategy": ["tactic1", "tactic2"],
        "partnership_strategy": ["partner type 1", "partner type 2"]
    }},
    "market_expansion_roadmap": {{
        "year_1_targets": {{
            "market_share": "X%",
            "revenue_target": "$X",
            "user_acquisition": "X users"
        }},
        "year_3_targets": {{
            "market_share": "X%",
            "revenue_target": "$X",
            "geographic_expansion": ["market1", "market2"]
        }},
        "expansion_triggers": ["trigger1", "trigger2"]
    }}
}}

Focus on actionable go-to-market strategies with clear timelines and metrics.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform target market analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["OpenAI"])
            
        except Exception as e:
            logger.error(f"Target market analysis failed: {str(e)}")
            return self.create_error_response("Target market analysis unavailable")
