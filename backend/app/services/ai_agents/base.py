"""
Base agent class for specialized validation analysis
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import logging
from app.services.ai_providers import AIProvider
from app.services.ai_providers.manager import AIProviderManager
from app.services.external_tools.manager import ExternalToolsManager

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """Base class for specialized validation agents"""
    
    def __init__(
        self, 
        name: str,
        title: str,
        background: str,
        icon: str = "🤖",
        preferred_provider: AIProvider = AIProvider.ANTHROPIC
    ):
        self.name = name
        self.title = title
        self.background = background
        self.icon = icon
        self.preferred_provider = preferred_provider
        self.ai_manager = AIProviderManager()
        self.tools_manager = ExternalToolsManager()
    
    @abstractmethod
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform specialized analysis"""
        pass
    
    @abstractmethod
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate the analysis prompt for this agent"""
        pass
    
    def format_answers_for_prompt(self, answers: Dict[str, Any]) -> str:
        """Format answers dictionary for inclusion in prompts"""
        if not answers:
            return "No additional context provided."
        
        formatted = []
        for key, value in answers.items():
            if value:
                formatted_key = key.replace('_', ' ').title()
                formatted.append(f"{formatted_key}: {value}")
        
        return "\n".join(formatted) if formatted else "No additional context provided."
    
    async def generate_analysis(
        self, 
        prompt: str, 
        use_external_tools: bool = True,
        max_tokens: int = 2000
    ) -> str:
        """Generate analysis using AI provider and external tools"""
        try:
            # Use preferred provider if available, fallback to any available
            provider = self.preferred_provider
            if not self.ai_manager.is_provider_available(provider):
                available_providers = self.ai_manager.get_available_providers()
                if not available_providers:
                    raise Exception("No AI providers available")
                provider = available_providers[0]
            
            # Generate response
            response = await self.ai_manager.generate_response(
                provider, prompt, max_tokens
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Analysis generation failed for {self.name}: {str(e)}")
            raise
    
    async def research_with_external_tools(
        self, 
        idea: str, 
        answers: Dict[str, Any]
    ) -> Optional[str]:
        """Research using external tools if available"""
        try:
            if not self.tools_manager.get_available_tools():
                return None
            
            research_data = await self.tools_manager.research_idea(idea, answers)
            return self.tools_manager.format_research_summary(research_data)
            
        except Exception as e:
            logger.error(f"External research failed for {self.name}: {str(e)}")
            return None
    
    def create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response"""
        return {
            "analyst": self.name,
            "title": self.title,
            "error": error_message,
            "success": False
        }
    
    def create_success_response(self, analysis: str, tools_used: list = None) -> Dict[str, Any]:
        """Create standardized success response"""
        return {
            "analyst": self.name,
            "title": self.title,
            "analysis": analysis,
            "tools_used": tools_used or [],
            "success": True
        }
