"""
Market analysis specialized agents
"""

from typing import Dict, Any
import logging
from .base import BaseAgent
from app.services.ai_providers import AIProvider

logger = logging.getLogger(__name__)


class MarketAnalysisAgent(BaseAgent):
    """Agent Ross - Senior Market Research Analyst"""
    
    def __init__(self):
        super().__init__(
            name="Agent <PERSON>",
            title="Senior Market Research Analyst",
            background="15+ years of experience in market validation and competitive analysis. Specializes in TAM/SAM/SOM analysis and identifying market opportunities.",
            icon="📊",
            preferred_provider=AIProvider.ANTHROPIC
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate market analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are Agent Ross, a Senior Market Research Analyst with 15+ years of experience in market validation and competitive analysis. You specialize in TAM/SAM/SOM analysis and identifying market opportunities.

TASK: Analyze the market potential for this idea: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive market analysis in JSON format:
{{
    "market_size_analysis": {{
        "tam": {{
            "value": "$X billion",
            "description": "Total Addressable Market definition",
            "methodology": "How TAM was calculated",
            "data_sources": ["source1", "source2"],
            "growth_rate": "X% CAGR",
            "time_horizon": "2024-2030"
        }},
        "sam": {{
            "value": "$X million", 
            "description": "Serviceable Addressable Market definition",
            "geographic_scope": "{geographic_scope}",
            "market_constraints": ["constraint1", "constraint2"],
            "penetration_potential": "X% of TAM"
        }},
        "som": {{
            "value": "$X million",
            "description": "Serviceable Obtainable Market definition", 
            "realistic_capture": "X% of SAM",
            "timeline_to_achieve": "X years",
            "market_share_assumptions": ["assumption1", "assumption2"]
        }}
    }},
    "market_trends": {{
        "current_trends": ["trend1", "trend2", "trend3"],
        "emerging_opportunities": ["opportunity1", "opportunity2"],
        "market_drivers": ["driver1", "driver2"],
        "growth_indicators": ["indicator1", "indicator2"]
    }},
    "target_segments": {{
        "primary_segment": {{
            "description": "Primary target segment",
            "size": "$X million",
            "growth_rate": "X%",
            "accessibility": "High/Medium/Low"
        }},
        "secondary_segments": [
            {{"description": "Secondary segment", "potential": "High/Medium/Low"}}
        ]
    }},
    "market_maturity": {{
        "stage": "Emerging/Growing/Mature/Declining",
        "characteristics": ["characteristic1", "characteristic2"],
        "implications": ["implication1", "implication2"]
    }},
    "analyst_confidence": {{
        "overall_score": "1-10",
        "data_quality": "High/Medium/Low",
        "methodology_notes": "Analysis approach and limitations"
    }}
}}

Use mixture-of-agents approach with external market data for validation.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform market analysis"""
        try:
            # Get external research if available
            external_research = await self.research_with_external_tools(idea, answers)
            
            # Generate analysis prompt
            prompt = self.get_analysis_prompt(idea, answers)
            
            if external_research:
                prompt += f"\n\nExternal Market Research:\n{external_research}"
            
            # Generate analysis
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            tools_used = ["Anthropic"]
            if external_research:
                tools_used.extend(self.tools_manager.get_available_tools())
            
            return self.create_success_response(analysis, tools_used)
            
        except Exception as e:
            logger.error(f"Market analysis failed: {str(e)}")
            return self.create_error_response("Market analysis unavailable")


class FinancialAnalysisAgent(BaseAgent):
    """Agent Mohammed - Senior Financial Analyst"""
    
    def __init__(self):
        super().__init__(
            name="Agent Mohammed",
            title="Senior Financial Analyst", 
            background="12+ years in startup valuation, financial modeling, and investment analysis. Excels at revenue projections and business model validation.",
            icon="💰",
            preferred_provider=AIProvider.OPENAI
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate financial analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are Agent Mohammed, a Senior Financial Analyst with 12+ years in startup valuation, financial modeling, and investment analysis. You excel at revenue projections and business model validation.

TASK: Conduct financial feasibility analysis for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide comprehensive financial analysis in JSON format:
{{
    "revenue_projections": {{
        "year_1": {{"revenue": "$X", "assumptions": ["assumption1"]}},
        "year_3": {{"revenue": "$X", "assumptions": ["assumption1"]}},
        "year_5": {{"revenue": "$X", "assumptions": ["assumption1"]}}
    }},
    "cost_structure": {{
        "initial_investment": "$X",
        "operational_costs": {{
            "fixed_costs": "$X/month",
            "variable_costs": "X% of revenue",
            "key_cost_drivers": ["driver1", "driver2"]
        }},
        "scaling_costs": ["cost1", "cost2"]
    }},
    "profitability_analysis": {{
        "break_even_timeline": "X months",
        "gross_margin": "X%",
        "net_margin_projection": "X% by year 3",
        "unit_economics": {{
            "customer_acquisition_cost": "$X",
            "lifetime_value": "$X",
            "ltv_cac_ratio": "X:1"
        }}
    }},
    "funding_requirements": {{
        "seed_funding": "$X",
        "series_a_projection": "$X",
        "total_funding_needed": "$X",
        "funding_milestones": ["milestone1", "milestone2"]
    }},
    "financial_risks": {{
        "high_risk": ["risk1", "risk2"],
        "medium_risk": ["risk3", "risk4"],
        "mitigation_strategies": ["strategy1", "strategy2"]
    }},
    "analyst_confidence": {{
        "overall_score": "1-10",
        "model_reliability": "High/Medium/Low",
        "key_assumptions": ["assumption1", "assumption2"]
    }}
}}

Focus on realistic projections based on comparable companies and market data.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform financial analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["OpenAI"])
            
        except Exception as e:
            logger.error(f"Financial analysis failed: {str(e)}")
            return self.create_error_response("Financial analysis unavailable")


class RiskAnalysisAgent(BaseAgent):
    """Agent David - Senior Risk Assessment Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Agent David",
            title="Senior Risk Assessment Specialist",
            background="20+ years in startup risk analysis, regulatory compliance, and strategic planning. Excels at identifying and quantifying business risks.",
            icon="⚠️",
            preferred_provider=AIProvider.DEEPSEEK
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate risk analysis prompt"""
        geographic_scope = answers.get('geographic_scope', 'Global')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are Agent David, a Senior Risk Assessment Specialist with 20+ years in startup risk analysis, regulatory compliance, and strategic planning. You excel at identifying and quantifying business risks.

TASK: Comprehensive risk assessment for: {idea}
Geographic Scope: {geographic_scope}
Context: {context}

Provide detailed risk analysis in JSON format:
{{
    "market_risks": {{
        "high_impact": [
            {{"risk": "Risk description", "probability": "High/Medium/Low", "impact": "High/Medium/Low", "mitigation": "Strategy"}}
        ],
        "medium_impact": [
            {{"risk": "Risk description", "probability": "High/Medium/Low", "impact": "High/Medium/Low", "mitigation": "Strategy"}}
        ]
    }},
    "technical_risks": {{
        "development_risks": ["risk1", "risk2"],
        "scalability_risks": ["risk1", "risk2"],
        "security_risks": ["risk1", "risk2"],
        "mitigation_strategies": ["strategy1", "strategy2"]
    }},
    "regulatory_risks": {{
        "compliance_requirements": ["requirement1", "requirement2"],
        "regulatory_changes": ["change1", "change2"],
        "geographic_considerations": {{
            "{geographic_scope}": ["consideration1", "consideration2"]
        }}
    }},
    "competitive_risks": {{
        "market_entry_barriers": ["barrier1", "barrier2"],
        "competitive_threats": ["threat1", "threat2"],
        "differentiation_risks": ["risk1", "risk2"]
    }},
    "operational_risks": {{
        "team_risks": ["risk1", "risk2"],
        "supply_chain_risks": ["risk1", "risk2"],
        "scaling_challenges": ["challenge1", "challenge2"]
    }},
    "financial_risks": {{
        "funding_risks": ["risk1", "risk2"],
        "cash_flow_risks": ["risk1", "risk2"],
        "market_timing_risks": ["risk1", "risk2"]
    }},
    "overall_risk_assessment": {{
        "risk_score": "1-10 (10 = highest risk)",
        "risk_category": "Low/Medium/High",
        "key_risk_factors": ["factor1", "factor2"],
        "recommended_actions": ["action1", "action2"]
    }}
}}

Provide actionable risk mitigation strategies for each identified risk.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform risk analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["Deepseek"])
            
        except Exception as e:
            logger.error(f"Risk analysis failed: {str(e)}")
            return self.create_error_response("Risk analysis unavailable")
