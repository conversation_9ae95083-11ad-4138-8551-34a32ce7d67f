"""
Technical analysis specialized agents
"""

from typing import Dict, Any
import logging
from .base import BaseAgent
from app.services.ai_providers import AIProvider

logger = logging.getLogger(__name__)


class TechnicalAnalysisAgent(BaseAgent):
    """Agent Sarah - Senior Technical Architect"""
    
    def __init__(self):
        super().__init__(
            name="Agent <PERSON>",
            title="Senior Technical Architect",
            background="18+ years in software development, system design, and technology stack optimization. Specializes in technical feasibility and scalability analysis.",
            icon="🔧",
            preferred_provider=AIProvider.ANTHROPIC
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate technical analysis prompt"""
        tech_involvement = answers.get('tech_involvement', 'Technology platform')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are Agent <PERSON>, a Senior Technical Architect with 18+ years in software development, system design, and technology stack optimization. You specialize in technical feasibility and scalability analysis.

TASK: Assess technical feasibility for: {idea}
Technology Involvement: {tech_involvement}
Context: {context}

Provide comprehensive technical analysis in JSON format:
{{
    "technical_feasibility": {{
        "overall_score": "1-10",
        "complexity_level": "Low/Medium/High/Very High",
        "development_timeline": "X months",
        "key_challenges": ["challenge1", "challenge2"],
        "feasibility_assessment": "Detailed assessment"
    }},
    "recommended_tech_stack": {{
        "frontend": {{
            "framework": "React/Vue/Angular/etc",
            "reasoning": "Why this choice",
            "alternatives": ["alt1", "alt2"]
        }},
        "backend": {{
            "framework": "FastAPI/Django/Node.js/etc",
            "database": "PostgreSQL/MongoDB/etc",
            "reasoning": "Why these choices"
        }},
        "infrastructure": {{
            "hosting": "AWS/GCP/Azure/etc",
            "scaling_strategy": "Strategy description",
            "estimated_costs": "$X/month initially"
        }}
    }},
    "scalability_analysis": {{
        "user_capacity": {{
            "initial": "X users",
            "year_1": "X users", 
            "year_3": "X users"
        }},
        "performance_requirements": {{
            "response_time": "< X ms",
            "uptime": "99.X%",
            "throughput": "X requests/second"
        }},
        "scaling_challenges": ["challenge1", "challenge2"],
        "scaling_solutions": ["solution1", "solution2"]
    }},
    "security_considerations": {{
        "data_protection": ["measure1", "measure2"],
        "authentication": "OAuth/JWT/etc",
        "compliance_requirements": ["GDPR", "SOC2", "etc"],
        "security_risks": ["risk1", "risk2"],
        "mitigation_strategies": ["strategy1", "strategy2"]
    }},
    "development_approach": {{
        "methodology": "Agile/Waterfall/etc",
        "team_structure": {{
            "frontend_devs": "X developers",
            "backend_devs": "X developers",
            "devops": "X engineers",
            "total_team_size": "X people"
        }},
        "development_phases": [
            {{"phase": "MVP", "duration": "X months", "features": ["feature1"]}},
            {{"phase": "Beta", "duration": "X months", "features": ["feature2"]}}
        ]
    }},
    "technical_risks": {{
        "high_risk": ["risk1", "risk2"],
        "medium_risk": ["risk3", "risk4"],
        "mitigation_plans": ["plan1", "plan2"]
    }},
    "integration_requirements": {{
        "third_party_apis": ["API1", "API2"],
        "payment_processing": "Stripe/PayPal/etc",
        "analytics": "Google Analytics/Mixpanel/etc",
        "integration_complexity": "Low/Medium/High"
    }}
}}

Focus on practical, scalable solutions with realistic timelines and costs.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform technical analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["Anthropic"])
            
        except Exception as e:
            logger.error(f"Technical analysis failed: {str(e)}")
            return self.create_error_response("Technical analysis unavailable")


class FeaturesAnalysisAgent(BaseAgent):
    """Features Categorization Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Features Specialist",
            title="Features Categorization Specialist",
            background="Expert in product roadmap planning and feature prioritization with focus on user value and technical feasibility.",
            icon="⚡",
            preferred_provider=AIProvider.OPENAI
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate features analysis prompt"""
        tech_involvement = answers.get('tech_involvement', 'Technology platform')
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Features Categorization Specialist with expertise in product roadmap planning and feature prioritization.

TASK: Categorize all features for: {idea}
Technology Involvement: {tech_involvement}
Context: {context}

Provide comprehensive feature categorization in JSON format:
{{
    "core_features": {{
        "description": "Essential features without which the product cannot function",
        "features": [
            {{
                "name": "Feature name",
                "description": "What it does",
                "user_value": "Why users need it",
                "technical_complexity": "Low/Medium/High",
                "development_time": "X weeks"
            }}
        ],
        "total_development_time": "X weeks"
    }},
    "must_have_features": {{
        "description": "Critical features for market competitiveness",
        "features": [
            {{
                "name": "Feature name",
                "description": "What it does",
                "user_value": "Why users need it",
                "technical_complexity": "Low/Medium/High",
                "development_time": "X weeks"
            }}
        ],
        "total_development_time": "X weeks"
    }},
    "should_have_features": {{
        "description": "Important features that enhance user experience",
        "features": [
            {{
                "name": "Feature name",
                "description": "What it does",
                "user_value": "Why users want it",
                "technical_complexity": "Low/Medium/High",
                "development_time": "X weeks"
            }}
        ],
        "total_development_time": "X weeks"
    }},
    "nice_to_have_features": {{
        "description": "Features that provide additional value but are not essential",
        "features": [
            {{
                "name": "Feature name",
                "description": "What it does",
                "user_value": "Additional benefit",
                "technical_complexity": "Low/Medium/High",
                "development_time": "X weeks"
            }}
        ],
        "total_development_time": "X weeks"
    }},
    "future_features": {{
        "description": "Advanced features for future releases",
        "features": [
            {{
                "name": "Feature name",
                "description": "What it does",
                "user_value": "Future benefit",
                "technical_complexity": "Low/Medium/High",
                "development_time": "X weeks",
                "prerequisites": ["prerequisite1", "prerequisite2"]
            }}
        ],
        "roadmap_timeline": "Available in version X.X"
    }},
    "feature_dependencies": {{
        "critical_dependencies": [
            {{"feature": "Feature A", "depends_on": ["Feature B", "Feature C"]}}
        ],
        "development_order": ["Feature 1", "Feature 2", "Feature 3"]
    }},
    "mvp_recommendation": {{
        "included_features": ["core feature 1", "must-have feature 1"],
        "excluded_features": ["nice-to-have feature 1"],
        "mvp_timeline": "X weeks",
        "post_mvp_roadmap": ["Phase 2 features", "Phase 3 features"]
    }}
}}

Prioritize based on user value, technical feasibility, and market requirements.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform features analysis"""
        try:
            prompt = self.get_analysis_prompt(idea, answers)
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            return self.create_success_response(analysis, ["OpenAI"])
            
        except Exception as e:
            logger.error(f"Features analysis failed: {str(e)}")
            return self.create_error_response("Features analysis unavailable")
