"""
Research and citation specialized agents
"""

from typing import Dict, Any
import logging
from .base import BaseAgent
from app.services.ai_providers import AIProvider

logger = logging.getLogger(__name__)


class CitationsAgent(BaseAgent):
    """Citations & Sources Specialist"""
    
    def __init__(self):
        super().__init__(
            name="Citations & Sources Specialist",
            title="Citations & Sources Specialist",
            background="Expert in source verification and academic citation standards with focus on credible research.",
            icon="📚",
            preferred_provider=AIProvider.ANTHROPIC
        )
    
    def get_analysis_prompt(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate citations analysis prompt"""
        context = self.format_answers_for_prompt(answers)
        
        return f"""
You are a Citations & Sources Specialist with expertise in source verification and academic citation standards.

TASK: Gather and verify research citations for: {idea}
Context: {context}

Provide comprehensive citations and sources in JSON format:
{{
    "industry_reports": [
        {{
            "title": "Report title",
            "organization": "Publishing organization",
            "year": "2024",
            "key_findings": ["finding1", "finding2"],
            "relevance": "How it relates to the idea",
            "credibility_score": "High/Medium/Low",
            "access_url": "URL if available"
        }}
    ],
    "market_research": [
        {{
            "title": "Research title",
            "source": "Research firm/organization",
            "methodology": "How research was conducted",
            "sample_size": "X participants/companies",
            "key_statistics": ["stat1", "stat2"],
            "publication_date": "Date",
            "relevance": "How it supports the analysis"
        }}
    ],
    "academic_papers": [
        {{
            "title": "Paper title",
            "authors": ["Author 1", "Author 2"],
            "journal": "Journal name",
            "year": "2024",
            "doi": "DOI if available",
            "key_concepts": ["concept1", "concept2"],
            "relevance": "How it relates to the idea"
        }}
    ],
    "government_statistics": [
        {{
            "title": "Statistics title",
            "agency": "Government agency",
            "dataset": "Dataset name",
            "year": "2024",
            "key_metrics": ["metric1", "metric2"],
            "geographic_scope": "Coverage area",
            "relevance": "How it supports market analysis"
        }}
    ],
    "news_and_media": [
        {{
            "title": "Article title",
            "publication": "Publication name",
            "date": "Publication date",
            "author": "Author name",
            "key_points": ["point1", "point2"],
            "credibility": "High/Medium/Low",
            "bias_assessment": "Potential bias considerations"
        }}
    ],
    "expert_opinions": [
        {{
            "expert": "Expert name",
            "credentials": "Expert background",
            "opinion": "Key opinion or quote",
            "context": "Where/when opinion was given",
            "relevance": "How it supports the analysis"
        }}
    ],
    "data_quality_assessment": {{
        "overall_quality": "High/Medium/Low",
        "source_diversity": "Good/Fair/Poor",
        "recency": "How recent the sources are",
        "geographic_coverage": "Geographic representation",
        "potential_gaps": ["gap1", "gap2"],
        "reliability_notes": "Notes on source reliability"
    }},
    "citation_methodology": {{
        "search_strategy": "How sources were found",
        "inclusion_criteria": ["criteria1", "criteria2"],
        "verification_process": "How sources were verified",
        "limitations": ["limitation1", "limitation2"]
    }}
}}

Focus on credible, recent sources that directly support the market analysis and validation.
"""
    
    async def analyze(self, idea: str, answers: Dict[str, Any]) -> Dict[str, Any]:
        """Perform citations analysis"""
        try:
            # Get external research - this is particularly important for citations
            external_research = await self.research_with_external_tools(idea, answers)
            
            prompt = self.get_analysis_prompt(idea, answers)
            if external_research:
                prompt += f"\n\nExternal Research Data:\n{external_research}"
                prompt += "\n\nUse the external research data to identify and cite specific sources."
            
            analysis = await self.generate_analysis(prompt, use_external_tools=True)
            
            tools_used = ["Anthropic"]
            if external_research:
                tools_used.extend(self.tools_manager.get_available_tools())
            
            return self.create_success_response(analysis, tools_used)
            
        except Exception as e:
            logger.error(f"Citations analysis failed: {str(e)}")
            return self.create_error_response("Citations analysis unavailable")
