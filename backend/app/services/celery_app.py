from celery import Celery
from app.core.config import settings

# Create Celery app
celery_app = Celery(
    "incepta",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=[
        "app.services.tasks.idea_validation",
        "app.services.tasks.roadmap_generation",
        "app.services.tasks.market_research",
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    result_expires=3600,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Task routing
celery_app.conf.task_routes = {
    "app.services.tasks.idea_validation.*": {"queue": "validation"},
    "app.services.tasks.roadmap_generation.*": {"queue": "roadmap"},
    "app.services.tasks.market_research.*": {"queue": "research"},
}

# Queue configuration
celery_app.conf.task_default_queue = "default"
celery_app.conf.task_queues = {
    "default": {
        "exchange": "default",
        "routing_key": "default",
    },
    "validation": {
        "exchange": "validation",
        "routing_key": "validation",
    },
    "roadmap": {
        "exchange": "roadmap", 
        "routing_key": "roadmap",
    },
    "research": {
        "exchange": "research",
        "routing_key": "research",
    },
}