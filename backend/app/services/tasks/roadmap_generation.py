from celery import current_task
from sqlalchemy.orm import Session
from app.worker import celery_app
from app.core.database import SessionLocal
from app.models.roadmap import Roadmap, RoadmapStatus
from app.services.ai_service import AIService
import logging

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def generate_roadmap_task(self, roadmap_id: int):
    """
    Background task to generate project roadmap using AI
    """
    db = SessionLocal()
    try:
        # Get roadmap record
        roadmap = db.query(Roadmap).filter(Roadmap.id == roadmap_id).first()
        
        if not roadmap:
            raise Exception(f"Roadmap {roadmap_id} not found")
        
        # Update status to generating
        roadmap.status = RoadmapStatus.GENERATING
        db.commit()
        
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 1, "total": 6, "status": "Initializing roadmap generation..."}
        )
        
        # Initialize AI service
        ai_service = AIService()
        
        # Get project planning data if available
        project_planning = roadmap.project_planning
        
        # Step 1: Generate phase breakdown
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 2, "total": 6, "status": "Generating development phases..."}
        )
        
        phases = ai_service.generate_roadmap_phases(
            title=roadmap.title,
            description=roadmap.description,
            project_planning=project_planning
        )
        roadmap.phases = phases
        db.commit()
        
        # Step 2: Generate team composition
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 3, "total": 6, "status": "Determining team composition..."}
        )
        
        team_composition = ai_service.generate_team_composition(phases)
        roadmap.team_composition = team_composition
        db.commit()
        
        # Step 3: Generate cost breakdown
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 4, "total": 6, "status": "Calculating cost estimates..."}
        )
        
        cost_breakdown = ai_service.generate_cost_breakdown(phases, team_composition)
        roadmap.cost_breakdown = cost_breakdown
        
        # Calculate total estimated cost
        total_cost = sum(
            phase.get("estimated_cost", 0) for phase in cost_breakdown.get("phases", [])
        )
        roadmap.total_estimated_cost = total_cost
        db.commit()
        
        # Step 4: Generate timeline estimates
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 5, "total": 6, "status": "Creating timeline estimates..."}
        )
        
        timeline_estimates = ai_service.generate_timeline_estimates(phases, team_composition)
        roadmap.timeline_estimates = timeline_estimates
        db.commit()
        
        # Step 5: Generate risk assessments and milestones
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 6, "total": 6, "status": "Finalizing roadmap..."}
        )
        
        risk_assessments = ai_service.generate_risk_assessments(phases)
        milestones = ai_service.generate_milestones(phases, timeline_estimates)
        
        roadmap.risk_assessments = risk_assessments
        roadmap.milestones = milestones
        roadmap.status = RoadmapStatus.COMPLETED
        roadmap.ai_provider = ai_service.current_provider
        
        db.commit()
        
        return {
            "status": "completed",
            "roadmap_id": roadmap_id,
            "total_phases": len(phases),
            "estimated_cost": float(total_cost) if total_cost else 0,
            "message": "Roadmap generation completed successfully"
        }
        
    except Exception as e:
        logger.error(f"Error generating roadmap {roadmap_id}: {str(e)}")
        
        # Update roadmap status to failed
        if 'roadmap' in locals():
            roadmap.status = RoadmapStatus.FAILED
            roadmap.error_message = str(e)
            db.commit()
        
        # Update task state
        current_task.update_state(
            state="FAILURE",
            meta={"error": str(e), "roadmap_id": roadmap_id}
        )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True)
def update_roadmap_costs_task(self, roadmap_id: int, hourly_rates: dict):
    """
    Task to recalculate roadmap costs with updated hourly rates
    """
    db = SessionLocal()
    try:
        roadmap = db.query(Roadmap).filter(Roadmap.id == roadmap_id).first()
        
        if not roadmap:
            raise Exception(f"Roadmap {roadmap_id} not found")
        
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 1, "total": 2, "status": "Recalculating costs..."}
        )
        
        # Update hourly rates
        roadmap.hourly_rates = hourly_rates
        
        # Recalculate cost breakdown
        ai_service = AIService()
        cost_breakdown = ai_service.recalculate_costs(
            roadmap.phases,
            roadmap.team_composition, 
            hourly_rates
        )
        
        roadmap.cost_breakdown = cost_breakdown
        
        # Update total estimated cost
        total_cost = sum(
            phase.get("estimated_cost", 0) for phase in cost_breakdown.get("phases", [])
        )
        roadmap.total_estimated_cost = total_cost
        
        db.commit()
        
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 2, "total": 2, "status": "Cost update completed"}
        )
        
        return {
            "status": "completed",
            "roadmap_id": roadmap_id,
            "new_total_cost": float(total_cost) if total_cost else 0
        }
        
    except Exception as e:
        logger.error(f"Error updating roadmap costs {roadmap_id}: {str(e)}")
        
        current_task.update_state(
            state="FAILURE",
            meta={"error": str(e), "roadmap_id": roadmap_id}
        )
        
        raise
        
    finally:
        db.close()