from celery import current_task
from sqlalchemy.orm import Session
from app.services.celery_app import celery_app
from app.core.database import SessionLocal
from app.models.idea_validation import MarketResearch, VerifiedCitation
from app.services.external_apis import TavilyService
import logging

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def research_market_task(self, validation_id: int):
    """
    Background task to conduct market research using Tavily API
    """
    db = SessionLocal()
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 1, "total": 4, "status": "Initializing market research..."}
        )
        
        # Initialize Tavily service
        tavily_service = TavilyService()
        
        # Generate research queries based on validation data
        # This would analyze the idea and generate relevant search queries
        research_queries = [
            f"market size for {validation_id}",  # This would be more sophisticated
            f"competitors in {validation_id} space",
            f"target audience for {validation_id}",
        ]
        
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 2, "total": 4, "status": "Conducting market searches..."}
        )
        
        for i, query in enumerate(research_queries):
            # Perform search
            search_results = tavily_service.search(query)
            
            # Create market research record
            market_research = MarketResearch(
                idea_validation_id=validation_id,
                query=query,
                results=search_results,
                search_provider="tavily",
                results_count=len(search_results.get("results", []))
            )
            db.add(market_research)
            db.commit()
            
            # Create citations
            for result in search_results.get("results", []):
                citation = VerifiedCitation(
                    market_research_id=market_research.id,
                    title=result.get("title", ""),
                    url=result.get("url", ""),
                    snippet=result.get("content", ""),
                    source_domain=result.get("url", "").split("//")[-1].split("/")[0] if result.get("url") else "",
                    relevance_score=result.get("score", 0)
                )
                db.add(citation)
            
            db.commit()
            
            current_task.update_state(
                state="PROGRESS",
                meta={
                    "current": 2 + (i + 1) * 0.5,
                    "total": 4,
                    "status": f"Processed query: {query}"
                }
            )
        
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 4, "total": 4, "status": "Market research completed"}
        )
        
        return {
            "status": "completed",
            "validation_id": validation_id,
            "queries_processed": len(research_queries)
        }
        
    except Exception as e:
        logger.error(f"Error in market research for validation {validation_id}: {str(e)}")
        
        current_task.update_state(
            state="FAILURE",
            meta={"error": str(e), "validation_id": validation_id}
        )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True)
def analyze_competitors_task(self, validation_id: int, competitor_urls: list):
    """
    Background task to analyze competitors using Firecrawl API
    """
    db = SessionLocal()
    try:
        current_task.update_state(
            state="PROGRESS",
            meta={"current": 1, "total": len(competitor_urls) + 1, "status": "Starting competitor analysis..."}
        )
        
        # This would integrate with Firecrawl API
        # Implementation placeholder for now
        
        for i, url in enumerate(competitor_urls):
            current_task.update_state(
                state="PROGRESS", 
                meta={
                    "current": i + 2,
                    "total": len(competitor_urls) + 1,
                    "status": f"Analyzing competitor: {url}"
                }
            )
            
            # Scrape competitor website with Firecrawl
            # competitor_data = firecrawl_service.scrape(url)
            # Process and store competitor data
            
        return {
            "status": "completed",
            "validation_id": validation_id,
            "competitors_analyzed": len(competitor_urls)
        }
        
    except Exception as e:
        logger.error(f"Error in competitor analysis for validation {validation_id}: {str(e)}")
        
        current_task.update_state(
            state="FAILURE",
            meta={"error": str(e), "validation_id": validation_id}
        )
        
        raise
        
    finally:
        db.close()