"""
Advanced priority queue system for validation requests
"""

import asyncio
import heapq
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json

class Priority(Enum):
    """Priority levels for validation requests"""
    CRITICAL = 1    # Premium users, urgent requests
    HIGH = 2        # Paid users
    NORMAL = 3      # Regular users
    LOW = 4         # Background processing, retries

@dataclass
class ValidationRequest:
    """Validation request with priority and metadata"""
    id: str
    idea: str
    answers: Dict[str, Any]
    priority: Priority
    user_id: str
    created_at: datetime
    estimated_duration: float = 300.0  # 5 minutes default
    retry_count: int = 0
    max_retries: int = 3
    callback: Optional[Callable] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        """For heapq comparison - lower priority value = higher priority"""
        if self.priority.value != other.priority.value:
            return self.priority.value < other.priority.value
        # If same priority, older requests first
        return self.created_at < other.created_at

class SmartQueue:
    """Intelligent queue with priority, load balancing, and adaptive scheduling"""
    
    def __init__(self, max_concurrent: int = 5):
        self.queue: List[ValidationRequest] = []
        self.active_requests: Dict[str, ValidationRequest] = {}
        self.completed_requests: Dict[str, Dict] = {}
        self.max_concurrent = max_concurrent
        self.processing_lock = asyncio.Lock()
        self.stats = {
            "total_processed": 0,
            "total_failed": 0,
            "avg_processing_time": 0,
            "queue_wait_times": []
        }
    
    async def enqueue(self, request: ValidationRequest) -> str:
        """Add request to priority queue"""
        async with self.processing_lock:
            heapq.heappush(self.queue, request)
            print(f"📥 Queued validation {request.id} with priority {request.priority.name}")
            return request.id
    
    async def dequeue(self) -> Optional[ValidationRequest]:
        """Get next request from queue"""
        async with self.processing_lock:
            if self.queue and len(self.active_requests) < self.max_concurrent:
                request = heapq.heappop(self.queue)
                self.active_requests[request.id] = request
                
                # Track queue wait time
                wait_time = (datetime.now() - request.created_at).total_seconds()
                self.stats["queue_wait_times"].append(wait_time)
                if len(self.stats["queue_wait_times"]) > 100:
                    self.stats["queue_wait_times"] = self.stats["queue_wait_times"][-100:]
                
                print(f"📤 Dequeued validation {request.id} (waited {wait_time:.1f}s)")
                return request
            return None
    
    async def complete_request(self, request_id: str, success: bool = True, 
                             result: Dict[str, Any] = None, error: str = None):
        """Mark request as completed"""
        async with self.processing_lock:
            if request_id in self.active_requests:
                request = self.active_requests[request_id]
                del self.active_requests[request_id]
                
                # Update stats
                self.stats["total_processed"] += 1
                if not success:
                    self.stats["total_failed"] += 1
                
                # Store completion info
                self.completed_requests[request_id] = {
                    "request": request,
                    "success": success,
                    "result": result,
                    "error": error,
                    "completed_at": datetime.now()
                }
                
                # Execute callback if provided
                if request.callback:
                    try:
                        await request.callback(request_id, success, result, error)
                    except Exception as e:
                        print(f"❌ Callback failed for {request_id}: {e}")
                
                print(f"✅ Completed validation {request_id} (success: {success})")
    
    async def retry_request(self, request_id: str, error: str = None):
        """Retry a failed request"""
        if request_id in self.completed_requests:
            completed = self.completed_requests[request_id]
            request = completed["request"]
            
            if request.retry_count < request.max_retries:
                request.retry_count += 1
                request.priority = Priority.HIGH  # Boost priority for retries
                request.metadata["retry_reason"] = error
                request.metadata["original_failure"] = completed["error"]
                
                await self.enqueue(request)
                print(f"🔄 Retrying validation {request_id} (attempt {request.retry_count})")
                return True
            else:
                print(f"❌ Max retries exceeded for {request_id}")
                return False
        return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        priority_counts = {p.name: 0 for p in Priority}
        for req in self.queue:
            priority_counts[req.priority.name] += 1
        
        avg_wait_time = (
            sum(self.stats["queue_wait_times"]) / len(self.stats["queue_wait_times"])
            if self.stats["queue_wait_times"] else 0
        )
        
        return {
            "queue_size": len(self.queue),
            "active_requests": len(self.active_requests),
            "priority_breakdown": priority_counts,
            "avg_wait_time": avg_wait_time,
            "success_rate": (
                (self.stats["total_processed"] - self.stats["total_failed"]) / 
                max(self.stats["total_processed"], 1)
            ),
            "capacity_utilization": len(self.active_requests) / self.max_concurrent
        }
    
    async def optimize_queue(self):
        """Optimize queue based on current conditions"""
        async with self.processing_lock:
            # Promote old requests
            current_time = datetime.now()
            for request in self.queue:
                wait_time = (current_time - request.created_at).total_seconds()
                
                # Promote requests waiting > 10 minutes
                if wait_time > 600 and request.priority != Priority.CRITICAL:
                    if request.priority == Priority.LOW:
                        request.priority = Priority.NORMAL
                    elif request.priority == Priority.NORMAL:
                        request.priority = Priority.HIGH
                    elif request.priority == Priority.HIGH:
                        request.priority = Priority.CRITICAL
            
            # Re-heapify after priority changes
            heapq.heapify(self.queue)

class AdaptiveScheduler:
    """Adaptive scheduler that adjusts based on system load"""
    
    def __init__(self, queue: SmartQueue):
        self.queue = queue
        self.load_history = []
        self.adjustment_factor = 1.0
    
    async def adjust_concurrency(self):
        """Dynamically adjust max concurrent requests"""
        status = self.queue.get_queue_status()
        
        # Increase concurrency if queue is growing and success rate is high
        if (status["queue_size"] > 10 and 
            status["success_rate"] > 0.9 and 
            status["capacity_utilization"] > 0.8):
            
            self.queue.max_concurrent = min(self.queue.max_concurrent + 1, 10)
            print(f"📈 Increased concurrency to {self.queue.max_concurrent}")
        
        # Decrease concurrency if success rate is low
        elif status["success_rate"] < 0.7:
            self.queue.max_concurrent = max(self.queue.max_concurrent - 1, 2)
            print(f"📉 Decreased concurrency to {self.queue.max_concurrent}")
    
    async def predict_completion_time(self, request: ValidationRequest) -> float:
        """Predict completion time based on historical data"""
        base_time = request.estimated_duration
        
        # Adjust based on current load
        load_factor = len(self.queue.active_requests) / self.queue.max_concurrent
        queue_delay = len(self.queue.queue) * 60  # Assume 1 min per queued item
        
        return base_time * (1 + load_factor * 0.5) + queue_delay

# Global queue instance
validation_queue = SmartQueue(max_concurrent=3)
scheduler = AdaptiveScheduler(validation_queue)
