"""
Firecrawl client for web scraping and content extraction
"""

from typing import Optional, Dict, Any, List
import logging
import asyncio
from firecrawl import <PERSON><PERSON>rawlApp

logger = logging.getLogger(__name__)


class FirecrawlClient:
    """Client for Firecrawl web scraping API"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.client = None
        self._setup_client()
    
    def _setup_client(self) -> None:
        """Setup Firecrawl client"""
        if self.api_key and self.api_key != "your-firecrawl-api-key":
            self.client = FirecrawlApp(api_key=self.api_key)
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Firecrawl is available"""
        return self.client is not None
    
    async def scrape_url(
        self,
        url: str,
        formats: List[str] = None,
        include_tags: List[str] = None,
        exclude_tags: List[str] = None,
        only_main_content: bool = True,
        timeout: float = 30.0
    ) -> Dict[str, Any]:
        """Scrape a single URL with timeout"""
        if not self.client:
            raise Exception("Firecrawl client not initialized")

        try:
            scrape_params = {
                "formats": formats or ["markdown", "html"],
                "onlyMainContent": only_main_content
            }

            if include_tags:
                scrape_params["includeTags"] = include_tags
            if exclude_tags:
                scrape_params["excludeTags"] = exclude_tags

            # Run the synchronous scrape_url in a thread with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(self.client.scrape_url, url, scrape_params),
                timeout=timeout
            )
            
            return {
                "url": url,
                "title": result.get("metadata", {}).get("title", ""),
                "content": result.get("markdown", ""),
                "html": result.get("html", ""),
                "metadata": result.get("metadata", {}),
                "success": True
            }
            
        except asyncio.TimeoutError:
            logger.error(f"Firecrawl scrape timeout for {url} after {timeout}s")
            return {
                "url": url,
                "error": f"Request timed out after {timeout} seconds",
                "success": False
            }
        except Exception as e:
            logger.error(f"Firecrawl scrape error for {url}: {str(e)}")
            return {
                "url": url,
                "error": str(e),
                "success": False
            }
    
    async def crawl_website(
        self,
        url: str,
        max_pages: int = 5,
        include_paths: List[str] = None,
        exclude_paths: List[str] = None,
        timeout: float = 60.0
    ) -> Dict[str, Any]:
        """Crawl multiple pages from a website with timeout"""
        if not self.client:
            raise Exception("Firecrawl client not initialized")

        try:
            crawl_params = {
                "limit": max_pages,
                "scrapeOptions": {
                    "formats": ["markdown"],
                    "onlyMainContent": True
                }
            }

            if include_paths:
                crawl_params["includePaths"] = include_paths
            if exclude_paths:
                crawl_params["excludePaths"] = exclude_paths

            # Run the synchronous crawl_url in a thread with timeout
            result = await asyncio.wait_for(
                asyncio.to_thread(self.client.crawl_url, url, crawl_params),
                timeout=timeout
            )
            
            return {
                "base_url": url,
                "pages": result.get("data", []),
                "total_pages": len(result.get("data", [])),
                "success": True
            }
            
        except asyncio.TimeoutError:
            logger.error(f"Firecrawl crawl timeout for {url} after {timeout}s")
            return {
                "base_url": url,
                "error": f"Crawl timed out after {timeout} seconds",
                "success": False
            }
        except Exception as e:
            logger.error(f"Firecrawl crawl error for {url}: {str(e)}")
            return {
                "base_url": url,
                "error": str(e),
                "success": False
            }
