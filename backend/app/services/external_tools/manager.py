"""
External tools manager for coordinating web search and scraping
"""

from typing import Dict, Any, List, Optional
import logging
import asyncio
from app.core.config import settings

from .tavily_client import TavilySearchClient
from .firecrawl_client import FirecrawlClient

logger = logging.getLogger(__name__)


class ExternalToolsManager:
    """Manager for external research tools"""
    
    def __init__(self):
        self.tavily = TavilySearchClient(settings.tavily_api_key)
        self.firecrawl = FirecrawlClient(settings.firecrawl_api_key)
    
    def get_available_tools(self) -> List[str]:
        """Get list of available external tools"""
        tools = []
        if self.tavily.is_available():
            tools.append("tavily")
        if self.firecrawl.is_available():
            tools.append("firecrawl")
        return tools
    
    async def search_and_scrape(
        self, 
        query: str, 
        max_search_results: int = 5,
        max_scrape_pages: int = 3
    ) -> Dict[str, Any]:
        """Perform search and scrape top results"""
        results = {
            "query": query,
            "search_results": [],
            "scraped_content": [],
            "tools_used": []
        }
        
        try:
            # Search with <PERSON>ly if available
            if self.tavily.is_available():
                search_results = await self.tavily.search(
                    query, max_results=max_search_results
                )
                results["search_results"] = search_results.get("results", [])
                results["tools_used"].append("tavily")
                
                # Scrape top URLs with Firecrawl if available
                if self.firecrawl.is_available() and results["search_results"]:
                    scrape_tasks = []
                    for result in results["search_results"][:max_scrape_pages]:
                        url = result.get("url")
                        if url:
                            scrape_tasks.append(
                                self.firecrawl.scrape_url(url)
                            )
                    
                    if scrape_tasks:
                        scraped_results = await asyncio.gather(
                            *scrape_tasks, return_exceptions=True
                        )
                        
                        for scraped in scraped_results:
                            if isinstance(scraped, dict) and scraped.get("success"):
                                results["scraped_content"].append(scraped)
                        
                        results["tools_used"].append("firecrawl")
            
        except Exception as e:
            logger.error(f"External tools error: {str(e)}")
            results["error"] = str(e)
        
        return results
    
    async def research_idea(
        self, 
        idea: str, 
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Research an idea using available external tools"""
        # Extract search query
        search_query = self.tavily.extract_search_query(idea, context) if self.tavily.is_available() else idea
        
        # Perform comprehensive research
        return await self.search_and_scrape(search_query)
    
    def format_research_summary(self, research_data: Dict[str, Any]) -> str:
        """Format research data into a summary"""
        if not research_data:
            return "No external research data available."
        
        summary_parts = []
        
        # Add search results summary
        search_results = research_data.get("search_results", [])
        if search_results:
            summary_parts.append(f"Found {len(search_results)} relevant search results:")
            for i, result in enumerate(search_results[:3], 1):
                title = result.get("title", "Unknown")
                content = result.get("content", "")[:200]
                summary_parts.append(f"{i}. {title}: {content}...")
        
        # Add scraped content summary
        scraped_content = research_data.get("scraped_content", [])
        if scraped_content:
            summary_parts.append(f"\nDetailed content from {len(scraped_content)} sources:")
            for content in scraped_content[:2]:
                title = content.get("title", "Unknown Source")
                text = content.get("content", "")[:300]
                summary_parts.append(f"- {title}: {text}...")
        
        tools_used = research_data.get("tools_used", [])
        if tools_used:
            summary_parts.append(f"\nResearch tools used: {', '.join(tools_used)}")
        
        return "\n".join(summary_parts) if summary_parts else "No research data available."
