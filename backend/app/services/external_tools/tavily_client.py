"""
Tavily search client for web research
"""

from typing import Optional, List, Dict, Any
import logging
from tavily import TavilyClient

logger = logging.getLogger(__name__)


class TavilySearchClient:
    """Client for Tavily web search API"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.client = None
        self._setup_client()
    
    def _setup_client(self) -> None:
        """Setup Tavily client"""
        if self.api_key and self.api_key != "your-tavily-api-key":
            self.client = TavilyClient(api_key=self.api_key)
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if <PERSON><PERSON> is available"""
        return self.client is not None
    
    async def search(
        self, 
        query: str, 
        max_results: int = 5,
        search_depth: str = "basic",
        include_domains: Optional[List[str]] = None,
        exclude_domains: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Search using Tavily"""
        if not self.client:
            raise Exception("Tavily client not initialized")
        
        try:
            results = self.client.search(
                query=query,
                max_results=max_results,
                search_depth=search_depth,
                include_domains=include_domains,
                exclude_domains=exclude_domains
            )
            
            # Format results for consistency
            formatted_results = {
                "query": query,
                "results": [],
                "total_results": len(results.get("results", []))
            }
            
            for result in results.get("results", []):
                formatted_results["results"].append({
                    "title": result.get("title", ""),
                    "url": result.get("url", ""),
                    "content": result.get("content", ""),
                    "score": result.get("score", 0)
                })
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"Tavily search error: {str(e)}")
            raise
    
    def extract_search_query(self, idea: str, context: Dict[str, Any] = None) -> str:
        """Extract search query from idea and context"""
        # Simple extraction logic - can be enhanced
        base_query = idea.strip()
        
        if context:
            geographic_scope = context.get('geographic_scope', '')
            if geographic_scope and geographic_scope != 'Global':
                base_query += f" {geographic_scope}"
        
        # Add market research keywords
        base_query += " market size trends analysis"
        
        return base_query
