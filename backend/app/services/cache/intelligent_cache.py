"""
Intelligent caching system for validation results
"""

import hashlib
import json
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import redis
from sentence_transformers import SentenceTransformer
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

class IntelligentCache:
    """Smart caching with semantic similarity matching"""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client or redis.Redis(host='localhost', port=6379, db=0)
        self.similarity_model = SentenceTransformer('all-MiniLM-L6-v2')
        self.similarity_threshold = 0.85  # 85% similarity threshold
        
    def _generate_cache_key(self, idea: str, answers: Dict[str, Any]) -> str:
        """Generate cache key from idea and answers"""
        content = f"{idea}_{json.dumps(answers, sort_keys=True)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _get_idea_embedding(self, idea: str) -> np.ndarray:
        """Get semantic embedding for idea"""
        return self.similarity_model.encode([idea])[0]
    
    async def find_similar_validation(self, idea: str, answers: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find similar cached validation using semantic similarity"""
        try:
            # Get embedding for current idea
            current_embedding = self._get_idea_embedding(idea)
            
            # Get all cached ideas with embeddings
            cached_keys = self.redis.keys("validation:*")
            best_match = None
            best_similarity = 0
            
            for key in cached_keys:
                cached_data = json.loads(self.redis.get(key))
                if 'embedding' in cached_data:
                    cached_embedding = np.array(cached_data['embedding'])
                    similarity = cosine_similarity([current_embedding], [cached_embedding])[0][0]
                    
                    if similarity > self.similarity_threshold and similarity > best_similarity:
                        # Check if answers are compatible
                        if self._answers_compatible(answers, cached_data.get('answers', {})):
                            best_similarity = similarity
                            best_match = cached_data
            
            if best_match:
                print(f"🎯 Found similar validation with {best_similarity:.2%} similarity")
                return best_match['result']
            
            return None
            
        except Exception as e:
            print(f"Error in similarity search: {e}")
            return None
    
    def _answers_compatible(self, current: Dict, cached: Dict) -> bool:
        """Check if answers are compatible for reuse"""
        # Key fields that must match
        key_fields = ['geographic_scope', 'target_market', 'tech_involvement']
        
        for field in key_fields:
            if current.get(field) != cached.get(field):
                return False
        return True
    
    async def cache_validation(self, idea: str, answers: Dict[str, Any], result: Dict[str, Any], ttl: int = 86400):
        """Cache validation result with semantic embedding"""
        try:
            cache_key = f"validation:{self._generate_cache_key(idea, answers)}"
            embedding = self._get_idea_embedding(idea).tolist()
            
            cache_data = {
                'idea': idea,
                'answers': answers,
                'result': result,
                'embedding': embedding,
                'timestamp': datetime.now().isoformat(),
                'ttl': ttl
            }
            
            self.redis.setex(cache_key, ttl, json.dumps(cache_data))
            print(f"✅ Cached validation for idea: {idea[:50]}...")
            
        except Exception as e:
            print(f"Error caching validation: {e}")


class ProgressiveCache:
    """Progressive caching for partial results"""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client or redis.Redis(host='localhost', port=6379, db=1)
    
    async def cache_agent_result(self, validation_id: str, agent_key: str, result: Dict[str, Any]):
        """Cache individual agent result"""
        cache_key = f"agent:{validation_id}:{agent_key}"
        self.redis.setex(cache_key, 3600, json.dumps(result))  # 1 hour TTL
    
    async def get_cached_agent_results(self, validation_id: str) -> Dict[str, Any]:
        """Get all cached agent results for a validation"""
        pattern = f"agent:{validation_id}:*"
        keys = self.redis.keys(pattern)
        results = {}
        
        for key in keys:
            agent_key = key.decode().split(':')[-1]
            result = json.loads(self.redis.get(key))
            results[agent_key] = result
        
        return results
    
    async def cache_phase_result(self, validation_id: str, phase: int, results: Dict[str, Any]):
        """Cache phase completion"""
        cache_key = f"phase:{validation_id}:{phase}"
        self.redis.setex(cache_key, 7200, json.dumps(results))  # 2 hours TTL


class AdaptiveCache:
    """Adaptive caching based on usage patterns"""
    
    def __init__(self, redis_client=None):
        self.redis = redis_client or redis.Redis(host='localhost', port=6379, db=2)
        self.usage_stats = {}
    
    async def track_usage(self, cache_key: str):
        """Track cache usage for adaptive TTL"""
        usage_key = f"usage:{cache_key}"
        self.redis.incr(usage_key)
        self.redis.expire(usage_key, 86400)  # Reset daily
    
    async def get_adaptive_ttl(self, cache_key: str) -> int:
        """Get adaptive TTL based on usage"""
        usage_key = f"usage:{cache_key}"
        usage_count = int(self.redis.get(usage_key) or 0)
        
        # More usage = longer TTL
        if usage_count > 10:
            return 86400 * 7  # 1 week
        elif usage_count > 5:
            return 86400 * 3  # 3 days
        elif usage_count > 2:
            return 86400      # 1 day
        else:
            return 3600       # 1 hour
