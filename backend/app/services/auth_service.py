from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from app.core.security import (
    verify_password,
    get_password_hash,
    create_access_token,
    generate_password_reset_token,
    verify_password_reset_token,
    generate_email_verification_token,
    verify_email_verification_token,
)
from app.models.user import User, UserRole
from app.models.subscription import UserSubscription, PricingPlan, PlanType
from app.schemas.auth import LoginRequest, RegisterRequest
from app.schemas.user import UserCreate
import logging

logger = logging.getLogger(__name__)


class AuthService:
    def __init__(self, db: Session):
        self.db = db
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password"""
        user = self.db.query(User).filter(User.email == email).first()
        if not user:
            return None
        if not verify_password(password, user.hashed_password):
            return None
        return user
    
    def create_user(self, user_create: RegisterRequest) -> User:
        """Create a new user account"""
        # Check if user already exists
        if self.db.query(User).filter(User.email == user_create.email).first():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Check if username is taken (if provided)
        if user_create.username:
            if self.db.query(User).filter(User.username == user_create.username).first():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
        
        # Validate passwords match
        if not user_create.passwords_match():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Passwords do not match"
            )
        
        # Create user
        hashed_password = get_password_hash(user_create.password)
        db_user = User(
            email=user_create.email,
            username=user_create.username,
            full_name=user_create.full_name,
            hashed_password=hashed_password,
            role=UserRole.USER,
            is_active=True,
            is_verified=False,  # Require email verification
        )
        
        self.db.add(db_user)
        self.db.commit()
        self.db.refresh(db_user)
        
        # Create free subscription for new user
        self._create_free_subscription(db_user.id)
        
        return db_user
    
    def login_user(self, login_data: LoginRequest) -> dict:
        """Login user and return access token"""
        user = self.authenticate_user(login_data.email, login_data.password)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Inactive user"
            )
        
        # Update last login
        user.last_login = datetime.utcnow()
        self.db.commit()
        
        # Create access token
        access_token = create_access_token(subject=user.id)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": user
        }
    
    def request_password_reset(self, email: str) -> str:
        """Generate password reset token"""
        user = self.db.query(User).filter(User.email == email).first()
        if not user:
            # Don't reveal if email exists or not for security
            raise HTTPException(
                status_code=status.HTTP_200_OK,
                detail="If the email exists, a reset link has been sent"
            )
        
        reset_token = generate_password_reset_token(email)
        
        # TODO: Send email with reset token
        logger.info(f"Password reset requested for {email}")
        
        return reset_token
    
    def reset_password(self, token: str, new_password: str, confirm_password: str) -> bool:
        """Reset password with token"""
        if new_password != confirm_password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Passwords do not match"
            )
        
        email = verify_password_reset_token(token)
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )
        
        user = self.db.query(User).filter(User.email == email).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update password
        user.hashed_password = get_password_hash(new_password)
        self.db.commit()
        
        logger.info(f"Password reset completed for {email}")
        return True
    
    def request_email_verification(self, user: User) -> str:
        """Generate email verification token"""
        verification_token = generate_email_verification_token(user.email)
        
        # TODO: Send verification email
        logger.info(f"Email verification requested for {user.email}")
        
        return verification_token
    
    def verify_email(self, token: str) -> bool:
        """Verify email with token"""
        email = verify_email_verification_token(token)
        if not email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification token"
            )
        
        user = self.db.query(User).filter(User.email == email).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        if user.is_verified:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already verified"
            )
        
        # Mark email as verified
        user.is_verified = True
        self.db.commit()
        
        logger.info(f"Email verified for {email}")
        return True
    
    def change_password(self, user: User, current_password: str, new_password: str) -> bool:
        """Change user password"""
        if not verify_password(current_password, user.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Incorrect current password"
            )
        
        user.hashed_password = get_password_hash(new_password)
        self.db.commit()
        
        logger.info(f"Password changed for user {user.id}")
        return True
    
    def _create_free_subscription(self, user_id: int):
        """Create free subscription for new user"""
        # Get or create free plan
        free_plan = self.db.query(PricingPlan).filter(
            PricingPlan.plan_type == PlanType.FREE
        ).first()
        
        if not free_plan:
            # Create default free plan if it doesn't exist
            free_plan = PricingPlan(
                name="Free",
                plan_type=PlanType.FREE,
                description="Basic plan with limited features",
                price=0.00,
                max_validations=3,
                max_roadmaps=1,
                enhanced_validation=False,
                is_active=True
            )
            self.db.add(free_plan)
            self.db.commit()
            self.db.refresh(free_plan)
        
        # Create user subscription
        subscription = UserSubscription(
            user_id=user_id,
            plan_id=free_plan.id,
            current_period_start=datetime.utcnow(),
            current_period_end=datetime.utcnow() + timedelta(days=30),
        )
        
        self.db.add(subscription)
        self.db.commit()