"""
Real-time performance monitoring and analytics
"""

import asyncio
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import json
from collections import defaultdict, deque
import psutil
import logging

logger = logging.getLogger(__name__)

@dataclass
class ValidationMetrics:
    """Metrics for a single validation"""
    validation_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_duration: Optional[float] = None
    agent_metrics: Dict[str, Dict] = None
    provider_usage: Dict[str, int] = None
    success_rate: float = 0.0
    error_count: int = 0
    cache_hit_rate: float = 0.0
    cost_estimate: float = 0.0

@dataclass
class SystemMetrics:
    """System-wide metrics"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    active_validations: int
    queue_size: int
    avg_response_time: float
    provider_health: Dict[str, bool]
    error_rate: float

class PerformanceMonitor:
    """Real-time performance monitoring"""
    
    def __init__(self):
        self.active_validations: Dict[str, ValidationMetrics] = {}
        self.completed_validations: deque = deque(maxlen=1000)  # Keep last 1000
        self.system_metrics: deque = deque(maxlen=288)  # 24 hours of 5-min intervals
        self.provider_stats = defaultdict(lambda: {"requests": 0, "failures": 0, "avg_time": 0})
        self.agent_stats = defaultdict(lambda: {"executions": 0, "failures": 0, "avg_time": 0})
        
    def start_validation(self, validation_id: str) -> ValidationMetrics:
        """Start tracking a validation"""
        metrics = ValidationMetrics(
            validation_id=validation_id,
            start_time=datetime.now(),
            agent_metrics={},
            provider_usage=defaultdict(int)
        )
        self.active_validations[validation_id] = metrics
        logger.info(f"📊 Started tracking validation: {validation_id}")
        return metrics
    
    def complete_validation(self, validation_id: str, success: bool = True):
        """Complete validation tracking"""
        if validation_id in self.active_validations:
            metrics = self.active_validations[validation_id]
            metrics.end_time = datetime.now()
            metrics.total_duration = (metrics.end_time - metrics.start_time).total_seconds()
            
            # Calculate success rate
            total_agents = len(metrics.agent_metrics)
            successful_agents = sum(1 for m in metrics.agent_metrics.values() if m.get('success', False))
            metrics.success_rate = successful_agents / total_agents if total_agents > 0 else 0
            
            # Move to completed
            self.completed_validations.append(metrics)
            del self.active_validations[validation_id]
            
            logger.info(f"✅ Completed tracking validation: {validation_id} ({metrics.total_duration:.2f}s)")
    
    def track_agent_execution(self, validation_id: str, agent_key: str, 
                            duration: float, success: bool, provider: str = None):
        """Track individual agent execution"""
        if validation_id in self.active_validations:
            metrics = self.active_validations[validation_id]
            
            agent_metric = {
                'duration': duration,
                'success': success,
                'provider': provider,
                'timestamp': datetime.now().isoformat()
            }
            
            metrics.agent_metrics[agent_key] = agent_metric
            
            if provider:
                metrics.provider_usage[provider] += 1
            
            # Update global stats
            self.agent_stats[agent_key]["executions"] += 1
            self.agent_stats[agent_key]["avg_time"] = (
                (self.agent_stats[agent_key]["avg_time"] * (self.agent_stats[agent_key]["executions"] - 1) + duration) /
                self.agent_stats[agent_key]["executions"]
            )
            
            if not success:
                self.agent_stats[agent_key]["failures"] += 1
                metrics.error_count += 1
    
    def track_provider_usage(self, provider: str, duration: float, success: bool):
        """Track provider performance"""
        stats = self.provider_stats[provider]
        stats["requests"] += 1
        stats["avg_time"] = ((stats["avg_time"] * (stats["requests"] - 1) + duration) / stats["requests"])
        
        if not success:
            stats["failures"] += 1
    
    def get_system_metrics(self) -> SystemMetrics:
        """Get current system metrics"""
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_usage=psutil.cpu_percent(),
            memory_usage=psutil.virtual_memory().percent,
            active_validations=len(self.active_validations),
            queue_size=0,  # TODO: Implement queue monitoring
            avg_response_time=self._calculate_avg_response_time(),
            provider_health=self._get_provider_health(),
            error_rate=self._calculate_error_rate()
        )
    
    def _calculate_avg_response_time(self) -> float:
        """Calculate average response time from recent validations"""
        if not self.completed_validations:
            return 0.0
        
        recent = [v for v in self.completed_validations if v.total_duration][-10:]  # Last 10
        return sum(v.total_duration for v in recent) / len(recent) if recent else 0.0
    
    def _get_provider_health(self) -> Dict[str, bool]:
        """Get provider health status"""
        health = {}
        for provider, stats in self.provider_stats.items():
            if stats["requests"] > 0:
                failure_rate = stats["failures"] / stats["requests"]
                health[provider] = failure_rate < 0.1  # Healthy if < 10% failure rate
        return health
    
    def _calculate_error_rate(self) -> float:
        """Calculate overall error rate"""
        if not self.completed_validations:
            return 0.0
        
        recent = list(self.completed_validations)[-50:]  # Last 50 validations
        total_errors = sum(v.error_count for v in recent)
        total_agents = sum(len(v.agent_metrics) for v in recent)
        
        return total_errors / total_agents if total_agents > 0 else 0.0
    
    def get_analytics_dashboard(self) -> Dict[str, Any]:
        """Get comprehensive analytics for dashboard"""
        return {
            "system": asdict(self.get_system_metrics()),
            "active_validations": len(self.active_validations),
            "completed_today": len([v for v in self.completed_validations 
                                  if v.end_time and v.end_time.date() == datetime.now().date()]),
            "avg_validation_time": self._calculate_avg_response_time(),
            "success_rate": 1 - self._calculate_error_rate(),
            "provider_stats": dict(self.provider_stats),
            "agent_stats": dict(self.agent_stats),
            "top_performing_agents": self._get_top_agents(),
            "cost_analysis": self._get_cost_analysis()
        }
    
    def _get_top_agents(self) -> List[Dict[str, Any]]:
        """Get top performing agents"""
        agents = []
        for agent, stats in self.agent_stats.items():
            if stats["executions"] > 0:
                success_rate = 1 - (stats["failures"] / stats["executions"])
                agents.append({
                    "agent": agent,
                    "success_rate": success_rate,
                    "avg_time": stats["avg_time"],
                    "executions": stats["executions"]
                })
        
        return sorted(agents, key=lambda x: x["success_rate"], reverse=True)[:5]
    
    def _get_cost_analysis(self) -> Dict[str, Any]:
        """Analyze costs by provider"""
        # Simplified cost calculation - would need actual pricing data
        provider_costs = {
            "openai": 0.002,      # per 1k tokens
            "anthropic": 0.008,   # per 1k tokens
            "deepseek": 0.0002,   # per 1k tokens
            "gemini": 0.001,      # per 1k tokens
            "perplexity": 0.001   # per 1k tokens
        }
        
        total_cost = 0
        cost_breakdown = {}
        
        for provider, stats in self.provider_stats.items():
            # Estimate tokens per request (rough estimate)
            estimated_tokens = stats["requests"] * 2000  # 2k tokens per request
            cost = (estimated_tokens / 1000) * provider_costs.get(provider.lower(), 0.001)
            cost_breakdown[provider] = cost
            total_cost += cost
        
        return {
            "total_estimated_cost": total_cost,
            "cost_by_provider": cost_breakdown,
            "cost_per_validation": total_cost / max(len(self.completed_validations), 1)
        }

# Global monitor instance
performance_monitor = PerformanceMonitor()
