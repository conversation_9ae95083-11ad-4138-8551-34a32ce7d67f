"""
Refactored AI service using modular components
"""

from typing import Dict, List, Any
import logging
import json
import asyncio

from app.services.ai_providers import AIProvider
from app.services.ai_providers.manager import AIProviderManager
from app.services.external_tools.manager import ExternalToolsManager
from app.services.ai_agents.manager import AgentManager

logger = logging.getLogger(__name__)


class AIService:
    """Refactored multi-provider AI service for idea validation and roadmap generation"""
    
    def __init__(self, provider: AIProvider = AIProvider.OPENAI):
        self.current_provider = provider
        self.provider_manager = AIProviderManager()
        self.tools_manager = ExternalToolsManager()
        self.agent_manager = AgentManager()
    
    def get_available_providers(self) -> List[AIProvider]:
        """Get list of available providers"""
        return self.provider_manager.get_available_providers()
    
    async def validate_idea(
        self, 
        idea: str, 
        answers: dict, 
        provider: AIProvider = None,
        max_tokens: int = 2000
    ) -> str:
        """Simple idea validation using specified provider"""
        try:
            # Use specified provider or fallback to current
            target_provider = provider or self.current_provider
            
            # Ensure provider is available
            if not self.provider_manager.is_provider_available(target_provider):
                available_providers = self.get_available_providers()
                if not available_providers:
                    raise Exception("No AI providers available")
                target_provider = available_providers[0]
            
            # Create validation prompt
            prompt = self._create_validation_prompt(idea, answers)
            
            # Generate response
            response = await self.provider_manager.generate_response(
                target_provider, prompt, max_tokens
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Idea validation failed: {str(e)}")
            return self._create_fallback_response(idea, str(e))
    
    async def comprehensive_idea_validation(
        self, 
        idea: str, 
        answers: dict, 
        streaming_callback=None
    ) -> str:
        """Run comprehensive idea validation using multi-agent approach"""
        try:
            logger.info("Starting comprehensive multi-agent validation...")
            
            # Run comprehensive validation using agent manager
            validation_results = await self.agent_manager.run_comprehensive_validation(
                idea, answers, streaming_callback
            )
            
            # Convert results to JSON string for compatibility
            return json.dumps(validation_results, indent=2)
            
        except Exception as e:
            logger.error(f"Comprehensive validation failed: {str(e)}")
            return self._create_fallback_response(idea, str(e))
    
    async def comprehensive_idea_validation_stream(
        self,
        idea: str,
        answers: dict,
        use_celery: bool = False
    ):
        """Stream comprehensive idea validation with real-time updates"""
        try:
            logger.info(f"Starting streaming comprehensive validation... (use_celery: {use_celery})")

            # Run comprehensive validation with streaming
            async for update in self._stream_comprehensive_validation(idea, answers, use_celery):
                yield update

        except Exception as e:
            logger.error(f"Streaming validation failed: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
    
    async def _stream_comprehensive_validation(self, idea: str, answers: dict, use_celery: bool = False):
        """Internal method for streaming validation updates"""
        updates_queue = asyncio.Queue()
        validation_completed = False

        async def callback(update):
            logger.info(f"🔄 Streaming callback received: {update.get('type', 'unknown')}")
            await updates_queue.put(update)

        # Send initial status
        yield f"data: {json.dumps({'type': 'status', 'message': f'Starting validation with use_celery={use_celery}'})}\n\n"

        # Start validation task with Celery support
        logger.info(f"🚀 Starting validation task with use_celery={use_celery}")
        validation_task = asyncio.create_task(
            self.agent_manager.run_comprehensive_validation(idea, answers, callback, use_celery)
        )

        # Stream updates as they come with overall timeout
        start_time = asyncio.get_event_loop().time()
        max_duration = 120  # 2 minutes max

        while not validation_completed:
            try:
                # Check overall timeout
                if asyncio.get_event_loop().time() - start_time > max_duration:
                    logger.warning("⏰ Validation timeout after 2 minutes")
                    yield f"data: {json.dumps({'type': 'error', 'message': 'Validation timeout'})}\n\n"
                    break

                # Wait for update with timeout
                update = await asyncio.wait_for(updates_queue.get(), timeout=2.0)

                # Check if this is the completion message
                if update.get('type') == 'validation_complete':
                    validation_completed = True

                yield f"data: {json.dumps(update)}\n\n"

            except asyncio.TimeoutError:
                # Check if task is done
                if validation_task.done():
                    break
                # Send heartbeat if no updates
                yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"

        # Ensure task is completed with timeout
        try:
            await asyncio.wait_for(validation_task, timeout=120)  # 2 minute timeout
        except asyncio.TimeoutError:
            logger.error("⏰ Validation task timed out")
            validation_task.cancel()
            yield f"data: {json.dumps({'type': 'error', 'message': 'Validation task timed out'})}\n\n"
        except Exception as e:
            logger.error(f"Validation task failed: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
    
    async def mixture_of_agents_validation(
        self, 
        idea: str, 
        answers: dict, 
        max_tokens: int = 3000
    ) -> str:
        """Run mixture-of-agents validation using multiple providers"""
        try:
            available_providers = self.get_available_providers()
            
            # Remove mixture-of-agents from the list for actual execution
            execution_providers = [p for p in available_providers if p != AIProvider.MIXTURE_OF_AGENTS]
            
            if len(execution_providers) < 2:
                # Fallback to single provider
                return await self.validate_idea(idea, answers)
            
            # Create validation prompt
            prompt = self._create_validation_prompt(idea, answers)
            
            # Run multiple providers in parallel
            tasks = []
            for provider in execution_providers[:4]:  # Limit to 4 providers
                task = self.provider_manager.generate_response(provider, prompt, max_tokens)
                tasks.append((provider, task))
            
            # Get external research
            research_task = self.tools_manager.research_idea(idea, answers)
            
            # Wait for all results
            provider_results = []
            for provider, task in tasks:
                try:
                    result = await task
                    provider_results.append({
                        "provider": provider,
                        "analysis": result
                    })
                except Exception as e:
                    logger.error(f"Provider {provider} failed: {str(e)}")
            
            # Get external research
            try:
                external_research = await research_task
                research_summary = self.tools_manager.format_research_summary(external_research)
            except Exception as e:
                logger.error(f"External research failed: {str(e)}")
                research_summary = "External research unavailable"
            
            # Consolidate results using best available provider
            consolidation_prompt = self._create_consolidation_prompt(
                idea, provider_results, research_summary
            )
            
            # Use Anthropic for consolidation if available, otherwise use first available
            consolidation_provider = (AIProvider.ANTHROPIC 
                                    if self.provider_manager.is_provider_available(AIProvider.ANTHROPIC)
                                    else execution_providers[0])
            
            final_result = await self.provider_manager.generate_response(
                consolidation_provider, consolidation_prompt, max_tokens
            )
            
            return final_result
            
        except Exception as e:
            logger.error(f"Mixture of agents validation failed: {str(e)}")
            return self._create_fallback_response(idea, str(e))
    
    def _create_validation_prompt(self, idea: str, answers: dict) -> str:
        """Create validation prompt from idea and answers"""
        context = self._format_answers_for_prompt(answers)
        
        return f"""
Provide a comprehensive startup idea validation for: {idea}

Context: {context}

Analyze the following aspects:
1. Market opportunity and size
2. Target audience and user personas
3. Competitive landscape
4. Technical feasibility
5. Business model potential
6. Key risks and challenges
7. Recommendations for next steps

Provide a structured analysis with actionable insights and a validation score (1-10).
"""
    
    def _create_consolidation_prompt(
        self, 
        idea: str, 
        provider_results: List[Dict], 
        research_summary: str
    ) -> str:
        """Create prompt for consolidating multiple provider results"""
        analyses = "\n\n".join([
            f"Analysis from {result['provider']}:\n{result['analysis']}"
            for result in provider_results
        ])
        
        return f"""
You are a senior business analyst consolidating multiple AI analyses for the idea: {idea}

Multiple AI Provider Analyses:
{analyses}

External Research Summary:
{research_summary}

Your task is to synthesize these analyses into a comprehensive, coherent validation report that:
1. Combines the best insights from each analysis
2. Resolves any contradictions with reasoned judgment
3. Incorporates external research findings
4. Provides a final validation score and clear recommendations

Provide a well-structured JSON report with executive summary, detailed analysis, and actionable next steps.
"""
    
    def _format_answers_for_prompt(self, answers: dict) -> str:
        """Format answers dictionary for inclusion in prompts"""
        if not answers:
            return "No additional context provided."
        
        formatted = []
        for key, value in answers.items():
            if value:
                formatted_key = key.replace('_', ' ').title()
                formatted.append(f"{formatted_key}: {value}")
        
        return "\n".join(formatted) if formatted else "No additional context provided."
    
    async def generate_idea_questions(self, idea_description: str) -> Dict[str, Any]:
        """Generate short, dynamic questions with mandatory geo/tech questions"""
        prompt = f"""
        Based on the following idea description, generate 3 short, specific questions to better understand this idea for validation.

        REQUIREMENTS:
        - Questions must be SHORT (max 8-10 words)
        - Examples go in placeholder, NOT in the question text
        - Focus on market, business model, and specific challenges
        - Use only text inputs (textarea or text)

        Idea Description: {idea_description}

        Return this exact JSON format with the first 2 questions being MANDATORY geography and tech questions:
        {{
            "questions": [
                {{
                    "id": "geographic_scope",
                    "question": "What's your target geographic market?",
                    "type": "text",
                    "placeholder": "e.g., Global, USA, Europe, India, California, specific cities...",
                    "required": true
                }},
                {{
                    "id": "tech_involvement",
                    "question": "What technology will be involved?",
                    "type": "text",
                    "placeholder": "e.g., Mobile app, Web platform, AI/ML, Blockchain, IoT, SaaS...",
                    "required": true
                }},
                {{
                    "id": "custom_question_1",
                    "question": "[Generate a specific question based on the idea]",
                    "type": "text",
                    "placeholder": "[Relevant example]",
                    "required": false
                }}
            ]
        }}
        """

        try:
            # Use Deepseek for question generation
            response = await self.provider_manager.generate_response(
                AIProvider.DEEPSEEK, prompt, max_tokens=800
            )

            # Try to parse as JSON
            try:
                questions_data = json.loads(response)
                return questions_data
            except json.JSONDecodeError:
                # Fallback questions if parsing fails
                return {
                    "questions": [
                        {
                            "id": "geographic_scope",
                            "question": "What's your target geographic market?",
                            "type": "text",
                            "placeholder": "e.g., Global, USA, Europe, India, California, specific cities...",
                            "required": True
                        },
                        {
                            "id": "tech_involvement",
                            "question": "What technology will be involved?",
                            "type": "text",
                            "placeholder": "e.g., Mobile app, Web platform, AI/ML, Blockchain, IoT, SaaS...",
                            "required": True
                        },
                        {
                            "id": "target_market",
                            "question": "Who is your target audience?",
                            "type": "text",
                            "placeholder": "e.g., Small businesses, Consumers, Enterprise, Students...",
                            "required": False
                        }
                    ]
                }

        except Exception as e:
            logger.error(f"Question generation failed: {str(e)}")
            # Return fallback questions
            return {
                "questions": [
                    {
                        "id": "geographic_scope",
                        "question": "What's your target geographic market?",
                        "type": "text",
                        "placeholder": "e.g., Global, USA, Europe, India, California, specific cities...",
                        "required": True
                    },
                    {
                        "id": "tech_involvement",
                        "question": "What technology will be involved?",
                        "type": "text",
                        "placeholder": "e.g., Mobile app, Web platform, AI/ML, Blockchain, IoT, SaaS...",
                        "required": True
                    },
                    {
                        "id": "business_model",
                        "question": "How will you make money?",
                        "type": "text",
                        "placeholder": "e.g., Subscription, One-time purchase, Freemium, Advertising...",
                        "required": False
                    }
                ]
            }

    def _create_fallback_response(self, idea: str, error_message: str) -> str:
        """Create fallback response when validation fails"""
        return json.dumps({
            "validation_summary": {
                "overall_score": 5.0,
                "status": "error",
                "message": "Validation analysis temporarily unavailable"
            },
            "error": error_message,
            "recommendations": [
                "Please try again later",
                "Check API key configurations",
                "Consider using a different AI provider"
            ],
            "idea": idea
        }, indent=2)
