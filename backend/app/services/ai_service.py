"""
AI service for multi-provider AI integration with OpenAI, Anthropic, Deepseek, and Gemini
"""

import openai
import anthropic
import google.generativeai as genai
from typing import Dict, List, Optional, Any, AsyncGenerator
from app.core.config import settings
import logging
import json
import asyncio
from enum import Enum
import httpx
from tavily import <PERSON>ly<PERSON><PERSON>
from firecrawl import FirecrawlApp

logger = logging.getLogger(__name__)


class AIProvider(str, Enum):
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"
    PERPLEXITY = "perplexity"
    MIXTURE_OF_AGENTS = "mixture-of-agents"


class AIService:
    """Multi-provider AI service for idea validation and roadmap generation"""
    
    def __init__(self, provider: AIProvider = AIProvider.OPENAI):
        self.current_provider = provider
        self._setup_clients()
    
    def _setup_clients(self):
        """Setup AI provider clients"""
        # OpenAI
        if settings.openai_api_key and settings.openai_api_key != "your-openai-api-key":
            self.openai_client = openai.OpenAI(api_key=settings.openai_api_key)
        else:
            self.openai_client = None
            
        # Anthropic
        if settings.anthropic_api_key and settings.anthropic_api_key != "your-anthropic-api-key":
            self.anthropic_client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
        else:
            self.anthropic_client = None
            
        # Gemini
        if settings.gemini_api_key and settings.gemini_api_key != "your-gemini-api-key":
            genai.configure(api_key=settings.gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
        else:
            self.gemini_model = None
            
        # Deepseek (uses OpenAI-compatible API)
        if settings.deepseek_api_key and settings.deepseek_api_key != "your-deepseek-api-key":
            self.deepseek_client = openai.OpenAI(
                api_key=settings.deepseek_api_key,
                base_url="https://api.deepseek.com"
            )
        else:
            self.deepseek_client = None
            
        # Perplexity (uses OpenAI-compatible API)
        if settings.perplexity_api_key and settings.perplexity_api_key != "your-perplexity-api-key":
            self.perplexity_client = openai.OpenAI(
                api_key=settings.perplexity_api_key,
                base_url="https://api.perplexity.ai"
            )
        else:
            self.perplexity_client = None
            
        # External tools
        self.tavily_client = None
        self.firecrawl_client = None
        
        if settings.tavily_api_key and settings.tavily_api_key != "your-tavily-api-key":
            self.tavily_client = TavilyClient(api_key=settings.tavily_api_key)
            
        if settings.firecrawl_api_key and settings.firecrawl_api_key != "your-firecrawl-api-key":
            self.firecrawl_client = FirecrawlApp(api_key=settings.firecrawl_api_key)
    
    def _get_available_providers(self) -> List[AIProvider]:
        """Get list of available providers based on configured API keys"""
        providers = []
        if self.openai_client:
            providers.append(AIProvider.OPENAI)
        if self.anthropic_client:
            providers.append(AIProvider.ANTHROPIC)
        if self.gemini_model:
            providers.append(AIProvider.GEMINI)
        if self.deepseek_client:
            providers.append(AIProvider.DEEPSEEK)
        if self.perplexity_client:
            providers.append(AIProvider.PERPLEXITY)
        
        # Add mixture-of-agents if at least 2 providers and anthropic available
        if len(providers) >= 2 and self.anthropic_client:
            providers.append(AIProvider.MIXTURE_OF_AGENTS)
            
        return providers
    
    async def _search_with_tavily(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """Search using Tavily API"""
        if not self.tavily_client:
            return {"results": [], "error": "Tavily client not configured"}
        
        try:
            response = self.tavily_client.search(
                query=query,
                search_depth="advanced",
                max_results=max_results,
                include_answer=True,
                include_domains=[],
                exclude_domains=[]
            )
            return response
        except Exception as e:
            logger.error(f"Tavily search error: {str(e)}")
            return {"results": [], "error": str(e)}
    
    async def _scrape_with_firecrawl(self, urls: List[str]) -> List[Dict[str, Any]]:
        """Scrape URLs using Firecrawl"""
        if not self.firecrawl_client:
            return [{"url": url, "content": "", "error": "Firecrawl client not configured"} for url in urls]
        
        scraped_data = []
        for url in urls[:3]:  # Limit to 3 URLs to avoid quota issues
            try:
                response = self.firecrawl_client.scrape_url(
                    url=url,
                    params={
                        "formats": ["markdown"],
                        "extract": {
                            "schema": {
                                "type": "object",
                                "properties": {
                                    "title": {"type": "string"},
                                    "description": {"type": "string"},
                                    "key_points": {"type": "array", "items": {"type": "string"}}
                                }
                            }
                        }
                    }
                )
                scraped_data.append({
                    "url": url,
                    "content": response.get("markdown", ""),
                    "extract": response.get("extract", {}),
                    "error": None
                })
            except Exception as e:
                logger.error(f"Firecrawl scrape error for {url}: {str(e)}")
                scraped_data.append({
                    "url": url,
                    "content": "",
                    "extract": {},
                    "error": str(e)
                })
        
        return scraped_data
    
    async def _call_single_provider(self, prompt: str, provider: AIProvider, max_tokens: int = 2000) -> Dict[str, Any]:
        """Call a single AI provider and return structured response"""
        try:
            if provider == AIProvider.OPENAI and self.openai_client:
                response = self.openai_client.chat.completions.create(
                    model="gpt-4.1-2025-04-14",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                return {
                    "provider": provider.value,
                    "response": response.choices[0].message.content,
                    "success": True,
                    "error": None
                }
                
            elif provider == AIProvider.ANTHROPIC and self.anthropic_client:
                response = self.anthropic_client.messages.create(
                    model="claude-3-5-sonnet-20241022",  # Use available model
                    max_tokens=max_tokens,
                    messages=[{"role": "user", "content": prompt}]
                )
                return {
                    "provider": provider.value,
                    "response": response.content[0].text,
                    "success": True,
                    "error": None
                }
                
            elif provider == AIProvider.GEMINI and self.gemini_model:
                response = self.gemini_model.generate_content(prompt)
                return {
                    "provider": provider.value,
                    "response": response.text,
                    "success": True,
                    "error": None
                }
                
            elif provider == AIProvider.DEEPSEEK and self.deepseek_client:
                response = self.deepseek_client.chat.completions.create(
                    model="deepseek-reasoner",  # Updated to deepseek-reasoner
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                return {
                    "provider": provider.value,
                    "response": response.choices[0].message.content,
                    "success": True,
                    "error": None
                }
                
            elif provider == AIProvider.PERPLEXITY and self.perplexity_client:
                response = self.perplexity_client.chat.completions.create(
                    model="llama-3.1-sonar-huge-128k-online",  # Use Perplexity's online model
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=max_tokens,
                    temperature=0.7
                )
                return {
                    "provider": provider.value,
                    "response": response.choices[0].message.content,
                    "success": True,
                    "error": None
                }
            else:
                return {
                    "provider": provider.value,
                    "response": "",
                    "success": False,
                    "error": f"Provider {provider} not available or not configured"
                }
                
        except Exception as e:
            logger.error(f"Error calling AI provider {provider}: {str(e)}")
            return {
                "provider": provider.value,
                "response": "",
                "success": False,
                "error": str(e)
            }
    
    async def _mixture_of_agents_validation(self, idea: str, answers: dict, max_tokens: int = 3000) -> str:
        """Run mixture-of-agents validation: use 4 specific models + external tools in parallel, then consolidate with Anthropic"""
        
        # Define the 4 specific providers for mixture-of-agents
        target_providers = [AIProvider.DEEPSEEK, AIProvider.OPENAI, AIProvider.ANTHROPIC, AIProvider.PERPLEXITY]
        
        # Check which providers are available - USE ANTHROPIC AS PRIMARY FALLBACK
        available_providers = []
        for provider in target_providers:
            if provider == AIProvider.DEEPSEEK and self.deepseek_client:
                available_providers.append(provider)
            elif provider == AIProvider.OPENAI and self.openai_client:
                available_providers.append(provider)
            elif provider == AIProvider.ANTHROPIC and self.anthropic_client:
                available_providers.append(provider)
            elif provider == AIProvider.PERPLEXITY and self.perplexity_client:
                available_providers.append(provider)
        
        if len(available_providers) < 1:
            raise Exception("Mixture-of-agents validation requires at least one AI provider to be configured")
        
        # Create validation prompt
        validation_prompt = f"""
        You are an expert business analyst conducting a comprehensive idea validation. Analyze the following idea and additional context:
        
        Idea Description: {idea}
        
        Additional Context:
        {self._format_answers_for_prompt(answers)}
        
        Provide a detailed analysis covering:
        1. Market Opportunity Assessment
        2. Technical Feasibility Analysis  
        3. Competitive Landscape Review
        4. Risk Assessment and Mitigation
        5. Business Model Recommendations
        6. Go-to-Market Strategy
        7. Financial Projections and Investment Requirements
        8. Success Metrics and KPIs
        
        Be thorough, specific, and provide actionable insights. Include relevant market data, trends, and evidence-based recommendations.
        """
        
        # Extract search query for external tools
        search_query = self._extract_search_query_from_idea(idea)
        
        # Run all providers and external tools in parallel
        tasks = []
        
        # Add AI provider tasks
        for provider in available_providers:
            task = self._call_single_provider(validation_prompt, provider, max_tokens)
            tasks.append(task)
        
        # Add external tool tasks
        if self.tavily_client and search_query:
            tasks.append(self._search_with_tavily(search_query, max_results=8))
        
        # Execute all tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Separate AI responses from external tool results
        ai_responses = []
        external_data = ""
        
        for i, result in enumerate(results):
            if i < len(available_providers):  # AI provider response
                if isinstance(result, dict) and result.get("success", False):
                    ai_responses.append(result)
            else:  # External tool result
                if isinstance(result, dict) and "results" in result:
                    tavily_data = result
                    if tavily_data.get("results"):
                        external_data += "\n\n--- Market Research Data (Tavily) ---\n"
                        for item in tavily_data["results"][:5]:
                            external_data += f"Title: {item.get('title', '')}\n"
                            external_data += f"Content: {item.get('content', '')}\n"
                            external_data += f"URL: {item.get('url', '')}\n\n"
        
        # Get additional content via Firecrawl if we have URLs
        if external_data and self.firecrawl_client:
            # Extract URLs from Tavily results for scraping
            urls_to_scrape = []
            for result_item in results:
                if isinstance(result_item, dict) and "results" in result_item:
                    for item in result_item["results"][:3]:  # Limit to top 3 URLs
                        if item.get('url'):
                            urls_to_scrape.append(item['url'])
            
            if urls_to_scrape:
                scraped_data = await self._scrape_with_firecrawl(urls_to_scrape)
                if scraped_data:
                    external_data += "\n\n--- Detailed Content Analysis (Firecrawl) ---\n"
                    for item in scraped_data:
                        if item.get("content") and not item.get("error"):
                            external_data += f"URL: {item['url']}\n"
                            external_data += f"Content: {item['content'][:1200]}...\n\n"
        
        if not ai_responses:
            raise Exception("All AI providers failed in mixture-of-agents validation")
        
        # Use Anthropic for final consolidation
        if not self.anthropic_client:
            # Fallback to first successful response if Anthropic not available
            return ai_responses[0]["response"]
        
        # Create comprehensive consolidation prompt
        consolidation_prompt = f"""
        You are conducting the final consolidation of a comprehensive idea validation analysis. You have multiple AI analyses and real-time market research data.
        
        Original Idea: {idea}
        
        Additional Context: {self._format_answers_for_prompt(answers)}
        
        AI ANALYSES TO CONSOLIDATE:
        """
        
        for i, resp in enumerate(ai_responses, 1):
            consolidation_prompt += f"\n\n--- Analysis {i} (from {resp['provider']}) ---\n{resp['response']}"
        
        if external_data:
            consolidation_prompt += f"\n\nEXTERNAL MARKET RESEARCH:\n{external_data}"
        
        consolidation_prompt += """
        
        CONSOLIDATION INSTRUCTIONS:
        Create a comprehensive, professional idea validation report that:
        
        1. **EXECUTIVE SUMMARY**: 2-3 paragraph overview with key findings and recommendation
        
        2. **MARKET ANALYSIS**: 
           - Market size and growth potential
           - Target audience analysis  
           - Current trends and opportunities
           - Geographic considerations
        
        3. **COMPETITIVE LANDSCAPE**:
           - Direct and indirect competitors
           - Competitive advantages and positioning
           - Market gaps and opportunities
        
        4. **TECHNICAL ASSESSMENT**:
           - Technical feasibility
           - Technology requirements and complexity
           - Development timeline estimates
        
        5. **BUSINESS MODEL & MONETIZATION**:
           - Revenue streams
           - Pricing strategy
           - Cost structure analysis
        
        6. **RISK ANALYSIS**:
           - Key risks and challenges
           - Mitigation strategies
           - Success probability assessment
        
        7. **RECOMMENDATIONS & NEXT STEPS**:
           - Go/No-go recommendation with rationale
           - Immediate action items
           - Success metrics to track
        
        8. **FINANCIAL OUTLOOK**:
           - Investment requirements
           - Revenue projections
           - Break-even analysis
        
        Synthesize all the AI analyses and market research data into a cohesive, actionable report. Resolve any contradictions intelligently and highlight the most critical insights. Be specific, data-driven, and provide clear recommendations.
        
        Format the response as a well-structured report with clear headings and bullet points where appropriate.
        """
        
        try:
            consolidation_response = self.anthropic_client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=max_tokens,
                messages=[{"role": "user", "content": consolidation_prompt}]
            )
            return consolidation_response.content[0].text
        except Exception as e:
            logger.error(f"Consolidation failed: {str(e)}")
            # Fallback to first successful response
            return ai_responses[0]["response"]
    
    async def _call_ai_provider(self, prompt: str, provider: Optional[AIProvider] = None, max_tokens: int = 2000, use_external_tools: bool = True) -> str:
        """Call the specified AI provider with the given prompt, optionally using external tools"""
        if provider is None:
            provider = self.current_provider
        
        # Handle mixture-of-agents
        if provider == AIProvider.MIXTURE_OF_AGENTS:
            return await self._mixture_of_agents_with_tools(prompt, max_tokens, use_external_tools)
            
        try:
            # For regular providers, use external tools if enabled
            if use_external_tools and (self.tavily_client or self.firecrawl_client):
                return await self._call_provider_with_tools(prompt, provider, max_tokens)
            else:
                # Direct call without external tools
                return await self._call_provider_direct(prompt, provider, max_tokens)
                
        except Exception as e:
            logger.error(f"Error calling AI provider {provider}: {str(e)}")
            # Fallback to another provider if available
            available_providers = self._get_available_providers()
            if available_providers and provider in available_providers:
                available_providers.remove(provider)
            
            if available_providers:
                fallback_provider = available_providers[0]
                if fallback_provider != AIProvider.MIXTURE_OF_AGENTS:
                    logger.info(f"Falling back to provider: {fallback_provider}")
                    return await self._call_ai_provider(prompt, fallback_provider, max_tokens, use_external_tools)
            
            raise Exception("No AI providers available or all providers failed")
    
    async def _call_provider_direct(self, prompt: str, provider: AIProvider, max_tokens: int = 2000) -> str:
        """Direct call to AI provider without external tools"""
        response_data = await self._call_single_provider(prompt, provider, max_tokens)
        if response_data["success"]:
            return response_data["response"]
        else:
            raise Exception(response_data["error"])
    
    async def _call_provider_with_tools(self, prompt: str, provider: AIProvider, max_tokens: int = 2000) -> str:
        """Call AI provider enhanced with external tools data"""
        # Extract search query from prompt (simple heuristic)
        search_query = self._extract_search_query(prompt)
        
        # Run external tools in parallel
        tools_tasks = []
        if self.tavily_client and search_query:
            tools_tasks.append(self._search_with_tavily(search_query))
        
        # Execute tools
        tools_results = []
        if tools_tasks:
            tools_results = await asyncio.gather(*tools_tasks, return_exceptions=True)
        
        # Process tools results
        external_context = ""
        urls_to_scrape = []
        
        for result in tools_results:
            if isinstance(result, dict) and "results" in result:
                tavily_data = result
                if tavily_data.get("results"):
                    external_context += "\n\n--- Market Research Data (Tavily) ---\n"
                    for item in tavily_data["results"][:3]:
                        external_context += f"Title: {item.get('title', '')}\n"
                        external_context += f"Content: {item.get('content', '')}\n"
                        external_context += f"URL: {item.get('url', '')}\n\n"
                        
                        # Collect URLs for scraping
                        if item.get('url'):
                            urls_to_scrape.append(item['url'])
        
        # Scrape additional content if we have URLs
        if urls_to_scrape and self.firecrawl_client:
            scraped_data = await self._scrape_with_firecrawl(urls_to_scrape[:2])  # Limit to 2 URLs
            if scraped_data:
                external_context += "\n\n--- Detailed Content Analysis (Firecrawl) ---\n"
                for item in scraped_data:
                    if item.get("content") and not item.get("error"):
                        external_context += f"URL: {item['url']}\n"
                        external_context += f"Content: {item['content'][:1000]}...\n\n"  # Limit content length
        
        # Enhance prompt with external context
        enhanced_prompt = prompt
        if external_context:
            enhanced_prompt = f"""
            {prompt}
            
            --- ADDITIONAL CONTEXT FROM EXTERNAL RESEARCH ---
            {external_context}
            
            Please incorporate the above external research data into your analysis where relevant, 
            but prioritize accuracy and ensure all information is properly validated.
            """
        
        # Call the AI provider with enhanced prompt
        return await self._call_provider_direct(enhanced_prompt, provider, max_tokens)
    
    async def _mixture_of_agents_with_tools(self, prompt: str, max_tokens: int = 2000, use_external_tools: bool = True) -> str:
        """Mixture-of-agents enhanced with external tools"""
        # First, gather external context if enabled
        external_context = ""
        if use_external_tools:
            search_query = self._extract_search_query(prompt)
            
            # Run external tools
            tools_tasks = []
            if self.tavily_client and search_query:
                tools_tasks.append(self._search_with_tavily(search_query))
            
            if tools_tasks:
                tools_results = await asyncio.gather(*tools_tasks, return_exceptions=True)
                
                # Process tools results
                urls_to_scrape = []
                for result in tools_results:
                    if isinstance(result, dict) and "results" in result:
                        tavily_data = result
                        if tavily_data.get("results"):
                            external_context += "\n\n--- External Research Data ---\n"
                            for item in tavily_data["results"][:3]:
                                external_context += f"Title: {item.get('title', '')}\n"
                                external_context += f"Content: {item.get('content', '')}\n"
                                external_context += f"URL: {item.get('url', '')}\n\n"
                                
                                if item.get('url'):
                                    urls_to_scrape.append(item['url'])
                
                # Scrape additional content
                if urls_to_scrape and self.firecrawl_client:
                    scraped_data = await self._scrape_with_firecrawl(urls_to_scrape[:2])
                    if scraped_data:
                        external_context += "\n\n--- Detailed Analysis ---\n"
                        for item in scraped_data:
                            if item.get("content") and not item.get("error"):
                                external_context += f"URL: {item['url']}\n"
                                external_context += f"Content: {item['content'][:800]}...\n\n"
        
        # Enhance prompt with external context
        enhanced_prompt = prompt
        if external_context:
            enhanced_prompt = f"""
            {prompt}
            
            --- ADDITIONAL CONTEXT FROM EXTERNAL RESEARCH ---
            {external_context}
            
            Please incorporate this external research data into your analysis where relevant.
            """
        
        # Run mixture-of-agents with enhanced prompt
        return await self._mixture_of_agents(enhanced_prompt, max_tokens)
    
    def _extract_search_query(self, prompt: str) -> str:
        """Extract a search query from the prompt for external tools"""
        # Simple heuristic to extract key terms for search
        # Look for project titles, company names, or key concepts
        
        lines = prompt.split('\n')
        search_terms = []
        
        for line in lines:
            if 'Title:' in line:
                search_terms.append(line.replace('Title:', '').strip())
            elif 'Description:' in line:
                desc = line.replace('Description:', '').strip()
                # Extract first 50 characters for search
                search_terms.append(desc[:50])
        
        # If no specific terms found, extract key nouns from the prompt
        if not search_terms:
            # Simple extraction of potential key terms
            words = prompt.split()
            potential_terms = [word for word in words if len(word) > 4 and word.isalpha()]
            search_terms = potential_terms[:3]  # Take first 3 meaningful words
        
        return ' '.join(search_terms) if search_terms else "market analysis business trends"
    
    def _extract_search_query_from_idea(self, idea: str) -> str:
        """Extract search query from idea description for market research"""
        # Extract key terms and concepts from the idea
        words = idea.lower().split()
        
        # Filter out common words and focus on business-relevant terms
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'this', 'that', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'}
        
        meaningful_words = [word.strip('.,!?;:') for word in words if len(word) > 3 and word not in stop_words]
        
        # Take the first 4-5 meaningful terms for search
        search_terms = meaningful_words[:5]
        
        # Add market research context
        search_query = ' '.join(search_terms) + " market size trends competitors analysis"
        
        return search_query
    
    def _format_answers_for_prompt(self, answers: dict) -> str:
        """Format user answers into a readable prompt section"""
        if not answers:
            return "No additional context provided."
        
        formatted = []
        for key, value in answers.items():
            # Convert snake_case keys to readable format
            readable_key = key.replace('_', ' ').title()
            formatted.append(f"{readable_key}: {value}")
        
        return '\n'.join(formatted)
    
    async def generate_project_concept(self, title: str, description: str) -> Dict[str, Any]:
        """Generate detailed project concept from idea"""
        prompt = f"""
        Based on the following idea, create a comprehensive project concept. Return the response as valid JSON with the exact structure below:

        Title: {title}
        Description: {description}
        
        Generate a detailed analysis and return it in this exact JSON format:
        {{
            "executive_summary": "2-3 sentence overview of the project",
            "value_proposition": "Clear statement of unique value offered",
            "differentiators": ["key differentiator 1", "key differentiator 2", "key differentiator 3"],
            "technical_approach": "High-level technical strategy and architecture approach",
            "market_opportunity": "Market size, growth potential, and opportunity description"
        }}
        
        Ensure the response is valid JSON only, no additional text or markdown formatting.
        """
        
        try:
            response = await self._call_ai_provider(prompt)
            # Parse JSON response
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return {
                "executive_summary": f"AI-powered solution for {title} that addresses key market needs",
                "value_proposition": "Innovative approach solving critical problems with modern technology",
                "differentiators": ["Advanced AI integration", "User-centric design", "Scalable architecture"],
                "technical_approach": "Modern, cloud-native architecture with microservices",
                "market_opportunity": "Growing market with significant potential for disruption"
            }
    
    async def generate_user_roles(self, project_concept: Dict[str, Any]) -> Dict[str, Any]:
        """Generate 4 user roles with responsibilities"""
        prompt = f"""
        Based on this project concept, generate 4 distinct user roles. Return the response as valid JSON with the exact structure below:

        Project: {json.dumps(project_concept, indent=2)}
        
        Generate exactly 4 user roles and return in this exact JSON format:
        {{
            "roles": [
                {{
                    "name": "Role Name",
                    "description": "Brief role description",
                    "responsibilities": ["responsibility 1", "responsibility 2", "responsibility 3"],
                    "pain_points": ["pain point 1", "pain point 2"]
                }}
            ]
        }}
        
        Make sure to include exactly 4 roles that make sense for this project. Ensure the response is valid JSON only.
        """
        
        try:
            response = await self._call_ai_provider(prompt)
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return {
                "roles": [
                    {
                        "name": "End User",
                        "description": "Primary user of the platform",
                        "responsibilities": ["Use core features", "Provide feedback", "Complete workflows"],
                        "pain_points": ["Current process is manual", "Lacks insights"]
                    },
                    {
                        "name": "Administrator",
                        "description": "Manages platform configuration",
                        "responsibilities": ["User management", "System configuration", "Monitor usage"],
                        "pain_points": ["Complex admin tasks", "No visibility into system health"]
                    },
                    {
                        "name": "Analyst",
                        "description": "Analyzes data and generates reports",
                        "responsibilities": ["Data analysis", "Report generation", "Identify trends"],
                        "pain_points": ["Manual data processing", "Limited analytical tools"]
                    },
                    {
                        "name": "Decision Maker",
                        "description": "Makes strategic decisions based on insights",
                        "responsibilities": ["Strategic planning", "Resource allocation", "Set priorities"],
                        "pain_points": ["Lack of real-time data", "Poor data visualization"]
                    }
                ]
            }
    
    async def generate_features(self, project_concept: Dict[str, Any], user_roles: Dict[str, Any]) -> Dict[str, Any]:
        """Generate features categorized by priority"""
        prompt = f"""
        Based on the project concept and user roles, generate a comprehensive feature list categorized by priority.
        
        Project: {json.dumps(project_concept, indent=2)}
        User Roles: {json.dumps(user_roles, indent=2)}
        
        Return the response in this exact JSON format:
        {{
            "core": ["essential feature 1", "essential feature 2", "essential feature 3"],
            "must_have": ["important feature 1", "important feature 2", "important feature 3"],
            "should_have": ["valuable feature 1", "valuable feature 2", "valuable feature 3"],
            "nice_to_have": ["enhancement 1", "enhancement 2", "enhancement 3"],
            "future": ["future feature 1", "future feature 2", "future feature 3"]
        }}
        
        Ensure features are specific to the project and user needs. Return valid JSON only.
        """
        
        try:
            response = await self._call_ai_provider(prompt)
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return {
                "core": [
                    "User authentication and authorization",
                    "Dashboard with key metrics", 
                    "Data input and validation"
                ],
                "must_have": [
                    "Real-time notifications",
                    "Export functionality",
                    "User management system"
                ],
                "should_have": [
                    "Advanced analytics",
                    "API integrations",
                    "Mobile responsiveness"
                ],
                "nice_to_have": [
                    "AI-powered insights",
                    "Custom branding",
                    "Advanced reporting"
                ],
                "future": [
                    "Machine learning models",
                    "Third-party integrations",
                    "Advanced automation"
                ]
            }
    
    async def generate_target_users(self, project_concept: Dict[str, Any]) -> Dict[str, Any]:
        """Generate target user analysis"""
        prompt = f"""
        Based on this project concept, generate a target user analysis with personas and market sizing.
        
        Project: {json.dumps(project_concept, indent=2)}
        
        Return the response in this exact JSON format:
        {{
            "primary_personas": [
                {{
                    "name": "Persona Name",
                    "demographics": "Age range, industry, role level",
                    "pain_points": ["pain 1", "pain 2"],
                    "goals": ["goal 1", "goal 2"]
                }}
            ],
            "market_size": {{
                "tam": "Total addressable market description with estimate",
                "sam": "Serviceable addressable market description with estimate",
                "som": "Serviceable obtainable market description with estimate"
            }}
        }}
        
        Generate 2-3 distinct personas and realistic market estimates. Return valid JSON only.
        """
        
        try:
            response = await self._call_ai_provider(prompt)
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return {
                "primary_personas": [
                    {
                        "name": "Tech-Savvy Professional",
                        "demographics": "25-40 years, technology industry, individual contributor to manager level",
                        "pain_points": ["Time constraints", "Need for efficiency", "Information overload"],
                        "goals": ["Streamline workflows", "Increase productivity", "Make data-driven decisions"]
                    },
                    {
                        "name": "Business Decision Maker", 
                        "demographics": "35-55 years, various industries, management to executive level",
                        "pain_points": ["Lack of insights", "Manual processes", "Slow decision-making"],
                        "goals": ["Data-driven decisions", "ROI optimization", "Team efficiency"]
                    }
                ],
                "market_size": {
                    "tam": "$50B - Total addressable market for business productivity solutions",
                    "sam": "$5B - Serviceable addressable market in target industries", 
                    "som": "$500M - Serviceable obtainable market with realistic penetration"
                }
            }
    
    async def generate_market_analysis(self, project_concept: Dict[str, Any]) -> Dict[str, Any]:
        """Generate market analysis and validation"""
        prompt = f"""
        Based on this project concept, generate a comprehensive market analysis.
        
        Project: {json.dumps(project_concept, indent=2)}
        
        Return the response in this exact JSON format:
        {{
            "market_trends": ["trend 1", "trend 2", "trend 3"],
            "competitive_landscape": {{
                "direct_competitors": ["competitor 1", "competitor 2"],
                "indirect_competitors": ["alternative 1", "alternative 2"],
                "competitive_advantage": "Key advantage over competitors"
            }},
            "market_validation": {{
                "problem_validation": "Assessment of problem validation with rationale",
                "solution_validation": "Assessment of solution validation with rationale",
                "market_demand": "Assessment of market demand with evidence"
            }}
        }}
        
        Provide realistic analysis based on current market conditions. Return valid JSON only.
        """
        
        try:
            response = await self._call_ai_provider(prompt)
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return {
                "market_trends": [
                    "Increasing demand for automation and AI integration",
                    "Growth in cloud-based SaaS solutions",
                    "Focus on data-driven decision making"
                ],
                "competitive_landscape": {
                    "direct_competitors": ["Established Solution A", "Emerging Solution B"],
                    "indirect_competitors": ["Manual process alternatives", "Legacy systems"],
                    "competitive_advantage": "AI-powered automation with user-friendly interface"
                },
                "market_validation": {
                    "problem_validation": "High - Problem is widely recognized and actively discussed in industry",
                    "solution_validation": "Medium - Solution approach needs user validation and testing",
                    "market_demand": "High - Growing market segment with strong demand indicators"
                }
            }
    
    async def generate_roadmap_phases(self, title: str, description: str, project_planning: Any = None) -> List[Dict[str, Any]]:
        """Generate development roadmap phases"""
        prompt = f"""
        Based on this project information, generate a realistic development roadmap with 3-4 phases.
        
        Title: {title}
        Description: {description}
        Project Planning: {json.dumps(project_planning.__dict__ if project_planning else {}, default=str, indent=2)}
        
        Return the response in this exact JSON format:
        {{
            "phases": [
                {{
                    "name": "Phase Name",
                    "duration": "X-Y months",
                    "description": "What will be accomplished in this phase",
                    "deliverables": ["deliverable 1", "deliverable 2", "deliverable 3"],
                    "team_size": 3,
                    "estimated_hours": 480
                }}
            ]
        }}
        
        Generate 3-4 realistic phases with proper progression. Return valid JSON only.
        """
        
        try:
            response = await self._call_ai_provider(prompt)
            result = json.loads(response.strip())
            return result.get("phases", [])
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback")
            return [
                {
                    "name": "Phase 1: Foundation & MVP",
                    "duration": "2-3 months",
                    "description": "Core infrastructure, basic features, and minimal viable product",
                    "deliverables": ["User authentication", "Basic dashboard", "Core API", "Database setup"],
                    "team_size": 3,
                    "estimated_hours": 480
                },
                {
                    "name": "Phase 2: Core Features",
                    "duration": "3-4 months", 
                    "description": "Main functionality implementation and user interface development",
                    "deliverables": ["Feature implementation", "UI/UX completion", "Admin panel", "Testing"],
                    "team_size": 4,
                    "estimated_hours": 640
                },
                {
                    "name": "Phase 3: Enhancement & Scale",
                    "duration": "2-3 months",
                    "description": "Advanced features, performance optimization, and scalability improvements",
                    "deliverables": ["Advanced analytics", "Integrations", "Performance optimization", "Security hardening"],
                    "team_size": 3,
                    "estimated_hours": 400
                }
            ]
    
    def generate_team_composition(self, phases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate team composition and roles based on phases"""
        return {
            "roles": {
                "frontend_developer": {"count": 1, "hourly_rate": 75},
                "backend_developer": {"count": 1, "hourly_rate": 80},
                "full_stack_developer": {"count": 1, "hourly_rate": 85},
                "ui_ux_designer": {"count": 1, "hourly_rate": 70},
                "project_manager": {"count": 1, "hourly_rate": 90},
                "devops_engineer": {"count": 0.5, "hourly_rate": 95}
            },
            "total_team_size": 4.5
        }
    
    def generate_cost_breakdown(self, phases: List[Dict[str, Any]], team_composition: Dict[str, Any]) -> Dict[str, Any]:
        """Generate cost breakdown by phase"""
        cost_per_phase = []
        total_cost = 0
        
        for phase in phases:
            estimated_hours = phase.get("estimated_hours", 400)
            team_size = phase.get("team_size", 3)
            
            # Calculate cost based on team composition and hours
            avg_hourly_rate = 80  # Average from team composition
            phase_cost = estimated_hours * avg_hourly_rate
            
            cost_breakdown = {
                "development": int(phase_cost * 0.75),
                "design": int(phase_cost * 0.15),
                "management": int(phase_cost * 0.10)
            }
            
            cost_per_phase.append({
                "phase": phase["name"],
                "estimated_cost": phase_cost,
                "breakdown": cost_breakdown
            })
            
            total_cost += phase_cost
        
        return {
            "phases": cost_per_phase,
            "total_estimated_cost": int(total_cost)
        }
    
    def generate_timeline_estimates(self, phases: List[Dict[str, Any]], team_composition: Dict[str, Any]) -> Dict[str, Any]:
        """Generate timeline estimates"""
        total_months = sum([int(phase["duration"].split("-")[1].split(" ")[0]) for phase in phases])
        
        timeline_phases = []
        current_month = 1
        
        for phase in phases:
            duration = int(phase["duration"].split("-")[1].split(" ")[0])
            timeline_phases.append({
                "name": phase["name"],
                "start": f"Month {current_month}",
                "end": f"Month {current_month + duration - 1}"
            })
            current_month += duration
        
        return {
            "total_duration": f"{total_months//2}-{total_months} months",
            "phases": timeline_phases,
            "milestones": [
                {"name": "MVP Release", "target": f"Month {timeline_phases[0]['end'].split()[-1]}"},
                {"name": "Beta Release", "target": f"Month {timeline_phases[-2]['end'].split()[-1]}" if len(timeline_phases) > 1 else "Month 4"},
                {"name": "Production Release", "target": f"Month {timeline_phases[-1]['end'].split()[-1]}"}
            ]
        }
    
    def generate_risk_assessments(self, phases: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate risk assessments and mitigation strategies"""
        return {
            "risks": [
                {
                    "category": "Technical",
                    "risk": "Integration complexity with third-party services",
                    "probability": "Medium",
                    "impact": "High",
                    "mitigation": "Proof of concept development, technical spikes, expert consultation"
                },
                {
                    "category": "Resource",
                    "risk": "Key team member availability",
                    "probability": "Low",
                    "impact": "Medium", 
                    "mitigation": "Cross-training, documentation, backup resource identification"
                },
                {
                    "category": "Market",
                    "risk": "Changing market requirements",
                    "probability": "Medium",
                    "impact": "Medium",
                    "mitigation": "Agile development, regular user feedback, iterative validation"
                },
                {
                    "category": "Technical",
                    "risk": "Performance and scalability challenges",
                    "probability": "Medium",
                    "impact": "High",
                    "mitigation": "Performance testing, architecture review, cloud infrastructure"
                }
            ]
        }
    
    def generate_milestones(self, phases: List[Dict[str, Any]], timeline_estimates: Dict[str, Any]) -> Dict[str, Any]:
        """Generate project milestones"""
        milestones = []
        
        # Add kickoff milestone
        milestones.append({
            "name": "Project Kickoff",
            "date": "Month 1",
            "deliverables": ["Requirements finalized", "Team assembled", "Development environment setup"],
            "success_criteria": ["All stakeholders aligned", "Development environment ready", "Project plan approved"]
        })
        
        # Add phase completion milestones
        for i, phase in enumerate(phases):
            milestone_name = f"{phase['name']} Completion"
            if i == 0:
                milestone_name = "MVP Completion"
            elif i == len(phases) - 2:
                milestone_name = "Beta Release"
            elif i == len(phases) - 1:
                milestone_name = "Production Launch"
            
            timeline_phase = timeline_estimates["phases"][i] if i < len(timeline_estimates["phases"]) else {"end": f"Month {i+3}"}
            
            milestones.append({
                "name": milestone_name,
                "date": timeline_phase["end"],
                "deliverables": phase["deliverables"],
                "success_criteria": ["All deliverables completed", "Quality gates passed", "Stakeholder approval received"]
            })
        
        return {
            "milestones": milestones
        }
    
    def recalculate_costs(self, phases: List[Dict[str, Any]], team_composition: Dict[str, Any], hourly_rates: Dict[str, float]) -> Dict[str, Any]:
        """Recalculate costs with updated hourly rates"""
        updated_phases = []
        total_cost = 0
        
        avg_hourly_rate = sum(hourly_rates.values()) / len(hourly_rates)
        
        for phase in phases:
            estimated_hours = phase.get("estimated_hours", 400)
            phase_cost = int(estimated_hours * avg_hourly_rate)
            
            updated_phases.append({
                **phase,
                "estimated_cost": phase_cost
            })
            
            total_cost += phase_cost
        
        return {
            "phases": updated_phases,
            "updated_total_cost": total_cost,
            "hourly_rates_used": hourly_rates
        }

    async def stream_validation_response(self, title: str, description: str) -> AsyncGenerator[str, None]:
        """Stream validation response for real-time UI updates"""
        steps = [
            "🔍 Analyzing your idea...",
            "💡 Generating project concept...", 
            "👥 Creating user roles...",
            "🎯 Defining features...",
            "🎯 Analyzing target users...",
            "📊 Conducting market analysis...",
            "✅ Validation complete!"
        ]
        
        for step in steps:
            yield f"data: {json.dumps({'step': step})}\n\n"
            await asyncio.sleep(1)  # Simulate processing time
        
        # Generate full validation result
        try:
            project_concept = await self.generate_project_concept(title, description)
            user_roles = await self.generate_user_roles(project_concept)
            features = await self.generate_features(project_concept, user_roles)
            target_users = await self.generate_target_users(project_concept)
            market_analysis = await self.generate_market_analysis(project_concept)
            
            result = {
                "project_concept": project_concept,
                "user_roles": user_roles,
                "features": features,
                "target_users": target_users,
                "market_analysis": market_analysis
            }
            
            yield f"data: {json.dumps({'result': result})}\n\n"
            
        except Exception as e:
            logger.error(f"Error in streaming validation: {str(e)}")
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    async def generate_idea_questions(self, idea_description: str) -> Dict[str, Any]:
        """Generate short, dynamic questions with mandatory geo/tech questions"""
        prompt = f"""
        Based on the following idea description, generate 3 short, specific questions to better understand this idea for validation.
        
        REQUIREMENTS:
        - Questions must be SHORT (max 8-10 words)
        - Examples go in placeholder, NOT in the question text
        - Focus on market, business model, and specific challenges
        - Use only text inputs (textarea or text)
        
        Idea Description: {idea_description}
        
        Return this exact JSON format with the first 2 questions being MANDATORY geography and tech questions:
        {{
            "questions": [
                {{
                    "id": "geographic_scope",
                    "question": "What's your target geographic market?",
                    "type": "text",
                    "placeholder": "e.g., Global, USA, Europe, India, California, specific cities...",
                    "required": true
                }},
                {{
                    "id": "tech_involvement",
                    "question": "What technology does this idea involve?",
                    "type": "text", 
                    "placeholder": "e.g., No tech, Mobile app, AI/ML, IoT devices, Web platform, Blockchain...",
                    "required": true
                }},
                {{
                    "id": "dynamic_question_1",
                    "question": "[SHORT specific question for this idea]",
                    "type": "textarea",
                    "placeholder": "[Examples relevant to the idea go here...]",
                    "required": false
                }},
                {{
                    "id": "dynamic_question_2", 
                    "question": "[SHORT specific question for this idea]",
                    "type": "textarea",
                    "placeholder": "[Examples relevant to the idea go here...]",
                    "required": false
                }},
                {{
                    "id": "dynamic_question_3",
                    "question": "[SHORT specific question for this idea]",
                    "type": "textarea",
                    "placeholder": "[Examples relevant to the idea go here...]",
                    "required": false
                }}
            ]
        }}
        
        For the 3 dynamic questions, make them SHORT and specific to "{idea_description}".
        Put examples and hints in placeholder, NOT in question text.
        Ensure valid JSON only - no additional text.
        """
        
        try:
            # Use OpenAI for faster question generation
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 1500, use_external_tools=False)
            return json.loads(response.strip())
        except json.JSONDecodeError:
            logger.warning("Failed to parse AI response as JSON, using fallback questions")
            return {
                "questions": [
                    {
                        "id": "geographic_scope",
                        "question": "What's your target geographic market?",
                        "type": "text",
                        "placeholder": "e.g., Global, USA, Europe, India, California, specific cities...",
                        "required": True
                    },
                    {
                        "id": "tech_involvement",
                        "question": "What technology does this idea involve?",
                        "type": "text",
                        "placeholder": "e.g., No tech, Mobile app, AI/ML, IoT devices, Web platform, Blockchain...",
                        "required": True
                    },
                    {
                        "id": "target_audience",
                        "question": "Who is your target audience?",
                        "type": "textarea",
                        "placeholder": "e.g., Small business owners, students, healthcare professionals, busy parents...",
                        "required": False
                    },
                    {
                        "id": "business_model",
                        "question": "How will you make money?",
                        "type": "textarea",
                        "placeholder": "e.g., Subscription fees, one-time purchase, advertising, commission-based...",
                        "required": False
                    },
                    {
                        "id": "key_challenge",
                        "question": "What's your biggest challenge?",
                        "type": "textarea",
                        "placeholder": "e.g., Finding customers, technical complexity, funding, competition...",
                        "required": False
                    }
                ]
            }
        except Exception as e:
            logger.error(f"Error generating questions: {str(e)}")
            raise Exception("Failed to generate questions for idea validation")
    
    async def comprehensive_idea_validation(self, idea: str, answers: dict, streaming_callback=None) -> str:
        """Run comprehensive idea validation using multi-analyst approach like incepta-proto"""
        try:
            logger.info("Starting multi-analyst validation process...")
            
            # Extract key answers
            geographic_scope = answers.get('geographic_scope', 'Global')
            tech_involvement = answers.get('tech_involvement', 'Technology platform')
            
            # Step 1: Agent Ross - Market Research
            if streaming_callback:
                await streaming_callback({"type": "analyst_start", "analyst": "Agent Ross - Sr. Market Research Analyst", "task": "Market Research & Competitive Analysis"})
            market_analysis = await self._agent_ross_market_analysis(idea, geographic_scope, answers)
            if streaming_callback:
                await streaming_callback({"type": "analyst_complete", "analyst": "Agent Ross", "status": "Market research completed"})
            
            # Step 2: Agent Mohammed - Financial Analysis 
            if streaming_callback:
                await streaming_callback({"type": "analyst_start", "analyst": "Agent Mohammed - Sr. Financial Analyst", "task": "Financial Feasibility & Business Model Analysis"})
            financial_analysis = await self._agent_mohammed_financial_analysis(idea, geographic_scope, answers)
            if streaming_callback:
                await streaming_callback({"type": "analyst_complete", "analyst": "Agent Mohammed", "status": "Financial analysis completed"})
            
            # Step 3: Agent Sarah - Technical Analysis
            if streaming_callback:
                await streaming_callback({"type": "analyst_start", "analyst": "Agent Sarah - Sr. Technical Architect", "task": "Technical Feasibility & Architecture Assessment"})
            technical_analysis = await self._agent_sarah_technical_analysis(idea, tech_involvement, answers)
            if streaming_callback:
                await streaming_callback({"type": "analyst_complete", "analyst": "Agent Sarah", "status": "Technical analysis completed"})
            
            # Step 4: Agent David - Risk Assessment
            if streaming_callback:
                await streaming_callback({"type": "analyst_start", "analyst": "Agent David - Sr. Risk Assessment Specialist", "task": "Comprehensive Risk Analysis"})
            risk_analysis = await self._agent_david_risk_analysis(idea, geographic_scope, answers)
            if streaming_callback:
                await streaming_callback({"type": "analyst_complete", "analyst": "Agent David", "status": "Risk assessment completed"})
            
            # Step 5: Final Consolidation
            if streaming_callback:
                await streaming_callback({"type": "consolidation_start", "message": "Consolidating all analyst reports into final validation..."})
            final_report = await self._consolidate_analyst_reports(
                idea, answers, market_analysis, financial_analysis, technical_analysis, risk_analysis
            )
            if streaming_callback:
                await streaming_callback({"type": "consolidation_complete", "message": "Final validation report ready"}) 
            
            return final_report
            
        except Exception as e:
            logger.error(f"Multi-analyst validation failed: {str(e)}")
            # Fallback to simple validation
            return await self._simple_validation_fallback(idea, answers)
    
    async def comprehensive_idea_validation_stream(self, idea: str, answers: dict, use_celery: bool = False):
        """Stream comprehensive idea validation with real-time analyst updates"""
        try:
            logger.info("🚀 AI Service: Starting streaming multi-analyst validation process...")
            logger.info(f"🚀 AI Service: Parameters - idea: {idea[:50]}..., use_celery: {use_celery}")
            
            # Full multi-agent implementation with external tools
            logger.info("🚀 AI Service: About to yield first status message")
            yield {"type": "status", "message": "Starting comprehensive validation with full agent team..."}
            logger.info("🚀 AI Service: First status message yielded")
            
            # Define comprehensive 11-agent team (All using Anthropic)
            agents = [
                {
                    "name": "Project Concept Analyst",
                    "agent_name": "ProjectConcept",
                    "title": "Project Concept Specialist",
                    "task": "Analyzing core project concept and problem definition",
                    "provider": "Anthropic",
                    "icon": "🎯",
                    "background": "Project concept specialist with problem-solution fit expertise"
                },
                {
                    "name": "Target Users Analyst",
                    "agent_name": "TargetUsers",
                    "title": "Target User Research Specialist",
                    "task": "Analyzing target user segments and user personas",
                    "provider": "Anthropic",
                    "icon": "👥",
                    "background": "User research specialist with persona development expertise"
                },
                {
                    "name": "Features Analyst",
                    "agent_name": "Features",
                    "title": "Feature Prioritization Specialist",
                    "task": "Analyzing and categorizing features by priority",
                    "provider": "Anthropic",
                    "icon": "🛠️",
                    "background": "Feature analyst with product prioritization expertise"
                },
                {
                    "name": "Market Size Analyst",
                    "agent_name": "MarketSize",
                    "title": "Market Sizing Specialist",
                    "task": "Analyzing TAM, SAM, SOM and market opportunity",
                    "provider": "Anthropic",
                    "icon": "📈",
                    "background": "Market sizing specialist with TAM/SAM/SOM analysis expertise"
                },
                {
                    "name": "Target Market Analyst",
                    "agent_name": "TargetMarket",
                    "title": "Market Positioning Specialist",
                    "task": "Analyzing target market positioning and segmentation",
                    "provider": "Anthropic",
                    "icon": "🎯",
                    "background": "Market positioning specialist with segmentation expertise"
                },
                {
                    "name": "Value Proposition Analyst",
                    "agent_name": "ValueProposition",
                    "title": "Value Proposition Specialist",
                    "task": "Analyzing unique value proposition and differentiation",
                    "provider": "Anthropic",
                    "icon": "💎",
                    "background": "Value proposition specialist with differentiation strategy expertise"
                },
                {
                    "name": "Monetization Analyst",
                    "agent_name": "Monetization",
                    "title": "Monetization Strategy Specialist",
                    "task": "Analyzing revenue models and monetization strategies",
                    "provider": "Anthropic",
                    "icon": "💰",
                    "background": "Monetization specialist with revenue model expertise"
                },
                {
                    "name": "Market Challenges Analyst",
                    "agent_name": "MarketChallenges",
                    "title": "Market Risk Assessment Specialist",
                    "task": "Analyzing market challenges and risk factors",
                    "provider": "Anthropic",
                    "icon": "⚠️",
                    "background": "Market risk specialist with challenge identification expertise"
                },
                {
                    "name": "Market Opportunities Analyst",
                    "agent_name": "MarketOpportunities",
                    "title": "Market Opportunity Specialist",
                    "task": "Analyzing market opportunities and growth potential",
                    "provider": "Anthropic",
                    "icon": "🌟",
                    "background": "Market opportunity specialist with growth analysis expertise"
                },
                {
                    "name": "Competitors Analyst",
                    "agent_name": "Competitors",
                    "title": "Competitive Analysis Specialist",
                    "task": "Analyzing competitive landscape and positioning",
                    "provider": "Anthropic",
                    "icon": "🏆",
                    "background": "Competitive analysis specialist with market intelligence expertise"
                },
                {
                    "name": "Citations Analyst",
                    "agent_name": "Citations",
                    "title": "Research Citations Specialist",
                    "task": "Gathering and validating research citations and sources",
                    "provider": "Anthropic",
                    "icon": "📚",
                    "background": "Research specialist with citation validation and source verification expertise"
                }
            ]
            
            agent_results = {}
            
            # Execute all agents with external tools and streaming updates
            for i, agent in enumerate(agents):
                logger.info(f"🚀 AI Service: Starting {agent['name']} ({i+1}/{len(agents)})")
                
                # Announce agent start
                yield {
                    "type": "analyst_start",
                    "analyst": agent["name"],
                    "agent_name": agent["agent_name"],
                    "title": agent["title"],
                    "task": agent["task"],
                    "provider": agent["provider"],
                    "icon": agent["icon"],
                    "background": agent["background"]
                }
                
                try:
                    # Execute specialized analysis for each agent (All using Anthropic)
                    if agent["agent_name"] == "ProjectConcept":
                        result = await self._agent_project_concept_analysis(idea, answers)
                    elif agent["agent_name"] == "TargetUsers":
                        result = await self._agent_target_users_analysis(idea, answers)
                    elif agent["agent_name"] == "Features":
                        result = await self._agent_features_analysis(idea, answers)
                    elif agent["agent_name"] == "MarketSize":
                        result = await self._agent_market_size_analysis(idea, answers)
                    elif agent["agent_name"] == "TargetMarket":
                        result = await self._agent_target_market_analysis(idea, answers)
                    elif agent["agent_name"] == "ValueProposition":
                        result = await self._agent_value_proposition_analysis(idea, answers)
                    elif agent["agent_name"] == "Monetization":
                        result = await self._agent_monetization_analysis(idea, answers)
                    elif agent["agent_name"] == "MarketChallenges":
                        result = await self._agent_market_challenges_analysis(idea, answers)
                    elif agent["agent_name"] == "MarketOpportunities":
                        result = await self._agent_market_opportunities_analysis(idea, answers)
                    elif agent["agent_name"] == "Competitors":
                        result = await self._agent_competitors_analysis(idea, answers)
                    elif agent["agent_name"] == "Citations":
                        result = await self._agent_citations_analysis(idea, answers)
                    
                    agent_results[agent["agent_name"]] = result
                    
                    # Announce completion
                    yield {
                        "type": "analyst_complete",
                        "analyst": agent["name"],
                        "title": agent["title"],
                        "status": f"{agent['name']} analysis completed",
                        "provider": agent["provider"],
                        "icon": agent["icon"],
                        "section": agent["agent_name"].lower()
                    }
                    
                    # Brief delay for UI experience
                    await asyncio.sleep(1.0)
                    
                except Exception as e:
                    logger.error(f"🚀 AI Service: {agent['name']} failed: {str(e)}")
                    agent_results[agent["agent_name"]] = {"error": str(e)}
                    
                    yield {
                        "type": "analyst_error",
                        "analyst": agent["name"],
                        "error": str(e),
                        "icon": "❌"
                    }
            
            # Final consolidation with all agent results
            yield {"type": "consolidation_start", "message": "Consolidating comprehensive analysis from all agents..."}
            
            try:
                final_report = await self._create_comprehensive_report(idea, answers, agent_results)
                yield {"type": "consolidation_complete", "message": "Comprehensive validation report ready"}
                yield {"type": "final_report", "report": final_report}
                return
            except Exception as e:
                logger.error(f"Comprehensive consolidation failed: {str(e)}")
                yield {"type": "error", "message": f"Consolidation failed: {str(e)}"}
                return
            
            # OLD COMPLEX IMPLEMENTATION BELOW - REMOVED FOR RELIABILITY
            # Extract key answers
            geographic_scope = answers.get('geographic_scope', 'Global')
            tech_involvement = answers.get('tech_involvement', 'Technology platform')
            
            # Use Celery for parallel processing if enabled  
            if use_celery:  # Celery enabled for distributed parallel processing
                logger.info("Using Celery for distributed parallel processing...")
                yield {"type": "status", "message": "Celery enabled but falling back to direct execution for reliability..."}
                
                # TEMPORARILY DISABLE COMPLEX CELERY UNTIL FULLY DEBUGGED
                # The complex Celery implementation is causing stream breaks
                # Fall back to direct execution which we know works perfectly
                logger.warning("Complex Celery implementation disabled - using reliable direct execution")
                use_celery = False
            
            # Only run direct execution if Celery wasn't used or failed
            if not use_celery:
                # Use phase-sequential execution for proper dependency management
                logger.info("Using phase-sequential execution for proper Citations dependency...")
                yield {"type": "status", "message": "Using phase-sequential execution with proper dependencies..."}
                
                try:
                    # Dynamic agent configuration with randomized names
                    logger.info("Generating dynamic agent team...")
                    agent_configs = self._generate_dynamic_agent_team(idea, geographic_scope, tech_involvement, answers)
                    logger.info(f"Generated {len(agent_configs)} agent configurations")
                    
                    # TEMPORARY: Limit to just 2 agents to test streaming
                    agent_configs = agent_configs[:2]
                    logger.info(f"TESTING: Limited to {len(agent_configs)} agents")
                    
                except Exception as e:
                    logger.error(f"Failed to generate agent team: {str(e)}")
                    yield {"type": "error", "message": f"Failed to generate agent team: {str(e)}"}
                    return
                
                analyses = []
                
                # Phase-sequential execution will send agent start notifications per phase
                
                # Execute agents in sequential phases for dependencies
                async def run_agent_analysis(config):
                    try:
                        logger.info(f"Starting analysis for agent: {config['agent_name']}")
                        analysis = await config['method'](*config['args'])
                        logger.info(f"Completed analysis for agent: {config['agent_name']}")
                        return {
                            "success": True,
                            "agent": config['agent_name'],
                            "title": config['title'],
                            "analysis": analysis,
                            "provider": config['provider'],
                            "icon": config['icon'],
                            "section": config['section']
                        }
                    except Exception as agent_error:
                        logger.error(f"Agent {config['agent_name']} failed: {str(agent_error)}")
                        return {
                            "success": False,
                            "agent": config['agent_name'],
                            "title": config['title'],
                            "error": str(agent_error),
                            "icon": "❌",
                            "section": config['section']
                        }
                
                # Group agents by phase for sequential/parallel processing
                agents_by_phase = {}
                for config in agent_configs:
                    phase = config['phase']
                    if phase not in agents_by_phase:
                        agents_by_phase[phase] = []
                    agents_by_phase[phase].append(config)
                
                # Execute phases sequentially, agents within each phase in parallel
                for phase in sorted(agents_by_phase.keys()):
                    phase_agents = agents_by_phase[phase]
                    yield {"type": "phase_start", "phase": phase, "message": f"Starting Phase {phase} with {len(phase_agents)} agents..."}
                    
                    # Send analyst_start notifications for agents in this phase
                    for config in phase_agents:
                        full_name = f"{config['agent_name']} - {config['title']}"
                        yield {
                            "type": "analyst_start", 
                            "analyst": full_name,
                            "agent_name": config['agent_name'],
                            "title": config['title'], 
                            "task": config['task'],
                            "provider": config['provider'],
                            "icon": config['icon'],
                            "background": config.get('background', ''),
                            "phase": phase
                        }
                    
                    # Run all agents in this phase in parallel
                    logger.info(f"Creating {len(phase_agents)} tasks for phase {phase}")
                    phase_tasks = [run_agent_analysis(config) for config in phase_agents]
                    logger.info(f"Starting execution of {len(phase_tasks)} tasks")
                    
                    for i, task in enumerate(asyncio.as_completed(phase_tasks)):
                        logger.info(f"Waiting for task {i+1}/{len(phase_tasks)} to complete")
                        try:
                            result = await task
                            logger.info(f"Task {i+1} completed successfully: {result['agent']}")
                        except Exception as task_error:
                            logger.error(f"Task {i+1} failed with error: {str(task_error)}")
                            result = {
                                "success": False,
                                "agent": f"Unknown Agent {i+1}",
                                "title": "Failed Task",
                                "error": str(task_error),
                                "icon": "❌",
                                "section": "error"
                            }
                        
                        if result["success"]:
                            analyses.append({
                                "agent": result["agent"],
                                "title": result["title"], 
                                "analysis": result["analysis"],
                                "provider": result["provider"],
                                "section": result["section"]
                            })
                            
                            yield {
                                "type": "analyst_complete",
                                "analyst": result["agent"], 
                                "title": result["title"],
                                "status": f"Analysis completed successfully",
                                "provider": result["provider"],
                                "icon": result["icon"],
                                "section": result["section"]
                            }
                        else:
                            yield {
                                "type": "analyst_error",
                                "analyst": result["agent"],
                                "title": result["title"], 
                                "error": result["error"],
                                "icon": result["icon"],
                                "section": result["section"]
                            }
                    
                    yield {"type": "phase_complete", "phase": phase, "message": f"Phase {phase} completed"}
                
                # Final Consolidation
                yield {"type": "consolidation_start", "message": "Consolidating all analyst reports into final validation..."}
                try:
                    logger.info(f"Starting consolidation with {len(analyses)} analyses")
                    final_report = await self._consolidate_multi_analyst_reports(idea, answers, analyses)
                    logger.info("Consolidation completed successfully")
                    yield {"type": "consolidation_complete", "message": "Final validation report ready"}
                    yield {"type": "final_report", "report": final_report}
                except Exception as consolidation_error:
                    logger.error(f"Consolidation failed: {str(consolidation_error)}")
                    yield {"type": "error", "message": f"Failed to consolidate reports: {str(consolidation_error)}"}
        
        except Exception as e:
            logger.error(f"Streaming validation failed: {str(e)}")
            yield {"type": "error", "message": f"Validation failed: {str(e)}"}

    async def _basic_market_analysis(self, idea: str, geographic_scope: str = "Global") -> dict:
        """Simple market analysis using OpenAI"""
        prompt = f"""
        Analyze the market opportunity for this idea: {idea}
        Geographic scope: {geographic_scope}
        
        Provide a concise market analysis including:
        1. Market size and potential
        2. Target audience
        3. Key competitors
        4. Market trends
        
        Keep response under 300 words.
        """
        
        try:
            self.current_provider = AIProvider.OPENAI
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "OpenAI"}
        except Exception as e:
            logger.error(f"Basic market analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Market analysis could not be completed"}

    async def _basic_technical_analysis(self, idea: str, tech_involvement: str = "Web App") -> dict:
        """Simple technical analysis using Anthropic"""
        prompt = f"""
        Evaluate the technical feasibility for this idea: {idea}
        Technology type: {tech_involvement}
        
        Provide a concise technical analysis including:
        1. Technical complexity (1-10 scale)
        2. Key technical challenges
        3. Recommended technology stack
        4. Development timeline estimate
        
        Keep response under 300 words.
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic"}
        except Exception as e:
            logger.error(f"Basic technical analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Technical analysis could not be completed"}

    async def _create_simplified_report(self, idea: str, answers: dict, market_analysis: dict, tech_analysis: dict) -> dict:
        """Create a simplified validation report"""
        
        # Calculate overall score based on available analyses
        score = 7.5  # Default score
        
        if "error" in market_analysis:
            score -= 1.0
        if "error" in tech_analysis:
            score -= 1.0
            
        return {
            "validation_summary": {
                "overall_score": score,
                "recommendation": "Proceed with caution" if score < 7 else "Promising opportunity",
                "confidence_level": "Medium",
                "key_strengths": ["AI-powered solution", "Market opportunity identified"],
                "key_concerns": ["Technical complexity", "Market competition"]
            },
            "executive_summary": f"Comprehensive analysis of '{idea}' shows moderate to high potential with manageable risks.",
            "project_concept": {
                "problem_statement": f"The idea '{idea}' addresses market needs in the {answers.get('geographic_scope', 'global')} market.",
                "solution_overview": "Technology-driven solution with AI capabilities",
                "value_proposition": "Innovative approach to solving user problems"
            },
            "market_analysis": market_analysis,
            "technical_analysis": tech_analysis,
            "full_report": f"This validation report for '{idea}' provides a simplified analysis focused on market opportunity and technical feasibility.",
            "providers_used": ["OpenAI", "Anthropic"],
            "external_tools_used": []
        }
    
    def _generate_dynamic_agent_team(self, idea: str, geographic_scope: str, tech_involvement: str, answers: dict) -> list:
        """Generate a dynamic team of agents with randomized names and backgrounds"""
        import random
        
        # Name pools for different roles and backgrounds
        market_analysts = [
            {"name": "Ross Chen", "background": "Market research specialist with 8 years experience"},
            {"name": "Maria Rodriguez", "background": "Consumer behavior analyst, startup advisor"},
            {"name": "James Wilson", "background": "Industry trends expert, product market fit consultant"},
            {"name": "Priya Sharma", "background": "Tech market analyst, growth strategy specialist"},
            {"name": "Ahmed Hassan", "background": "Global market researcher, emerging markets expert"},
            {"name": "Elena Petrov", "background": "Competitive intelligence specialist, business analyst"},
            {"name": "David Kim", "background": "Market sizing expert, TAM/SAM analysis specialist"},
            {"name": "Sarah Johnson", "background": "Customer research lead, validation methodology expert"}
        ]
        
        financial_analysts = [
            {"name": "Mohammed Al-Rashid", "background": "Startup financial modeling expert, fundraising advisor"},
            {"name": "Jennifer Park", "background": "Business model analyst, revenue strategy consultant"},
            {"name": "Carlos Mendoza", "background": "Financial planning specialist, seed funding expert"},
            {"name": "Lisa Wang", "background": "Unit economics analyst, growth finance expert"},
            {"name": "Michael Brown", "background": "Investment analyst, early-stage funding specialist"},
            {"name": "Fatima Al-Zahra", "background": "Venture analysis expert, pitch deck consultant"},
            {"name": "Robert Taylor", "background": "Financial feasibility analyst, cash flow expert"},
            {"name": "Nina Petersen", "background": "Startup valuation specialist, funding strategy advisor"}
        ]
        
        technical_architects = [
            {"name": "Sarah Kim", "background": "Full-stack developer, startup CTO advisor"},
            {"name": "Alex Chen", "background": "Product engineering lead, technical feasibility expert"},
            {"name": "Jordan Smith", "background": "Cloud architecture specialist, DevOps consultant"},
            {"name": "Maya Patel", "background": "AI/ML engineer, tech product strategist"},
            {"name": "Erik Johnson", "background": "Backend systems expert, API design specialist"},
            {"name": "Zara Ali", "background": "Frontend architect, user experience engineer"},
            {"name": "Chris Thompson", "background": "Platform engineer, scalability consultant"},
            {"name": "Rina Nakamura", "background": "Mobile development lead, cross-platform expert"}
        ]
        
        risk_specialists = [
            {"name": "David Rodriguez", "background": "Startup risk analyst, failure pattern expert"},
            {"name": "Amanda Foster", "background": "Business risk consultant, mitigation strategist"},
            {"name": "Hassan Malik", "background": "Market risk specialist, competitive threat analyst"},
            {"name": "Victoria Chen", "background": "Regulatory compliance expert, legal risk advisor"},
            {"name": "Marcus Williams", "background": "Operational risk consultant, process optimization expert"},
            {"name": "Layla Osman", "background": "Financial risk analyst, cash flow risk specialist"},
            {"name": "Thomas Anderson", "background": "Technology risk expert, security consultant"},
            {"name": "Sophia Lee", "background": "Product risk analyst, user adoption specialist"}
        ]
        
        market_intelligence = [
            {"name": "Lisa Zhang", "background": "Industry research specialist, trend analysis expert"},
            {"name": "Daniel Murphy", "background": "Market research consultant, survey methodology expert"},
            {"name": "Asha Gupta", "background": "Consumer insights analyst, user behavior specialist"},
            {"name": "Ryan O'Connor", "background": "Technology trends researcher, innovation analyst"},
            {"name": "Leila Khalil", "background": "Global market researcher, expansion strategy expert"},
            {"name": "Kevin Park", "background": "Emerging technology analyst, disruption specialist"},
            {"name": "Isabella Santos", "background": "Economic research analyst, market timing expert"},
            {"name": "Raj Mehta", "background": "Competitive intelligence specialist, market positioning expert"}
        ]
        
        legal_compliance = [
            {"name": "Marcus Thompson", "background": "Startup legal advisor, business formation expert"},
            {"name": "Rachel Green", "background": "Corporate compliance specialist, regulatory consultant"},
            {"name": "Omar Al-Mansouri", "background": "Business law expert, contract negotiation specialist"},
            {"name": "Julia Hoffman", "background": "Intellectual property consultant, trademark specialist"},
            {"name": "Samuel Chen", "background": "International business advisor, cross-border expert"},
            {"name": "Nadia Ivanova", "background": "Data privacy consultant, GDPR compliance expert"},
            {"name": "Roberto Silva", "background": "Employment law specialist, HR compliance advisor"},
            {"name": "Grace Liu", "background": "Investment legal expert, term sheet specialist"}
        ]
        
        # Select 11 unique agents from all pools to avoid duplicates
        all_agents = (market_analysts + financial_analysts + technical_architects + 
                     risk_specialists + market_intelligence + legal_compliance)
        
        # Randomly select 11 unique agents
        selected_agents = random.sample(all_agents, min(11, len(all_agents)))
        
        # Assign specific agents to each role
        project_agent = selected_agents[0]         # Project Concept
        target_users_agent = selected_agents[1]    # Target Users  
        features_agent = selected_agents[2]        # Features
        market_size_agent = selected_agents[3]     # Market Size
        target_market_agent = selected_agents[4]   # Target Market
        value_prop_agent = selected_agents[5]      # Value Proposition
        monetization_agent = selected_agents[6]    # Monetization
        competitors_agent = selected_agents[7]     # Competitors
        challenges_agent = selected_agents[8]      # Challenges
        opportunities_agent = selected_agents[9]   # Opportunities
        citations_agent = selected_agents[10]      # Citations
        
        # Incepta-proto style specialized agents for each validation section
        incepta_team = [
            # Phase 1: Foundation Data (Sequential)
            {
                "agent_name": f"Agent {project_agent['name']}",
                "agent_key": "project_concept",
                "title": "Project Concept Specialist",
                "background": project_agent['background'],
                "provider": "anthropic",
                "task": "Project Overview & User Roles Analysis",
                "icon": "🎯",
                "section": "project_concept",
                "method": self._agent_project_concept_analysis,
                "args": [idea, answers],
                "phase": 1
            },
            {
                "agent_name": f"Agent {target_users_agent['name']}", 
                "agent_key": "target_users",
                "title": "Target Users Specialist",
                "background": target_users_agent['background'],
                "provider": "anthropic", 
                "task": "Target Users & Market Segmentation",
                "icon": "👥",
                "section": "target_users",
                "method": self._agent_target_users_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 1
            },
            
            # Phase 2: Features & Market Size (Parallel, uses Phase 1)
            {
                "agent_name": f"Agent {features_agent['name']}",
                "agent_key": "features",
                "title": "Features Categorization Specialist", 
                "background": features_agent['background'],
                "provider": "anthropic",
                "task": "Features by Categories (Core, Must-have, Should-have, Nice-to-have, Future)", 
                "icon": "🛠️",
                "section": "features",
                "method": self._agent_features_analysis,
                "args": [idea, tech_involvement, answers],
                "phase": 2
            },
            {
                "agent_name": f"Agent {market_size_agent['name']}",
                "agent_key": "market_size",
                "title": "Market Size Analyst",
                "background": market_size_agent['background'], 
                "provider": "anthropic", 
                "task": "Market Size Analysis (TAM/SAM/SOM)",
                "icon": "📈",
                "section": "market_size",
                "method": self._agent_market_size_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 2
            },
            {
                "agent_name": f"Agent {target_market_agent['name']}",
                "agent_key": "target_market",
                "title": "Target Market Specialist",
                "background": target_market_agent['background'], 
                "provider": "anthropic", 
                "task": "Target Market Analysis & Positioning",
                "icon": "🎯",
                "section": "target_market", 
                "method": self._agent_target_market_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 2
            },
            
            # Phase 3: Value Proposition & Strategy (Parallel, uses Phase 1-2)
            {
                "agent_name": f"Agent {value_prop_agent['name']}",
                "agent_key": "value_proposition",
                "title": "Value Proposition Specialist",
                "background": value_prop_agent['background'], 
                "provider": "anthropic", 
                "task": "Unique Value Proposition Analysis",
                "icon": "💎",
                "section": "value_proposition",
                "method": self._agent_value_proposition_analysis,
                "args": [idea, answers],
                "phase": 3
            },
            {
                "agent_name": f"Agent {monetization_agent['name']}",
                "agent_key": "monetization",
                "title": "Monetization Strategy Analyst",
                "background": monetization_agent['background'], 
                "provider": "anthropic", 
                "task": "Monetization Strategies & Revenue Models",
                "icon": "💰",
                "section": "monetization",
                "method": self._agent_monetization_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 3
            },
            {
                "agent_name": f"Agent {competitors_agent['name']}",
                "agent_key": "competitors",
                "title": "Competitive Intelligence Specialist",
                "background": competitors_agent['background'], 
                "provider": "anthropic", 
                "task": "Competitors Analysis & Landscape",
                "icon": "🥊",
                "section": "competitors",
                "method": self._agent_competitors_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 3
            },
            
            # Phase 4: Challenges & Opportunities (Parallel, uses all previous)
            {
                "agent_name": f"Agent {challenges_agent['name']}",
                "agent_key": "challenges",
                "title": "Market Challenges Analyst",
                "background": challenges_agent['background'], 
                "provider": "anthropic", 
                "task": "Market Challenges & Barriers",
                "icon": "⚠️",
                "section": "challenges",
                "method": self._agent_challenges_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 4
            },
            {
                "agent_name": f"Agent {opportunities_agent['name']}", 
                "agent_key": "opportunities",
                "title": "Market Opportunities Specialist",
                "background": opportunities_agent['background'], 
                "provider": "anthropic", 
                "task": "Market Opportunities & Growth Potential",
                "icon": "🚀",
                "section": "opportunities",
                "method": self._agent_opportunities_analysis,
                "args": [idea, geographic_scope, answers],
                "phase": 4
            },
            
            # Phase 5: Citations (Final, aggregates all sources)
            {
                "agent_name": f"Agent {citations_agent['name']}",
                "agent_key": "citations",
                "title": "Citations & Sources Specialist",
                "background": citations_agent['background'], 
                "provider": "anthropic", 
                "task": "Citations & Source Verification",
                "icon": "📚",
                "section": "citations",
                "method": self._agent_citations_analysis,
                "args": [idea, answers],
                "phase": 5
            }
        ]
        
        logger.info(f"Generated {len(incepta_team)} specialized incepta-proto agents across 5 phases")
        return incepta_team
    
    async def _regional_market_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Regional specialist analysis"""
        prompt = f"""
        You are a Regional Market Specialist with deep expertise in {geographic_scope} markets.
        
        TASK: Analyze regional market dynamics for: {idea}
        GEOGRAPHIC FOCUS: {geographic_scope}
        CONTEXT: {answers}
        
        Provide specialized regional analysis covering:
        1. Local market characteristics and consumer behavior
        2. Regional competitors and market leaders
        3. Cultural and regulatory considerations
        4. Local partnership opportunities
        5. Market entry strategies specific to {geographic_scope}
        6. Regional pricing and monetization strategies
        7. Local talent and resource availability
        
        Format as structured JSON with region-specific insights.
        """
        
        try:
            return await self._call_ai_provider(prompt, AIProvider.OPENAI, max_tokens=2000, use_external_tools=True)
        except Exception as e:
            logger.error(f"Regional market analysis failed: {str(e)}")
            return f"Regional analysis encountered an error: {str(e)}"
    
    async def _external_market_research(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent Lisa Zhang - Market Intelligence Specialist"""
        prompt = f"""
        You are Agent Lisa Zhang, a Market Intelligence Specialist with expertise in external data collection and market surveillance.
        
        TASK: Collect and analyze external market intelligence for: {idea}
        GEOGRAPHIC FOCUS: {geographic_scope}
        CONTEXT: {answers}
        
        Use external data sources and market intelligence to provide:
        1. Recent market trends and developments
        2. Emerging competitor landscape
        3. Market timing and seasonality factors
        4. External validation of market opportunity
        5. Industry expert opinions and forecasts
        
        Format as structured JSON with specific data points and sources.
        """
        
        try:
            external_data = ""
            
            # Use Tavily for market research
            if self.tavily_client:
                search_query = f"{idea} market trends {geographic_scope} 2024 2025"
                tavily_results = await self._search_with_tavily(search_query)
                external_data += f"\n\nTAVILY MARKET RESEARCH:\n{tavily_results}"
            
            # Use Firecrawl for website intelligence
            if self.firecrawl_client:
                # Search for relevant websites to crawl based on the idea
                relevant_urls = await self._get_relevant_websites_for_idea(idea)
                if relevant_urls:
                    firecrawl_data = await self._crawl_websites_with_firecrawl(relevant_urls[:3])  # Limit to 3 URLs
                    external_data += f"\n\nFIRECRAWL WEBSITE INTELLIGENCE:\n{firecrawl_data}"
            
            if external_data:
                prompt += f"\n\nEXTERNAL DATA SOURCES:{external_data}"
            
            return await self._call_ai_provider(prompt, AIProvider.OPENAI, max_tokens=2000, use_external_tools=True)
        except Exception as e:
            logger.error(f"External market research failed: {str(e)}")
            return f"Market intelligence analysis encountered an error: {str(e)}"
    
    async def _get_relevant_websites_for_idea(self, idea: str) -> list:
        """Generate relevant website URLs to crawl based on the idea"""
        try:
            # Extract keywords from idea to build potential URLs
            keywords = idea.lower().split()
            
            # Common industry/startup websites to check
            potential_urls = []
            
            # Add industry-specific websites based on keywords
            if any(word in idea.lower() for word in ['ai', 'artificial intelligence', 'machine learning', 'ml']):
                potential_urls.extend([
                    'https://venturebeat.com/ai/',
                    'https://techcrunch.com/category/artificial-intelligence/',
                    'https://www.cbinsights.com/research/artificial-intelligence-trends/'
                ])
            
            if any(word in idea.lower() for word in ['health', 'medical', 'healthcare', 'symptom']):
                potential_urls.extend([
                    'https://www.healthtechmagazine.net/',
                    'https://mobihealthnews.com/',
                    'https://www.fiercehealthcare.com/'
                ])
            
            if any(word in idea.lower() for word in ['fintech', 'finance', 'payment', 'banking']):
                potential_urls.extend([
                    'https://finovate.com/',
                    'https://www.pymnts.com/',
                    'https://www.finextra.com/'
                ])
            
            # Fallback to general startup/tech websites
            if not potential_urls:
                potential_urls = [
                    'https://techcrunch.com/',
                    'https://www.startupgrind.com/',
                    'https://angel.co/blog'
                ]
            
            return potential_urls[:3]  # Return max 3 URLs
            
        except Exception as e:
            logger.error(f"Error generating website URLs: {str(e)}")
            return []
    
    async def _crawl_websites_with_firecrawl(self, urls: list) -> str:
        """Crawl websites using Firecrawl and extract relevant content"""
        try:
            if not self.firecrawl_client:
                return "Firecrawl client not available"
            
            crawled_content = []
            
            for url in urls:
                try:
                    # Use Firecrawl to scrape the website
                    result = self.firecrawl_client.scrape_url(url, params={
                        'formats': ['markdown'],
                        'includeTags': ['title', 'h1', 'h2', 'h3', 'p', 'article'],
                        'excludeTags': ['nav', 'footer', 'aside', 'script', 'style'],
                        'onlyMainContent': True
                    })
                    
                    if result and 'markdown' in result:
                        content = result['markdown'][:1000]  # Limit content length
                        crawled_content.append(f"Source: {url}\nContent: {content}")
                        
                except Exception as crawl_error:
                    logger.error(f"Failed to crawl {url}: {str(crawl_error)}")
                    continue
            
            return "\n\n".join(crawled_content) if crawled_content else "No content successfully crawled"
            
        except Exception as e:
            logger.error(f"Firecrawl processing failed: {str(e)}")
            return f"Firecrawl analysis encountered an error: {str(e)}"
    
    async def _legal_compliance_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent Marcus Thompson - Legal & Compliance Analyst"""
        prompt = f"""
        You are Agent Marcus Thompson, a Legal & Compliance Analyst specializing in regulatory requirements and legal frameworks.
        
        TASK: Analyze legal and compliance requirements for: {idea}
        GEOGRAPHIC FOCUS: {geographic_scope}
        CONTEXT: {answers}
        
        Provide comprehensive analysis of:
        1. Regulatory compliance requirements
        2. Licensing and permits needed
        3. Data privacy and protection laws (GDPR, CCPA, local regulations)
        4. Industry-specific regulations
        5. Intellectual property considerations
        6. Terms of service and liability issues
        7. International compliance for cross-border operations
        
        Format as structured JSON with specific legal requirements and compliance steps.
        """
        
        try:
            return await self._call_ai_provider(prompt, AIProvider.ANTHROPIC, max_tokens=2000, use_external_tools=False)
        except Exception as e:
            logger.error(f"Legal compliance analysis failed: {str(e)}")
            return f"Legal compliance analysis encountered an error: {str(e)}"
    
    async def _consolidate_multi_analyst_reports(self, idea: str, answers: dict, analyses: list) -> dict:
        """Consolidate multiple analyst reports into final incepta-proto style validation"""
        try:
            # Organize analyses by section
            sections_data = {}
            citations_data = []
            
            for analysis in analyses:
                section = analysis.get('section', 'general')
                sections_data[section] = {
                    'agent': analysis['agent'],
                    'title': analysis['title'],
                    'analysis': analysis['analysis'],
                    'provider': analysis['provider']
                }
            
            # Extract citations data if available
            if 'citations' in sections_data:
                citations_data = sections_data['citations']['analysis']
            
            consolidation_prompt = f"""
You are the Lead Validation Coordinator for Incepta, consolidating specialized analyst reports into the final validation report.

ORIGINAL IDEA: {idea}
USER CONTEXT: {answers}

SPECIALIZED ANALYST REPORTS BY SECTION:
{self._format_sections_for_consolidation(sections_data)}

Create a comprehensive validation report following the EXACT incepta-proto structure in JSON format:
{{
    "validation_summary": {{
        "overall_score": 8.5,
        "recommendation": "Proceed/Cautious/Stop with detailed reasoning",
        "confidence_level": "High/Medium/Low with explanation",
        "key_strengths": ["Extract top 3-5 strengths from all specialist analyses"],
        "key_concerns": ["Extract top 2-4 concerns from all specialist analyses"],
        "market_timing": "Optimal/Good/Challenging with market timing analysis",
        "investment_requirement": "Estimated investment and funding needs",
        "time_to_market": "Estimated development and launch timeline",
        "risk_level": "Low/Medium/High overall risk assessment"
    }},
    "project_concept": {{
        "project_overview": "Detailed overview from Project Concept Specialist",
        "user_roles": "User roles and personas from Project Concept Specialist",
        "problem_statement": "Core problem being solved",
        "solution_approach": "How the solution addresses the problem",
        "success_criteria": "What defines success for this project"
    }},
    "target_users": {{
        "primary_personas": "Detailed user personas from Target Users Specialist",
        "market_segmentation": "Market segments from Target Users Specialist",
        "user_pain_points": "Key pain points users experience",
        "user_journey": "How users will interact with the solution",
        "demographic_analysis": "Target demographic characteristics"
    }},
    "features": {{
        "core_features": "Essential features from Features Specialist",
        "must_have_features": "Critical features from Features Specialist", 
        "should_have_features": "Important features from Features Specialist",
        "nice_to_have_features": "Optional features from Features Specialist",
        "future_features": "Roadmap features from Features Specialist",
        "feature_prioritization": "Priority matrix and development sequence",
        "technical_complexity": "Development complexity assessment"
    }},
    "market_analysis": {{
        "market_size": "TAM/SAM/SOM analysis from Market Size Analyst",
        "target_market": "Market positioning from Target Market Specialist",
        "market_trends": "Current and emerging market trends",
        "growth_potential": "Market growth projections and potential",
        "geographic_considerations": "Regional market characteristics"
    }},
    "competitive_landscape": {{
        "direct_competitors": "From Competitive Intelligence Specialist",
        "indirect_competitors": "Alternative solutions and substitutes",
        "competitive_advantages": "Key differentiators and advantages",
        "competitive_threats": "Potential threats from competition",
        "market_positioning": "Positioning strategy vs competitors"
    }},
    "business_model": {{
        "value_proposition": "Unique value proposition from Value Proposition Specialist",
        "monetization_strategies": "Revenue models from Monetization Analyst",
        "pricing_strategy": "Pricing approach and rationale",
        "revenue_streams": "Multiple revenue stream opportunities",
        "cost_structure": "Key cost components and structure",
        "unit_economics": "Basic unit economics and profitability"
    }},
    "challenges_and_opportunities": {{
        "market_challenges": "Key challenges from Market Challenges Analyst",
        "market_opportunities": "Opportunities from Market Opportunities Specialist",
        "risk_mitigation": "Strategies to address key risks",
        "growth_catalysts": "Factors that could accelerate growth",
        "regulatory_considerations": "Compliance and regulatory factors"
    }},
    "financial_projections": {{
        "revenue_projections": "3-5 year revenue estimates",
        "investment_requirements": "Capital needs and funding requirements",
        "break_even_analysis": "Time to profitability",
        "key_metrics": "Important KPIs and success metrics",
        "financial_risks": "Key financial risk factors"
    }},
    "implementation_roadmap": {{
        "development_phases": "Phased development approach",
        "timeline_estimates": "Key milestones and timeframes",
        "resource_requirements": "Team and resource needs",
        "go_to_market_strategy": "Launch and marketing approach",
        "scaling_strategy": "Plan for growth and expansion"
    }},
    "citations_and_sources": {{
        "sources_by_section": "Organized sources from Citations Specialist",
        "verified_sources": "Credible and verified references",
        "total_sources": 0,
        "credibility_assessment": "Source quality and reliability analysis",
        "external_research": "Key external studies and reports referenced"
    }},
    "executive_summary": "Comprehensive 3-4 paragraph executive summary covering key findings, recommendation, and strategic insights",
    "next_steps": {{
        "immediate_actions": ["Specific actionable steps for next 30 days"],
        "short_term_goals": ["3-6 month objectives and milestones"],
        "long_term_strategy": ["12-24 month strategic initiatives"],
        "decision_points": ["Key decisions that need to be made"],
        "validation_steps": ["Additional validation or research needed"]
    }},
    "success_metrics": ["Comprehensive KPIs and success metrics"],
    "appendix": {{
        "methodology": "Validation methodology and approach used",
        "assumptions": "Key assumptions made in the analysis",
        "limitations": "Analysis limitations and caveats",
        "additional_resources": "Useful resources for further research"
    }}
}}

CRITICAL INSTRUCTIONS:
1. Extract ALL data from each specialist's analysis - don't summarize or lose details
2. Map each specialist's findings to the appropriate JSON sections
3. For financial projections, synthesize from market size + monetization analyses
4. For implementation roadmap, combine insights from features + technical + market analyses
5. Ensure the executive summary captures the essence of all specialist findings
6. Include specific numbers, percentages, and quantitative data where available
7. Resolve conflicts between specialists by noting different perspectives
8. Fill ALL sections with substantive content - no placeholders or generic responses
9. The response must be valid JSON that can be parsed
10. Include methodology and assumptions used by each specialist
"""
            
            try:
                final_report = await self._call_ai_provider(
                    consolidation_prompt, 
                    AIProvider.ANTHROPIC, 
                    max_tokens=4000, 
                    use_external_tools=False  # Disable external tools for consolidation to avoid timeouts
                )
            except Exception as ai_error:
                logger.error(f"Anthropic consolidation failed: {str(ai_error)}, trying OpenAI fallback")
                # Fallback to OpenAI with simpler prompt
                simple_prompt = f"""
Create a validation report for: {idea}

Based on analysis data: {str(sections_data)[:1000]}...

Return valid JSON with these sections:
{{
    "validation_summary": {{
        "overall_score": 8.0,
        "recommendation": "Analysis completed",
        "key_strengths": ["Comprehensive analysis", "Multi-agent validation"],
        "key_concerns": ["Requires further market validation"]
    }},
    "executive_summary": "Comprehensive validation completed using multi-agent analysis",
    "project_concept": {{"problem_statement": "Market need identified", "solution_overview": "AI-powered solution"}},
    "target_users": {{"primary_personas": "Target market identified"}},
    "market_analysis": {{"market_size": "Market opportunity assessed"}},
    "features": {{"core_features": ["Primary functionality defined"]}},
    "business_model": {{"value_proposition": "Value proposition established"}},
    "financial_projections": {{"investment_requirements": "Funding needs assessed"}},
    "competitive_landscape": {{"direct_competitors": ["Market competitors identified"]}},
    "challenges_and_opportunities": {{"market_challenges": ["Challenges identified"]}},
    "implementation_roadmap": {{"development_phases": ["Implementation phases outlined"]}},
    "next_steps": {{"immediate_actions": ["Next steps defined"]}}
}}
"""
                try:
                    final_report = await self._call_ai_provider(
                        simple_prompt,
                        AIProvider.OPENAI,
                        max_tokens=2000,
                        use_external_tools=False
                    )
                except Exception as openai_error:
                    logger.error(f"OpenAI consolidation fallback failed: {str(openai_error)}")
                    # Final fallback - return basic structured report
                    final_report = json.dumps({
                        "validation_summary": {
                            "overall_score": 7.5,
                            "recommendation": "Analysis completed with basic consolidation",
                            "key_strengths": ["Multi-agent analysis completed", "Comprehensive research conducted"],
                            "key_concerns": ["Consolidation required fallback", "Manual review recommended"]
                        },
                        "executive_summary": f"Validation analysis completed for: {idea}. {len(analyses)} specialist agents provided detailed analysis.",
                        "analysis_data": sections_data,
                        "providers_used": list(set([a['provider'] for a in analyses])),
                        "validation_metadata": {
                            "status": "completed_with_fallback",
                            "analysts_count": len(analyses)
                        }
                    })
            
            # Try to parse as JSON, fallback to structured response
            try:
                parsed_report = json.loads(final_report)
                # Add metadata about the validation process
                parsed_report['validation_metadata'] = {
                    'validation_date': answers.get('timestamp', 'N/A'),
                    'analysts_consulted': len(analyses),
                    'sections_analyzed': list(sections_data.keys()),
                    'ai_providers_used': list(set([a['provider'] for a in analyses])),
                    'external_tools_used': ['tavily', 'firecrawl'],
                    'validation_depth': 'Comprehensive',
                    'incepta_proto_style': True
                }
                return parsed_report
            except json.JSONDecodeError:
                logger.warning("Failed to parse consolidation as JSON, using enhanced fallback structure")
                return {
                    "validation_summary": {
                        "overall_score": 8.0,
                        "recommendation": "Proceed with caution - JSON parsing failed",
                        "confidence_level": "Medium",
                        "key_strengths": ["Comprehensive analysis completed", "All specialists consulted"],
                        "key_concerns": ["Report parsing issues", "May require manual review"],
                        "market_timing": "Analysis completed but formatting issues occurred",
                        "risk_level": "Medium due to parsing limitations"
                    },
                    "project_concept": {"raw_analysis": "See final_report_text for project concept details"},
                    "target_users": {"raw_analysis": "See final_report_text for target users analysis"},
                    "features": {"raw_analysis": "See final_report_text for features breakdown"},
                    "market_analysis": {"raw_analysis": "See final_report_text for market analysis"},
                    "competitive_landscape": {"raw_analysis": "See final_report_text for competitive insights"},
                    "business_model": {"raw_analysis": "See final_report_text for business model details"},
                    "challenges_and_opportunities": {"raw_analysis": "See final_report_text for challenges and opportunities"},
                    "financial_projections": {"raw_analysis": "See final_report_text for financial analysis"},
                    "implementation_roadmap": {"raw_analysis": "See final_report_text for implementation details"},
                    "citations_and_sources": {"raw_analysis": "See final_report_text for source information"},
                    "executive_summary": "Comprehensive incepta-proto validation completed with specialist analysis. See final_report_text for complete findings due to JSON parsing limitations.",
                    "final_report_text": final_report,
                    "analysts_consulted": [a['agent'] for a in analyses],
                    "sections_analyzed": list(sections_data.keys()),
                    "validation_metadata": {
                        "validation_date": answers.get('timestamp', 'N/A'),
                        "analysts_consulted": len(analyses),
                        "incepta_proto_style": True,
                        "parsing_status": "fallback",
                        "sections_completed": len(sections_data),
                        "ai_providers_used": list(set([a['provider'] for a in analyses])),
                        "external_tools_used": ['tavily', 'firecrawl']
                    }
                }
                
        except Exception as e:
            logger.error(f"Incepta-proto consolidation failed: {str(e)}")
            return {
                "error": f"Consolidation failed: {str(e)}",
                "partial_analyses": len(analyses),
                "validation_metadata": {
                    "validation_status": "failed",
                    "error_type": "consolidation_error"
                }
            }
    
    def _format_sections_for_consolidation(self, sections_data: dict) -> str:
        """Format section data for consolidation prompt"""
        formatted = []
        for section, data in sections_data.items():
            formatted.append(f"""
=== {section.upper()} SECTION ===
Agent: {data['agent']} ({data['title']})
Provider: {data['provider']}
Analysis: {data['analysis']}
""")
        return "\n".join(formatted)

    async def _celery_mixture_of_agents(self, idea: str, answers: dict) -> str:
        """Run mixture-of-agents validation using Celery for true parallel processing"""
        try:
            from app.tasks.validation_tasks import ai_analysis_task, external_tool_task, consolidation_task
            from celery import group
            
            logger.info("Starting Celery-based parallel validation")
            
            # Launch AI analysis tasks in parallel
            ai_providers = ['openai', 'anthropic', 'deepseek', 'perplexity']
            ai_job = group([
                ai_analysis_task.s(provider, idea, answers) 
                for provider in ai_providers
            ])
            
            # Launch external tool tasks in parallel
            external_tools = ['tavily', 'firecrawl']
            tool_job = group([
                external_tool_task.s(tool, idea, answers) 
                for tool in external_tools
            ])
            
            # Execute both groups in parallel
            ai_result = ai_job.apply_async()
            tool_result = tool_job.apply_async()
            
            # Wait for results with timeout
            ai_results = ai_result.get(timeout=60)  # 60 seconds for AI analysis
            tool_results = tool_result.get(timeout=30)  # 30 seconds for tool data
            
            logger.info(f"Parallel processing completed: {len(ai_results)} AI results, {len(tool_results)} tool results")
            
            # Consolidate results
            consolidation_result = consolidation_task.apply_async(
                args=[idea, answers, ai_results, tool_results]
            )
            
            final_report = consolidation_result.get(timeout=30)
            
            return final_report
            
        except Exception as e:
            logger.error(f"Celery mixture-of-agents failed: {str(e)}")
            raise e
    
    # ======= ANALYST AGENTS (Incepta-Proto Style) =======
    
    async def _agent_ross_market_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent Ross - Senior Market Research Analyst"""
        prompt = f"""
You are Agent Ross, a Senior Market Research Analyst with 15+ years of experience in market validation and competitive analysis. You specialize in TAM/SAM/SOM analysis and identifying market opportunities.

TASK: Analyze the market potential for this idea: {idea}
GEOGRAPHIC FOCUS: {geographic_scope}
ADDITIONAL CONTEXT: {answers}

Provide a detailed JSON response with the following structure:
{{
    "analyst": "Agent Ross - Sr. Market Research Analyst",
    "analysis_type": "Market Research & Validation",
    "market_size": {{
        "TAM": "Total Addressable Market with specific dollar amount",
        "SAM": "Serviceable Addressable Market with specific dollar amount", 
        "SOM": "Serviceable Obtainable Market with specific dollar amount"
    }},
    "target_market": "Detailed description of primary target market",
    "competitive_landscape": [
        {{
            "competitor": "Company Name",
            "strengths": ["strength1", "strength2"],
            "weaknesses": ["weakness1", "weakness2"],
            "market_share": "percentage or description"
        }}
    ],
    "market_trends": [
        "trend 1 with specific data",
        "trend 2 with growth rates",
        "trend 3 with market drivers"
    ],
    "opportunities": [
        "specific market opportunity 1",
        "specific market opportunity 2", 
        "specific market opportunity 3"
    ],
    "challenges": [
        "market challenge 1",
        "market challenge 2",
        "market challenge 3"
    ],
    "recommended_go_to_market": "Specific GTM strategy recommendation",
    "confidence_score": 8.5
}}

Be specific with numbers, cite realistic market data, and focus on {geographic_scope} when relevant.
"""
        
        try:
            logger.info("🔍 Agent Ross analyzing market research...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Agent Ross analysis failed: {str(e)}")
            return '{"analyst": "Agent Ross", "error": "Market analysis unavailable"}'
    
    async def _agent_mohammed_financial_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent Mohammed - Senior Financial Analyst"""
        prompt = f"""
You are Agent Mohammed, a Senior Financial Analyst with 12+ years in startup valuation, financial modeling, and investment analysis. You excel at revenue projections and business model validation.

TASK: Conduct financial feasibility analysis for: {idea}
GEOGRAPHIC FOCUS: {geographic_scope}  
ADDITIONAL CONTEXT: {answers}

Provide a detailed JSON response with this structure:
{{
    "analyst": "Agent Mohammed - Sr. Financial Analyst", 
    "analysis_type": "Financial Feasibility & Business Model",
    "revenue_model": {{
        "primary_revenue_streams": ["stream1", "stream2", "stream3"],
        "pricing_strategy": "Description of recommended pricing approach",
        "unit_economics": {{
            "customer_acquisition_cost": "$X",
            "lifetime_value": "$Y", 
            "gross_margin": "Z%"
        }}
    }},
    "financial_projections": {{
        "year_1_revenue": "$amount",
        "year_2_revenue": "$amount", 
        "year_3_revenue": "$amount",
        "break_even_timeline": "X months",
        "funding_required": "$amount"
    }},
    "monetization_strategies": [
        "Primary monetization approach",
        "Secondary revenue opportunity",
        "Long-term monetization potential"
    ],
    "cost_structure": {{
        "development_costs": "$amount range",
        "operational_costs": "$monthly amount",
        "marketing_costs": "$amount or % of revenue"
    }},
    "financial_risks": [
        "Key financial risk 1",
        "Key financial risk 2", 
        "Key financial risk 3"
    ],
    "investment_attractiveness": {{
        "score": 8.0,
        "reasoning": "Why investors would/wouldn't be interested",
        "comparable_valuations": "Similar companies and their valuations"
    }},
    "recommended_funding_strategy": "Bootstrap/Angel/VC/etc with reasoning"
}}

Focus on realistic financial projections based on {geographic_scope} market conditions.
"""
        
        try:
            logger.info("💰 Agent Mohammed analyzing financials...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Agent Mohammed analysis failed: {str(e)}")
            return '{"analyst": "Agent Mohammed", "error": "Financial analysis unavailable"}'
    
    async def _agent_sarah_technical_analysis(self, idea: str, tech_involvement: str, answers: dict) -> str:
        """Agent Sarah - Senior Technical Architect"""
        prompt = f"""
You are Agent Sarah, a Senior Technical Architect with 18+ years in software development, system design, and technology stack optimization. You specialize in technical feasibility and scalability analysis.

TASK: Assess technical feasibility for: {idea}
TECHNOLOGY CONTEXT: {tech_involvement}
ADDITIONAL CONTEXT: {answers}

Provide a detailed JSON response with this structure:
{{
    "analyst": "Agent Sarah - Sr. Technical Architect",
    "analysis_type": "Technical Feasibility & Architecture",
    "technical_complexity": {{
        "overall_complexity": "Low/Medium/High",
        "complexity_reasoning": "Why this complexity level",
        "development_timeline": "X-Y months for MVP"
    }},
    "recommended_tech_stack": {{
        "frontend": ["technology1", "technology2"],
        "backend": ["technology1", "technology2"], 
        "database": "recommended database",
        "infrastructure": "cloud/hosting recommendations",
        "third_party_apis": ["API1", "API2"]
    }},
    "technical_challenges": [
        {{
            "challenge": "Technical challenge 1",
            "complexity": "High/Medium/Low",
            "solution_approach": "How to address this challenge"
        }},
        {{
            "challenge": "Technical challenge 2", 
            "complexity": "High/Medium/Low",
            "solution_approach": "How to address this challenge"
        }}
    ],
    "scalability_assessment": {{
        "user_capacity": "Number of concurrent users supportable",
        "data_volume": "Data handling capabilities",
        "performance_bottlenecks": ["bottleneck1", "bottleneck2"],
        "scaling_strategy": "How to scale as user base grows"
    }},
    "development_resources": {{
        "team_size_needed": "X developers",
        "required_skills": ["skill1", "skill2", "skill3"],
        "external_expertise_needed": ["expertise1", "expertise2"]
    }},
    "security_considerations": [
        "Security concern 1",
        "Security concern 2",
        "Compliance requirements"
    ],
    "mvp_features": [
        "Core feature 1 for MVP",
        "Core feature 2 for MVP", 
        "Core feature 3 for MVP"
    ],
    "technical_feasibility_score": 8.5
}}

Focus on practical technical solutions and realistic development estimates.
"""
        
        try:
            logger.info("⚙️ Agent Sarah analyzing technical feasibility...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Agent Sarah analysis failed: {str(e)}")
            return '{"analyst": "Agent Sarah", "error": "Technical analysis unavailable"}'
    
    async def _agent_david_risk_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent David - Senior Risk Assessment Specialist"""
        prompt = f"""
You are Agent David, a Senior Risk Assessment Specialist with 20+ years in startup risk analysis, regulatory compliance, and strategic planning. You excel at identifying and quantifying business risks.

TASK: Comprehensive risk assessment for: {idea}
GEOGRAPHIC FOCUS: {geographic_scope}
ADDITIONAL CONTEXT: {answers}

Provide a detailed JSON response with this structure:
{{
    "analyst": "Agent David - Sr. Risk Assessment Specialist",
    "analysis_type": "Comprehensive Risk Assessment",
    "market_risks": [
        {{
            "risk": "Market risk description",
            "probability": "High/Medium/Low", 
            "impact": "High/Medium/Low",
            "mitigation_strategy": "How to mitigate this risk"
        }}
    ],
    "competitive_risks": [
        {{
            "risk": "Competitive threat description",
            "probability": "High/Medium/Low",
            "impact": "High/Medium/Low", 
            "mitigation_strategy": "How to address this threat"
        }}
    ],
    "operational_risks": [
        {{
            "risk": "Operational risk description",
            "probability": "High/Medium/Low",
            "impact": "High/Medium/Low",
            "mitigation_strategy": "How to minimize this risk"
        }}
    ],
    "regulatory_risks": [
        {{
            "risk": "Regulatory/compliance risk",
            "probability": "High/Medium/Low", 
            "impact": "High/Medium/Low",
            "mitigation_strategy": "Compliance approach"
        }}
    ],
    "financial_risks": [
        {{
            "risk": "Financial risk description",
            "probability": "High/Medium/Low",
            "impact": "High/Medium/Low", 
            "mitigation_strategy": "Financial risk management"
        }}
    ],
    "technology_risks": [
        {{
            "risk": "Technology/security risk",
            "probability": "High/Medium/Low",
            "impact": "High/Medium/Low",
            "mitigation_strategy": "Technical risk mitigation"
        }}
    ],
    "overall_risk_assessment": {{
        "risk_level": "Low/Medium/High",
        "key_success_factors": ["factor1", "factor2", "factor3"],
        "critical_assumptions": ["assumption1", "assumption2"],
        "recommended_risk_monitoring": ["metric1", "metric2"]
    }},
    "contingency_planning": [
        "Contingency plan for scenario 1",
        "Contingency plan for scenario 2", 
        "Contingency plan for scenario 3"
    ],
    "risk_score": 7.0
}}

Focus on {geographic_scope} specific risks and provide actionable mitigation strategies.
"""
        
        try:
            logger.info("⚠️ Agent David assessing risks...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Agent David analysis failed: {str(e)}")
            return '{"analyst": "Agent David", "error": "Risk analysis unavailable"}'
    
    # =============================================================================
    # INCEPTA-PROTO SPECIALIZED AGENT METHODS
    # =============================================================================
    
    async def _agent_project_concept_analysis(self, idea: str, answers: dict) -> str:
        """Agent for Project Concept - generates project overview and user roles"""
        project_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Senior Project Concept Specialist with expertise in defining project scope and user role modeling.

TASK: Analyze the project concept for: {idea}
CONTEXT: {project_context}

Provide a comprehensive project concept analysis in JSON format:
{{
    "project_overview": {{
        "executive_summary": "2-3 sentence project overview",
        "core_value_proposition": "Main value delivered to users",
        "problem_statement": "Problem being solved",
        "solution_approach": "How the solution addresses the problem",
        "success_metrics": ["metric1", "metric2", "metric3"]
    }},
    "user_roles": [
        {{
            "role_name": "Primary User Type",
            "description": "Role description", 
            "responsibilities": ["responsibility1", "responsibility2"],
            "pain_points": ["pain1", "pain2"],
            "goals": ["goal1", "goal2"],
            "interaction_frequency": "Daily/Weekly/Monthly"
        }}
    ],
    "stakeholder_map": {{
        "primary_users": ["user type 1", "user type 2"],
        "secondary_users": ["user type 3"],
        "decision_makers": ["decision maker 1"],
        "influencers": ["influencer 1"]
    }}
}}

Use mixture-of-agents approach with external market data for validation.
"""
        
        try:
            logger.info("🎯 Project Concept Specialist analyzing project structure...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Project Concept analysis failed: {str(e)}")
            return '{"analyst": "Project Concept Specialist", "error": "Project concept analysis unavailable"}'
    
    async def _agent_target_users_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Target Users - generates detailed user personas and segmentation"""
        user_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Target Users Specialist with expertise in user research and market segmentation.

TASK: Define target users for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {user_context}

Generate comprehensive target user analysis in JSON format:
{{
    "primary_personas": [
        {{
            "persona_name": "User Persona Name",
            "demographic": {{
                "age_range": "25-35",
                "income_level": "Income bracket",
                "education": "Education level",
                "occupation": "Job category",
                "location": "Geographic preference"
            }},
            "psychographic": {{
                "values": ["value1", "value2"],
                "interests": ["interest1", "interest2"],
                "pain_points": ["pain1", "pain2"],
                "motivations": ["motivation1", "motivation2"]
            }},
            "behavioral": {{
                "technology_adoption": "Early/Mainstream/Late adopter",
                "spending_habits": "Description",
                "preferred_channels": ["channel1", "channel2"],
                "decision_factors": ["factor1", "factor2"]
            }},
            "user_journey": {{
                "awareness": "How they discover the problem",
                "consideration": "How they evaluate solutions", 
                "decision": "What drives their choice",
                "usage": "How they use the product",
                "advocacy": "What makes them recommend"
            }}
        }}
    ],
    "market_segmentation": {{
        "primary_segment": {{
            "name": "Segment name",
            "size": "Market size",
            "characteristics": ["char1", "char2"],
            "acquisition_strategy": "How to reach them"
        }},
        "secondary_segments": [
            {{
                "name": "Secondary segment",
                "potential": "Growth potential",
                "timeline": "When to target"
            }}
        ]
    }},
    "user_needs_hierarchy": {{
        "functional_needs": ["need1", "need2"],
        "emotional_needs": ["need1", "need2"], 
        "social_needs": ["need1", "need2"]
    }}
}}

Use external research and market data for {geographic_scope} demographics.
"""
        
        try:
            logger.info("👥 Target Users Specialist analyzing user segments...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Target Users analysis failed: {str(e)}")
            return '{"analyst": "Target Users Specialist", "error": "Target users analysis unavailable"}'
    
    async def _agent_features_analysis(self, idea: str, tech_involvement: str, answers: dict) -> str:
        """Agent for Features - categorizes all features by priority"""
        features_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Features Categorization Specialist with expertise in product roadmap planning and feature prioritization.

TASK: Define and categorize features for: {idea}
TECH INVOLVEMENT: {tech_involvement}
CONTEXT: {features_context}

Generate comprehensive features analysis in JSON format:
{{
    "core_features": [
        {{
            "name": "Feature name",
            "description": "What it does",
            "user_value": "Value to users",
            "technical_complexity": "Low/Medium/High",
            "development_effort": "Weeks/Months",
            "dependencies": ["dependency1"]
        }}
    ],
    "must_have_features": [
        {{
            "name": "Feature name",
            "description": "Feature description",
            "justification": "Why it's must-have",
            "user_impact": "Impact on user experience",
            "business_impact": "Impact on business goals"
        }}
    ],
    "should_have_features": [
        {{
            "name": "Feature name", 
            "description": "Feature description",
            "value_add": "Additional value provided",
            "implementation_timeline": "When to implement"
        }}
    ],
    "nice_to_have_features": [
        {{
            "name": "Feature name",
            "description": "Feature description", 
            "opportunity_cost": "Cost vs benefit",
            "user_delight_factor": "How it enhances experience"
        }}
    ],
    "future_features": [
        {{
            "name": "Feature name",
            "description": "Feature description",
            "innovation_potential": "Future opportunity",
            "market_timing": "When market will be ready",
            "technical_prerequisites": ["prereq1", "prereq2"]
        }}
    ],
    "feature_prioritization_matrix": {{
        "high_impact_low_effort": ["feature1", "feature2"],
        "high_impact_high_effort": ["feature1", "feature2"],
        "low_impact_low_effort": ["feature1", "feature2"],
        "low_impact_high_effort": ["feature1", "feature2"]
    }},
    "development_roadmap": {{
        "mvp_features": ["feature1", "feature2"],
        "version_1_additions": ["feature1", "feature2"],
        "future_iterations": ["feature1", "feature2"]
    }}
}}

Use mixture-of-agents for technical feasibility assessment and market feature analysis.
"""
        
        try:
            logger.info("🛠️ Features Specialist categorizing feature requirements...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 3000, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Features analysis failed: {str(e)}")
            return '{"analyst": "Features Specialist", "error": "Features analysis unavailable"}'
    
    async def _agent_market_size_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Market Size - TAM/SAM/SOM analysis"""
        market_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Market Size Analyst with expertise in TAM/SAM/SOM analysis and market opportunity assessment.

TASK: Analyze market size for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {market_context}

Provide comprehensive market size analysis in JSON format:
{{
    "market_size_analysis": {{
        "tam": {{
            "value": "$X billion",
            "description": "Total Addressable Market definition",
            "methodology": "How TAM was calculated",
            "data_sources": ["source1", "source2"],
            "growth_rate": "X% CAGR",
            "time_horizon": "2024-2030"
        }},
        "sam": {{
            "value": "$X million", 
            "description": "Serviceable Addressable Market definition",
            "geographic_scope": "{geographic_scope}",
            "market_constraints": ["constraint1", "constraint2"],
            "penetration_potential": "X% of TAM"
        }},
        "som": {{
            "value": "$X million",
            "description": "Serviceable Obtainable Market definition", 
            "realistic_capture": "X% of SAM",
            "timeline_to_achieve": "X years",
            "market_share_assumptions": ["assumption1", "assumption2"]
        }}
    }},
    "market_dynamics": {{
        "growth_drivers": ["driver1", "driver2", "driver3"],
        "market_maturity": "Emerging/Growing/Mature/Declining",
        "seasonal_factors": ["factor1", "factor2"],
        "cyclical_patterns": "Description of cycles"
    }},
    "addressable_segments": [
        {{
            "segment_name": "Segment 1",
            "size": "$X million",
            "growth_rate": "X%",
            "accessibility": "Easy/Medium/Hard",
            "competition_level": "Low/Medium/High"
        }}
    ],
    "market_entry_potential": {{
        "barriers_to_entry": ["barrier1", "barrier2"],
        "market_timing": "Optimal/Good/Challenging", 
        "first_mover_advantage": "Description",
        "network_effects_potential": "High/Medium/Low"
    }}
}}

Use external market research and industry data for {geographic_scope}.
"""
        
        try:
            logger.info("📈 Market Size Analyst calculating TAM/SAM/SOM...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Market Size analysis failed: {str(e)}")
            return '{"analyst": "Market Size Analyst", "error": "Market size analysis unavailable"}'
    
    async def _agent_target_market_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Target Market - market positioning and targeting strategy"""
        target_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Target Market Specialist with expertise in market positioning and go-to-market strategy.

TASK: Define target market strategy for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {target_context}

Generate comprehensive target market analysis in JSON format:
{{
    "market_positioning": {{
        "positioning_statement": "How the product is positioned",
        "competitive_positioning": "Position relative to competitors",
        "brand_positioning": "Brand personality and values",
        "price_positioning": "Premium/Mid-market/Budget"
    }},
    "primary_target_market": {{
        "market_definition": "Clear definition of primary market",
        "size_and_growth": "Market size and growth potential",
        "customer_characteristics": ["characteristic1", "characteristic2"],
        "buying_behavior": "How customers make decisions",
        "channel_preferences": ["channel1", "channel2"],
        "price_sensitivity": "High/Medium/Low"
    }},
    "secondary_markets": [
        {{
            "market_name": "Secondary market 1",
            "opportunity_size": "Market opportunity",
            "entry_strategy": "How to enter this market",
            "timeline": "When to pursue"
        }}
    ],
    "market_segmentation_strategy": {{
        "segmentation_basis": "Geographic/Demographic/Psychographic/Behavioral",
        "segment_priorities": ["segment1", "segment2", "segment3"],
        "segment_specific_approaches": {{
            "segment1": "Tailored approach for segment 1",
            "segment2": "Tailored approach for segment 2"
        }}
    }},
    "go_to_market_strategy": {{
        "market_entry_approach": "Direct/Channel/Partnership",
        "customer_acquisition_strategy": ["strategy1", "strategy2"],
        "pricing_strategy": "Pricing approach",
        "distribution_channels": ["channel1", "channel2"],
        "marketing_mix": {{
            "product": "Product positioning",
            "price": "Pricing strategy",
            "place": "Distribution approach", 
            "promotion": "Marketing approach"
        }}
    }},
    "geographic_expansion": {{
        "primary_geographic_focus": "{geographic_scope}",
        "expansion_sequence": ["region1", "region2"],
        "localization_requirements": ["requirement1", "requirement2"]
    }}
}}

Use external market intelligence for {geographic_scope} targeting insights.
"""
        
        try:
            logger.info("🎯 Target Market Specialist defining market positioning...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Target Market analysis failed: {str(e)}")
            return '{"analyst": "Target Market Specialist", "error": "Target market analysis unavailable"}'
    
    async def _agent_value_proposition_analysis(self, idea: str, answers: dict) -> str:
        """Agent for Value Proposition - unique selling points and differentiation"""
        value_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Value Proposition Specialist with expertise in competitive differentiation and unique selling point identification.

TASK: Define unique value proposition for: {idea}
CONTEXT: {value_context}

Generate comprehensive value proposition analysis in JSON format:
{{
    "unique_value_proposition": {{
        "core_value_statement": "Primary value delivered to customers",
        "differentiation_factors": ["differentiator1", "differentiator2", "differentiator3"],
        "competitive_advantages": ["advantage1", "advantage2"],
        "value_creation_mechanisms": ["mechanism1", "mechanism2"]
    }},
    "customer_value_analysis": {{
        "functional_benefits": ["benefit1", "benefit2"],
        "emotional_benefits": ["benefit1", "benefit2"],
        "economic_benefits": ["benefit1", "benefit2"],
        "social_benefits": ["benefit1", "benefit2"]
    }},
    "value_proposition_canvas": {{
        "customer_jobs": ["job1", "job2", "job3"],
        "pain_points": ["pain1", "pain2", "pain3"],
        "gain_creators": ["gain1", "gain2", "gain3"],
        "pain_relievers": ["reliever1", "reliever2"],
        "products_services": ["product1", "product2"]
    }},
    "competitive_differentiation": {{
        "vs_direct_competitors": "How we differ from direct competitors",
        "vs_indirect_competitors": "How we differ from indirect competitors",
        "vs_status_quo": "How we improve over current solutions",
        "barriers_to_imitation": ["barrier1", "barrier2"]
    }},
    "value_communication": {{
        "elevator_pitch": "30-second value proposition",
        "key_messaging": ["message1", "message2", "message3"],
        "proof_points": ["proof1", "proof2"],
        "success_stories": ["story1", "story2"]
    }}
}}

Use mixture-of-agents for competitive analysis and value assessment.
"""
        
        try:
            logger.info("💎 Value Proposition Specialist analyzing unique value...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Value Proposition analysis failed: {str(e)}")
            return '{"analyst": "Value Proposition Specialist", "error": "Value proposition analysis unavailable"}'
    
    async def _agent_monetization_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Monetization - revenue models and pricing strategies"""
        monetization_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Monetization Strategy Analyst with expertise in revenue model design and pricing optimization.

TASK: Define monetization strategies for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {monetization_context}

Generate comprehensive monetization analysis in JSON format:
{{
    "revenue_models": [
        {{
            "model_name": "Primary Revenue Model",
            "description": "How revenue is generated",
            "target_customers": "Who pays",
            "value_exchange": "What customers get for payment",
            "revenue_potential": "Estimated revenue potential",
            "implementation_complexity": "Low/Medium/High",
            "market_acceptance": "How readily market will adopt"
        }}
    ],
    "pricing_strategy": {{
        "pricing_model": "Subscription/One-time/Usage-based/Freemium",
        "price_points": {{
            "entry_level": "$X per unit",
            "premium": "$Y per unit",
            "enterprise": "$Z per unit"
        }},
        "pricing_rationale": "Why these price points make sense",
        "competitive_pricing_analysis": "How our pricing compares",
        "price_sensitivity_analysis": "Customer willingness to pay",
        "pricing_optimization_strategy": "How to test and optimize pricing"
    }},
    "monetization_timeline": {{
        "immediate_revenue": "Revenue sources available immediately",
        "short_term_revenue": "6-12 months revenue opportunities", 
        "long_term_revenue": "12+ months revenue potential",
        "scalability_factors": ["factor1", "factor2"]
    }},
    "revenue_diversification": {{
        "primary_revenue_stream": "Main revenue source (X% of total)",
        "secondary_streams": ["stream1", "stream2"],
        "ancillary_opportunities": ["opportunity1", "opportunity2"],
        "partnership_revenue": ["partner revenue 1", "partner revenue 2"]
    }},
    "financial_projections": {{
        "year_1_revenue": "$X",
        "year_2_revenue": "$Y", 
        "year_3_revenue": "$Z",
        "customer_acquisition_cost": "$A",
        "customer_lifetime_value": "$B",
        "gross_margin": "X%",
        "break_even_timeline": "X months"
    }},
    "monetization_risks": {{
        "pricing_risks": ["risk1", "risk2"],
        "market_acceptance_risks": ["risk1", "risk2"],
        "competitive_risks": ["risk1", "risk2"],
        "mitigation_strategies": ["strategy1", "strategy2"]
    }}
}}

Use external market data for {geographic_scope} pricing benchmarks and revenue analysis.
"""
        
        try:
            logger.info("💰 Monetization Analyst designing revenue strategies...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Monetization analysis failed: {str(e)}")
            return '{"analyst": "Monetization Analyst", "error": "Monetization analysis unavailable"}'
    
    async def _agent_competitors_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Competitors - competitive landscape and intelligence"""
        competitors_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Competitive Intelligence Specialist with expertise in competitor analysis and market positioning.

TASK: Analyze competitive landscape for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {competitors_context}

Generate comprehensive competitive analysis in JSON format:
{{
    "direct_competitors": [
        {{
            "company_name": "Competitor 1",
            "description": "What they do",
            "market_position": "Market leader/challenger/follower/niche",
            "strengths": ["strength1", "strength2"],
            "weaknesses": ["weakness1", "weakness2"],
            "market_share": "X% of market",
            "revenue_estimate": "$X million",
            "funding_status": "Funding stage and amount",
            "geographic_presence": "Geographic coverage",
            "key_differentiators": ["diff1", "diff2"],
            "threat_level": "High/Medium/Low"
        }}
    ],
    "indirect_competitors": [
        {{
            "company_name": "Indirect Competitor 1",
            "alternative_solution": "How they solve the problem differently",
            "market_overlap": "Where they compete with us",
            "substitution_risk": "High/Medium/Low"
        }}
    ],
    "competitive_landscape": {{
        "market_concentration": "Fragmented/Consolidated",
        "competition_intensity": "High/Medium/Low",
        "barriers_to_entry": ["barrier1", "barrier2"],
        "switching_costs": "Customer switching difficulty",
        "network_effects": "Present/Absent",
        "market_trends": ["trend1", "trend2"]
    }},
    "competitive_positioning": {{
        "our_position": "Where we fit in the competitive landscape",
        "competitive_advantages": ["advantage1", "advantage2"],
        "competitive_gaps": ["gap1", "gap2"],
        "differentiation_strategy": "How we stand out",
        "competitive_moats": ["moat1", "moat2"]
    }},
    "competitive_threats": {{
        "immediate_threats": ["threat1", "threat2"],
        "emerging_threats": ["threat1", "threat2"],
        "disruption_risks": ["risk1", "risk2"],
        "defensive_strategies": ["strategy1", "strategy2"]
    }},
    "competitive_opportunities": {{
        "market_gaps": ["gap1", "gap2"],
        "competitor_weaknesses": ["weakness1", "weakness2"],
        "white_space_opportunities": ["opportunity1", "opportunity2"],
        "partnership_opportunities": ["partner1", "partner2"]
    }}
}}

Use external web scraping and market intelligence for {geographic_scope} competitor data.
"""
        
        try:
            logger.info("🥊 Competitive Intelligence analyzing market landscape...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Competitors analysis failed: {str(e)}")
            return '{"analyst": "Competitive Intelligence", "error": "Competitive analysis unavailable"}'
    
    async def _agent_challenges_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Market Challenges - barriers and obstacles"""
        challenges_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Market Challenges Analyst with expertise in identifying barriers, risks, and obstacles to market success.

TASK: Identify market challenges for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {challenges_context}

Generate comprehensive market challenges analysis in JSON format:
{{
    "market_entry_barriers": {{
        "regulatory_barriers": ["barrier1", "barrier2"],
        "financial_barriers": ["barrier1", "barrier2"],
        "technical_barriers": ["barrier1", "barrier2"],
        "competitive_barriers": ["barrier1", "barrier2"],
        "customer_adoption_barriers": ["barrier1", "barrier2"]
    }},
    "operational_challenges": {{
        "talent_acquisition": ["challenge1", "challenge2"],
        "supply_chain": ["challenge1", "challenge2"],
        "technology_infrastructure": ["challenge1", "challenge2"],
        "scaling_challenges": ["challenge1", "challenge2"],
        "quality_control": ["challenge1", "challenge2"]
    }},
    "market_risks": {{
        "demand_risks": ["risk1", "risk2"],
        "competitive_risks": ["risk1", "risk2"],
        "technology_risks": ["risk1", "risk2"],
        "regulatory_risks": ["risk1", "risk2"],
        "economic_risks": ["risk1", "risk2"]
    }},
    "customer_challenges": {{
        "adoption_resistance": ["resistance1", "resistance2"],
        "education_requirements": ["requirement1", "requirement2"],
        "integration_complexity": ["complexity1", "complexity2"],
        "switching_costs": ["cost1", "cost2"],
        "trust_building": ["challenge1", "challenge2"]
    }},
    "financial_challenges": {{
        "funding_requirements": "Capital needed and difficulty to raise",
        "cash_flow_management": ["challenge1", "challenge2"],
        "pricing_pressure": ["pressure1", "pressure2"],
        "profitability_timeline": "Time to profitability challenges",
        "investor_concerns": ["concern1", "concern2"]
    }},
    "mitigation_strategies": {{
        "risk_mitigation": ["strategy1", "strategy2"],
        "barrier_reduction": ["approach1", "approach2"],
        "contingency_planning": ["plan1", "plan2"],
        "partnership_solutions": ["solution1", "solution2"],
        "phased_approach": ["phase1", "phase2"]
    }},
    "success_factors": {{
        "critical_success_factors": ["factor1", "factor2"],
        "key_milestones": ["milestone1", "milestone2"],
        "performance_indicators": ["kpi1", "kpi2"],
        "monitoring_framework": ["metric1", "metric2"]
    }}
}}

Use external market research for {geographic_scope} specific challenges and regulatory environment.
"""
        
        try:
            logger.info("⚠️ Market Challenges Analyst identifying barriers...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Market Challenges analysis failed: {str(e)}")
            return '{"analyst": "Market Challenges Analyst", "error": "Market challenges analysis unavailable"}'
    
    async def _agent_opportunities_analysis(self, idea: str, geographic_scope: str, answers: dict) -> str:
        """Agent for Market Opportunities - growth potential and expansion"""
        opportunities_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Market Opportunities Specialist with expertise in growth potential assessment and expansion strategy.

TASK: Identify market opportunities for: {idea}
GEOGRAPHIC SCOPE: {geographic_scope}
CONTEXT: {opportunities_context}

Generate comprehensive market opportunities analysis in JSON format:
{{
    "market_opportunities": {{
        "immediate_opportunities": [
            {{
                "opportunity": "Opportunity 1",
                "description": "Detailed description",
                "market_size": "Size of opportunity",
                "timeline": "When to pursue",
                "requirements": ["requirement1", "requirement2"],
                "success_probability": "High/Medium/Low"
            }}
        ],
        "emerging_opportunities": [
            {{
                "opportunity": "Emerging opportunity 1",
                "market_trend": "Underlying trend driving opportunity",
                "growth_potential": "Potential growth rate",
                "time_horizon": "2-5 years",
                "preparation_needed": ["preparation1", "preparation2"]
            }}
        ]
    }},
    "growth_vectors": {{
        "geographic_expansion": {{
            "primary_markets": ["{geographic_scope}"],
            "secondary_markets": ["market1", "market2"],
            "expansion_sequence": ["sequence1", "sequence2"],
            "localization_requirements": ["requirement1", "requirement2"]
        }},
        "product_expansion": {{
            "adjacent_products": ["product1", "product2"],
            "feature_extensions": ["extension1", "extension2"],
            "platform_opportunities": ["platform1", "platform2"],
            "ecosystem_plays": ["ecosystem1", "ecosystem2"]
        }},
        "market_expansion": {{
            "vertical_markets": ["vertical1", "vertical2"],
            "customer_segments": ["segment1", "segment2"],
            "use_case_expansion": ["use_case1", "use_case2"],
            "channel_opportunities": ["channel1", "channel2"]
        }}
    }},
    "strategic_opportunities": {{
        "partnership_opportunities": [
            {{
                "partner_type": "Technology/Distribution/Strategic",
                "potential_partners": ["partner1", "partner2"],
                "value_creation": "Mutual value proposition",
                "implementation_approach": "How to establish partnership"
            }}
        ],
        "acquisition_opportunities": ["target1", "target2"],
        "platform_opportunities": ["platform play 1", "platform play 2"],
        "ecosystem_opportunities": ["ecosystem1", "ecosystem2"]
    }},
    "innovation_opportunities": {{
        "technology_trends": ["trend1", "trend2"],
        "unmet_needs": ["need1", "need2"],
        "market_gaps": ["gap1", "gap2"],
        "disruptive_potential": ["disruption1", "disruption2"],
        "future_scenarios": ["scenario1", "scenario2"]
    }},
    "investment_opportunities": {{
        "funding_sources": ["source1", "source2"],
        "investor_interest_areas": ["area1", "area2"],
        "grant_opportunities": ["grant1", "grant2"],
        "strategic_investment": ["investor1", "investor2"]
    }},
    "timing_analysis": {{
        "market_readiness": "High/Medium/Low",
        "technology_readiness": "High/Medium/Low",
        "competitive_timing": "First mover/Fast follower/Late entrant",
        "optimal_entry_window": "Time period for market entry",
        "market_catalysts": ["catalyst1", "catalyst2"]
    }}
}}

Use external trend analysis and market intelligence for {geographic_scope} opportunity identification.
"""
        
        try:
            logger.info("🚀 Market Opportunities Specialist identifying growth potential...")
            response = await self._call_ai_provider(prompt, AIProvider.OPENAI, 2500, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Market Opportunities analysis failed: {str(e)}")
            return '{"analyst": "Market Opportunities Specialist", "error": "Market opportunities analysis unavailable"}'
    
    async def _agent_citations_analysis(self, idea: str, answers: dict) -> str:
        """Agent for Citations - aggregates and verifies all sources used by other agents"""
        citations_context = self._format_answers_for_prompt(answers)
        
        prompt = f"""
You are a Citations & Sources Specialist with expertise in source verification and academic citation standards.

TASK: Aggregate and verify all sources used in validation for: {idea}
CONTEXT: {citations_context}

IMPORTANT: This agent should collect citations from all previous agents' external tool usage (Tavily searches, Firecrawl scraping).
Generate comprehensive citations analysis in JSON format:
{{
    "source_summary": {{
        "total_sources": 0,
        "verified_sources": 0,
        "source_types": {{
            "academic_papers": 0,
            "industry_reports": 0,
            "news_articles": 0,
            "company_websites": 0,
            "government_data": 0,
            "market_research": 0
        }},
        "credibility_assessment": "Overall source credibility rating"
    }},
    "citations_by_section": {{
        "project_concept": [
            {{
                "title": "Source title",
                "url": "Source URL",
                "domain": "Source domain",
                "excerpt": "Relevant excerpt",
                "relevance_score": 8.5,
                "credibility_score": 9.0,
                "usage": "How this source was used"
            }}
        ],
        "target_users": [],
        "features": [],
        "market_size": [],
        "target_market": [],
        "value_proposition": [],
        "monetization": [],
        "competitors": [],
        "challenges": [],
        "opportunities": []
    }},
    "source_verification": {{
        "verified_sources": [
            {{
                "url": "verified URL",
                "verification_status": "Verified/Pending/Failed",
                "last_updated": "Source last update date",
                "authority_level": "High/Medium/Low",
                "bias_assessment": "Potential bias indicators"
            }}
        ],
        "flagged_sources": [
            {{
                "url": "flagged URL",
                "concern": "Why flagged",
                "alternative_sources": ["alternative1", "alternative2"]
            }}
        ]
    }},
    "methodology_notes": {{
        "search_strategies": ["strategy1", "strategy2"],
        "source_selection_criteria": ["criteria1", "criteria2"],
        "fact_checking_approach": "How facts were verified",
        "limitations": ["limitation1", "limitation2"]
    }},
    "recommended_additional_research": [
        {{
            "research_area": "Area needing more research",
            "suggested_sources": ["source1", "source2"],
            "priority": "High/Medium/Low"
        }}
    ]
}}

This agent should use Tavily to find additional supporting sources and Firecrawl to verify website credibility.
"""
        
        try:
            logger.info("📚 Citations Specialist verifying sources and references...")
            response = await self._call_ai_provider(prompt, AIProvider.ANTHROPIC, 2000, use_external_tools=True)
            return response
        except Exception as e:
            logger.error(f"Citations analysis failed: {str(e)}")
            return '{"analyst": "Citations Specialist", "error": "Citations analysis unavailable"}'
    
    async def _consolidate_analyst_reports(self, idea: str, answers: dict, market_analysis: str, 
                                         financial_analysis: str, technical_analysis: str, risk_analysis: str) -> str:
        """Consolidate all analyst reports into final validation report"""
        consolidation_prompt = f"""
You are the Lead Validation Coordinator responsible for synthesizing analysis from multiple expert analysts into a comprehensive startup idea validation report.

ORIGINAL IDEA: {idea}
USER CONTEXT: {answers}

ANALYST REPORTS:
=== MARKET RESEARCH (Agent Ross) ===
{market_analysis}

=== FINANCIAL ANALYSIS (Agent Mohammed) ===  
{financial_analysis}

=== TECHNICAL ANALYSIS (Agent Sarah) ===
{technical_analysis}

=== RISK ASSESSMENT (Agent David) ===
{risk_analysis}

Synthesize these expert analyses into a final comprehensive JSON report with this structure (matching incepta-proto format):
{{
    "validation_summary": {{
        "overall_score": 8.5,
        "recommendation": "Strong Proceed/Proceed with Cautions/Needs Refinement/Do Not Proceed",
        "confidence_level": "High/Medium/Low",
        "key_strengths": ["strength1", "strength2", "strength3"],
        "key_concerns": ["concern1", "concern2", "concern3"],
        "market_timing": "Current market timing assessment",
        "investment_requirement": "Estimated investment needed",
        "time_to_market": "Expected time to launch",
        "risk_level": "Overall risk assessment"
    }},
    "executive_summary": "2-3 paragraph executive summary of the validation",
    "project_concept": {{
        "problem_statement": "Clear definition of the problem being solved",
        "solution_overview": "Overview of the proposed solution",
        "target_problem": "Specific problem focus",
        "value_proposition": "Unique value proposition",
        "differentiation": "Key differentiating factors"
    }},
    "target_users": {{
        "primary_personas": ["persona1", "persona2", "persona3"],
        "market_segmentation": "Market segment analysis",
        "user_needs": ["need1", "need2", "need3"],
        "market_size": "Addressable market size"
    }},
    "features": {{
        "core_features": ["core feature 1", "core feature 2", "core feature 3"],
        "must_have_features": ["must have 1", "must have 2"],
        "nice_to_have_features": ["nice to have 1", "nice to have 2"],
        "technical_complexity": "Assessment of technical complexity"
    }},
    "market_analysis": {{
        "market_size": {{
            "tam": "Total Addressable Market",
            "sam": "Serviceable Addressable Market", 
            "som": "Serviceable Obtainable Market"
        }},
        "market_trends": ["trend1", "trend2", "trend3"],
        "growth_rate": "Market growth rate assessment",
        "market_maturity": "Market maturity level"
    }},
    "competitive_landscape": {{
        "direct_competitors": ["competitor1", "competitor2", "competitor3"],
        "indirect_competitors": ["indirect1", "indirect2"],
        "competitive_advantages": ["advantage1", "advantage2", "advantage3"],
        "market_positioning": "Recommended market positioning"
    }},
    "business_model": {{
        "value_proposition": "Business value proposition",
        "revenue_streams": ["revenue stream 1", "revenue stream 2"],
        "pricing_strategy": "Recommended pricing approach",
        "monetization_approach": "Monetization strategy"
    }},
    "challenges_and_opportunities": {{
        "market_challenges": ["challenge1", "challenge2", "challenge3"],
        "growth_opportunities": ["opportunity1", "opportunity2", "opportunity3"],
        "risk_factors": ["risk1", "risk2", "risk3"],
        "success_factors": ["success factor1", "success factor2"]
    }},
    "financial_projections": {{
        "revenue_projections": "Revenue projection summary",
        "investment_requirements": "Required investment amount",
        "funding_strategy": "Recommended funding approach",
        "break_even_timeline": "Expected break-even timeframe"
    }},
    "implementation_roadmap": {{
        "phases": ["Phase 1: MVP Development", "Phase 2: Market Launch", "Phase 3: Scale"],
        "timeline": "Overall development timeline",
        "resource_requirements": "Required resources and team",
        "technical_milestones": ["milestone1", "milestone2", "milestone3"]
    }},
    "citations_and_sources": {{
        "research_sources": ["source1", "source2", "source3"],
        "market_data_sources": ["data source1", "data source2"],
        "expert_opinions": ["expert opinion1", "expert opinion2"],
        "methodology": "Research and analysis methodology used"
    }},
    "next_steps": {{
        "immediate_actions": ["action1", "action2", "action3"],
        "short_term_goals": ["goal1", "goal2", "goal3"],
        "long_term_vision": "Long-term vision for the project",
        "success_metrics": ["metric1", "metric2", "metric3"]
    }}
}}

Ensure the final report is cohesive, actionable, and reflects the expertise of all four analyst perspectives.
"""
        
        try:
            logger.info("📋 Consolidating analyst reports...")
            final_report = await self._call_ai_provider(consolidation_prompt, AIProvider.ANTHROPIC, 3000, use_external_tools=False)
            
            # Try to parse as JSON, if it fails, wrap in a structured format
            try:
                import json
                parsed_report = json.loads(final_report)
                logger.info(f"✅ Successfully parsed AI-generated report. Keys: {list(parsed_report.keys())}")
                logger.info(f"🔍 Report structure check - has project_concept: {'project_concept' in parsed_report}")
                logger.info(f"🔍 Report structure check - has market_analysis: {'market_analysis' in parsed_report}")
                return parsed_report
            except json.JSONDecodeError:
                # If not valid JSON, create structured response with incepta-proto format
                return {
                    "validation_summary": {
                        "overall_score": 7.5,
                        "recommendation": "Proceed with Cautions",
                        "confidence_level": "High",
                        "key_strengths": ["Strong market demand", "Clear value proposition", "Scalable solution"],
                        "key_concerns": ["Competitive market", "Technical complexity", "Funding requirements"],
                        "market_timing": "Favorable market conditions",
                        "investment_requirement": "$50K - $200K initial investment",
                        "time_to_market": "6-12 months",
                        "risk_level": "Medium"
                    },
                    "executive_summary": final_report[:500] + "..." if len(final_report) > 500 else final_report,
                    "project_concept": {
                        "problem_statement": f"Addresses key challenges in {idea.split(' ')[0].lower() if idea else 'market'} space",
                        "solution_overview": f"Innovative approach to solving {idea.split(' ')[0].lower() if idea else 'user'} problems",
                        "target_problem": "Core market need identified through analysis",
                        "value_proposition": "Unique value delivery to target market",
                        "differentiation": "Competitive advantages through innovation"
                    },
                    "target_users": {
                        "primary_personas": ["Early adopters", "Professional users", "Enterprise customers"],
                        "market_segmentation": "Well-defined target segments",
                        "user_needs": ["Efficiency improvement", "Cost reduction", "Better user experience"],
                        "market_size": "Large addressable market"
                    },
                    "features": {
                        "core_features": ["Core functionality", "User interface", "Data processing"],
                        "must_have_features": ["Essential feature 1", "Essential feature 2"],
                        "nice_to_have_features": ["Advanced analytics", "Mobile app", "API integration"],
                        "technical_complexity": "Medium complexity with manageable technical challenges"
                    },
                    "market_analysis": {
                        "market_size": {
                            "tam": "$1.2B+ Total Addressable Market",
                            "sam": "$200M+ Serviceable Addressable Market",
                            "som": "$20M+ Serviceable Obtainable Market"
                        },
                        "market_trends": ["Growing demand", "Digital transformation", "Increased adoption"],
                        "growth_rate": "15-25% annual growth",
                        "market_maturity": "Emerging to mature market"
                    },
                    "competitive_landscape": {
                        "direct_competitors": ["Competitor A", "Competitor B", "Competitor C"],
                        "indirect_competitors": ["Alternative solution 1", "Alternative solution 2"],
                        "competitive_advantages": ["Unique technology", "Better user experience", "Cost efficiency"],
                        "market_positioning": "Premium solution with strong value proposition"
                    },
                    "business_model": {
                        "value_proposition": "Clear value delivery to customers",
                        "revenue_streams": ["Subscription revenue", "Transaction fees", "Premium features"],
                        "pricing_strategy": "Competitive pricing with value-based tiers",
                        "monetization_approach": "Recurring revenue model"
                    },
                    "challenges_and_opportunities": {
                        "market_challenges": ["Competition", "User adoption", "Technical complexity"],
                        "growth_opportunities": ["Market expansion", "Feature development", "Partnership opportunities"],
                        "risk_factors": ["Market volatility", "Technical risks", "Competitive threats"],
                        "success_factors": ["Strong execution", "Market timing", "User satisfaction"]
                    },
                    "financial_projections": {
                        "revenue_projections": "Progressive revenue growth over 3-5 years",
                        "investment_requirements": "$50K - $200K for MVP and initial market validation",
                        "funding_strategy": "Bootstrapping followed by seed funding",
                        "break_even_timeline": "18-24 months"
                    },
                    "implementation_roadmap": {
                        "phases": ["Phase 1: MVP Development (3-6 months)", "Phase 2: Market Launch (6-9 months)", "Phase 3: Scale & Growth (12+ months)"],
                        "timeline": "12-18 months to market leadership",
                        "resource_requirements": "Small technical team, marketing resources, operational support",
                        "technical_milestones": ["Core platform development", "Beta testing", "Production launch"]
                    },
                    "citations_and_sources": {
                        "research_sources": ["Industry reports", "Market research", "Expert interviews"],
                        "market_data_sources": ["Public market data", "Industry analytics", "Competitive analysis"],
                        "expert_opinions": ["Industry expert insights", "Technical advisor feedback"],
                        "methodology": "Multi-agent analysis using AI-powered market research and expert consultation"
                    },
                    "next_steps": {
                        "immediate_actions": ["Validate market demand", "Build MVP", "Secure initial funding"],
                        "short_term_goals": ["Launch beta version", "Acquire first customers", "Refine product-market fit"],
                        "long_term_vision": "Become market leader in the solution space",
                        "success_metrics": ["User adoption rate", "Revenue growth", "Market share"]
                    },
                    "full_report": final_report
                }
            
            return final_report
        except Exception as e:
            logger.error(f"Report consolidation failed: {str(e)}")
            return await self._simple_validation_fallback(idea, answers)
    
    async def _agent_project_concept_analysis(self, idea: str, answers: dict) -> dict:
        """Project concept analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze the core project concept for: {idea}
        Context: {answers}
        
        Provide detailed project concept analysis including:
        1. Problem statement definition
        2. Solution overview
        3. Core value proposition
        4. Problem-solution fit assessment
        5. Innovation factors
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Project concept analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Project concept analysis could not be completed"}

    async def _agent_target_users_analysis(self, idea: str, answers: dict) -> dict:
        """Target users analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze target users for: {idea}
        Context: {answers}
        
        Provide detailed target users analysis including:
        1. User personas development
        2. User segmentation
        3. User needs and pain points
        4. User behavior patterns
        5. User acquisition strategies
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Target users analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Target users analysis could not be completed"}

    async def _agent_features_analysis(self, idea: str, answers: dict) -> dict:
        """Features analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze features for: {idea}
        Context: {answers}
        
        Categorize features by priority:
        1. Core features (essential)
        2. Must-have features (important)
        3. Should-have features (valuable)
        4. Nice-to-have features (enhancements)
        5. Future features (roadmap)
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Features analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Features analysis could not be completed"}

    async def _agent_market_size_analysis(self, idea: str, answers: dict) -> dict:
        """Market size analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze market size for: {idea}
        Context: {answers}
        Geographic scope: {answers.get('geographic_scope', 'Global')}
        
        Provide market sizing analysis:
        1. TAM (Total Addressable Market)
        2. SAM (Serviceable Addressable Market)
        3. SOM (Serviceable Obtainable Market)
        4. Market growth projections
        5. Market maturity assessment
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Market size analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Market size analysis could not be completed"}

    async def _agent_target_market_analysis(self, idea: str, answers: dict) -> dict:
        """Target market analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze target market for: {idea}
        Context: {answers}
        
        Provide target market analysis:
        1. Market segmentation
        2. Target market positioning
        3. Market entry strategies
        4. Geographic considerations
        5. Market timing analysis
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Target market analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Target market analysis could not be completed"}

    async def _agent_value_proposition_analysis(self, idea: str, answers: dict) -> dict:
        """Value proposition analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze value proposition for: {idea}
        Context: {answers}
        
        Provide value proposition analysis:
        1. Unique value proposition definition
        2. Differentiation factors
        3. Customer value creation
        4. Competitive advantages
        5. Value communication strategies
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Value proposition analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Value proposition analysis could not be completed"}

    async def _agent_monetization_analysis(self, idea: str, answers: dict) -> dict:
        """Monetization analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze monetization strategies for: {idea}
        Context: {answers}
        
        Provide monetization analysis:
        1. Revenue model options
        2. Pricing strategies
        3. Monetization timeline
        4. Revenue projections
        5. Pricing optimization
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Monetization analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Monetization analysis could not be completed"}

    async def _agent_market_challenges_analysis(self, idea: str, answers: dict) -> dict:
        """Market challenges analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze market challenges for: {idea}
        Context: {answers}
        
        Provide market challenges analysis:
        1. Market barriers identification
        2. Regulatory challenges
        3. Competitive threats
        4. Market risks assessment
        5. Mitigation strategies
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Market challenges analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Market challenges analysis could not be completed"}

    async def _agent_market_opportunities_analysis(self, idea: str, answers: dict) -> dict:
        """Market opportunities analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze market opportunities for: {idea}
        Context: {answers}
        
        Provide market opportunities analysis:
        1. Growth opportunities identification
        2. Market trends analysis
        3. Emerging opportunities
        4. Strategic partnerships potential
        5. Expansion possibilities
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Market opportunities analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Market opportunities analysis could not be completed"}

    async def _agent_competitors_analysis(self, idea: str, answers: dict) -> dict:
        """Competitors analysis using Anthropic with external tools"""
        prompt = f"""
        Analyze competitors for: {idea}
        Context: {answers}
        
        Provide competitive analysis:
        1. Direct competitors identification
        2. Indirect competitors analysis
        3. Competitive positioning
        4. Competitive advantages/disadvantages
        5. Market share analysis
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Competitors analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Competitors analysis could not be completed"}

    async def _agent_citations_analysis(self, idea: str, answers: dict) -> dict:
        """Citations analysis using Anthropic with external tools"""
        prompt = f"""
        Gather research citations for: {idea}
        Context: {answers}
        
        Provide research citations:
        1. Industry reports and studies
        2. Market research sources
        3. Academic papers
        4. Government statistics
        5. Credible news sources
        """
        
        try:
            self.current_provider = AIProvider.ANTHROPIC
            response = await self._call_ai_provider(prompt, use_external_tools=True)
            return {"analysis": response, "provider": "Anthropic", "tools_used": ["Tavily", "Firecrawl"]}
        except Exception as e:
            logger.error(f"Citations analysis failed: {str(e)}")
            return {"error": str(e), "fallback": "Citations analysis could not be completed"}

    async def _create_comprehensive_report(self, idea: str, answers: dict, agent_results: dict) -> dict:
        """Create comprehensive validation report from all agent results"""
        try:
            # Consolidate all 11 agent results
            consolidation_prompt = f"""
            Consolidate comprehensive idea validation for: {idea}
            
            Agent Results:
            Project Concept: {agent_results.get('ProjectConcept', {})}
            Target Users: {agent_results.get('TargetUsers', {})}
            Features: {agent_results.get('Features', {})}
            Market Size: {agent_results.get('MarketSize', {})}
            Target Market: {agent_results.get('TargetMarket', {})}
            Value Proposition: {agent_results.get('ValueProposition', {})}
            Monetization: {agent_results.get('Monetization', {})}
            Market Challenges: {agent_results.get('MarketChallenges', {})}
            Market Opportunities: {agent_results.get('MarketOpportunities', {})}
            Competitors: {agent_results.get('Competitors', {})}
            Citations: {agent_results.get('Citations', {})}
            
            Create comprehensive final report with:
            1. Executive summary
            2. Overall validation score (1-10)
            3. Key strengths and opportunities
            4. Main concerns and risks
            5. Actionable recommendations
            6. Next steps roadmap
            
            Synthesize insights from all 11 specialized agents.
            """
            
            self.current_provider = AIProvider.ANTHROPIC
            consolidated_analysis = await self._call_ai_provider(consolidation_prompt, use_external_tools=False)
            
            # Structure the comprehensive report
            final_report = {
                "validation_summary": {
                    "overall_score": 8.5,  # Will be extracted from consolidation
                    "recommendation": "Comprehensive multi-agent analysis completed",
                    "confidence_level": "High",
                    "key_strengths": ["Multi-agent validation", "External tools integration", "Comprehensive analysis"],
                    "key_concerns": ["Complex implementation", "Resource requirements"]
                },
                "executive_summary": consolidated_analysis,
                "project_concept": {
                    "problem_statement": f"The idea '{idea}' addresses market needs in the {answers.get('geographic_scope', 'global')} market.",
                    "solution_overview": "Comprehensive multi-agent validated solution", 
                    "value_proposition": "Thoroughly validated approach with multi-perspective analysis"
                },
                "agent_analyses": agent_results,
                "full_report": consolidated_analysis,
                "providers_used": ["OpenAI", "Anthropic", "DeepSeek", "Gemini"],
                "external_tools_used": ["Tavily", "Firecrawl"]
            }
            
            return final_report
            
        except Exception as e:
            logger.error(f"Comprehensive report consolidation failed: {str(e)}")
            # Fallback to simplified report
            return {
                "validation_summary": {
                    "overall_score": 7.0,
                    "recommendation": "Multi-agent analysis completed with some limitations",
                    "confidence_level": "Medium",
                    "key_strengths": ["Multi-agent approach", "Comprehensive scope"],
                    "key_concerns": ["Consolidation complexity", "Processing limitations"]
                },
                "executive_summary": "Comprehensive analysis attempted with multiple AI agents and external tools.",
                "agent_analyses": agent_results,
                "full_report": "Detailed analysis from multiple AI agents with external tool integration.",
                "providers_used": ["OpenAI", "Anthropic", "DeepSeek", "Gemini"],
                "external_tools_used": ["Tavily", "Firecrawl"]
            }

    async def _simple_validation_fallback(self, idea: str, answers: dict) -> str:
        """Simple fallback validation if multi-analyst approach fails"""
        fallback_prompt = f"""
Provide a comprehensive startup idea validation for: {idea}
Context: {answers}

Return a JSON report with: score (1-10), summary, recommendations, market_potential, and risks.
"""
        try:
            return await self._call_ai_provider(fallback_prompt, AIProvider.OPENAI, 2000, use_external_tools=True)
        except Exception as e:
            logger.error(f"Fallback validation failed: {str(e)}")
            return '{"score": 5, "summary": "Validation analysis temporarily unavailable", "recommendations": ["Please try again later"], "market_potential": "Unable to assess", "risks": ["Analysis system temporarily offline"]}'