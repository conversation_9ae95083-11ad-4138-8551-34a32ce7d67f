"""
AI Provider Manager for handling multiple AI providers
"""

from typing import Dict, List, Optional
import logging
from app.core.config import settings

from . import AIProvider
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .deepseek_provider import DeepseekProvider
from .gemini_provider import GeminiProvider
from .perplexity_provider import PerplexityProvider

logger = logging.getLogger(__name__)


class AIProviderManager:
    """Manager for multiple AI providers"""
    
    def __init__(self):
        self.providers = {}
        self._setup_providers()
    
    def _setup_providers(self) -> None:
        """Setup all available AI providers"""
        # OpenAI
        self.providers[AIProvider.OPENAI] = OpenAIProvider(settings.openai_api_key)
        
        # Anthropic
        self.providers[AIProvider.ANTHROPIC] = AnthropicProvider(settings.anthropic_api_key)
        
        # Deepseek
        self.providers[AIProvider.DEEPSEEK] = DeepseekProvider(settings.deepseek_api_key)
        
        # Gemini
        self.providers[AIProvider.GEMINI] = GeminiProvider(settings.gemini_api_key)
        
        # Perplexity
        self.providers[AIProvider.PERPLEXITY] = PerplexityProvider(settings.perplexity_api_key)
    
    def get_provider(self, provider: AIProvider):
        """Get a specific provider"""
        return self.providers.get(provider)
    
    def get_available_providers(self) -> List[AIProvider]:
        """Get list of available providers based on configured API keys"""
        available = []
        for provider_type, provider in self.providers.items():
            if provider.is_available():
                available.append(provider_type)
        
        # Add mixture-of-agents if at least 2 providers and anthropic available
        if (len(available) >= 2 and 
            AIProvider.ANTHROPIC in available):
            available.append(AIProvider.MIXTURE_OF_AGENTS)
        
        return available
    
    async def generate_response(
        self, 
        provider: AIProvider, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate response using specified provider"""
        if provider not in self.providers:
            raise ValueError(f"Provider {provider} not supported")
        
        provider_instance = self.providers[provider]
        if not provider_instance.is_available():
            raise Exception(f"Provider {provider} not available")
        
        return await provider_instance.generate_response(
            prompt, max_tokens, temperature, **kwargs
        )
    
    def is_provider_available(self, provider: AIProvider) -> bool:
        """Check if a specific provider is available"""
        if provider not in self.providers:
            return False
        return self.providers[provider].is_available()
