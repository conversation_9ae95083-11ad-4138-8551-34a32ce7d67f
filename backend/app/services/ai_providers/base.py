"""
Base AI provider interface and common functionality
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class BaseAIProvider(ABC):
    """Abstract base class for AI providers"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.client = None
        self._setup_client()
    
    @abstractmethod
    def _setup_client(self) -> None:
        """Setup the AI provider client"""
        pass
    
    @abstractmethod
    async def generate_response(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate response from the AI provider"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is available and configured"""
        pass
    
    def get_provider_name(self) -> str:
        """Get the provider name"""
        return self.__class__.__name__.replace("Provider", "").lower()
    
    async def generate_with_fallback(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate response with error handling and fallback"""
        try:
            if not self.is_available():
                raise Exception(f"{self.get_provider_name()} provider not available")
            
            return await self.generate_response(
                prompt, max_tokens, temperature, **kwargs
            )
        except Exception as e:
            logger.error(f"Error with {self.get_provider_name()} provider: {str(e)}")
            raise
