"""
Gemini provider implementation
"""

import google.generativeai as genai
from typing import Optional
import logging
from .base import BaseAIProvider

logger = logging.getLogger(__name__)


class GeminiProvider(BaseAIProvider):
    """Gemini provider implementation"""
    
    def _setup_client(self) -> None:
        """Setup Gemini client"""
        if self.api_key and self.api_key != "your-gemini-api-key":
            genai.configure(api_key=self.api_key)
            self.client = genai.GenerativeModel('gemini-1.5-pro')
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Gemini is available"""
        return self.client is not None
    
    async def generate_response(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        **kwargs
    ) -> str:
        """Generate response using Gemini"""
        if not self.client:
            raise Exception("Gemini client not initialized")
        
        try:
            # Configure generation parameters
            generation_config = genai.types.GenerationConfig(
                max_output_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            
            response = self.client.generate_content(
                prompt,
                generation_config=generation_config
            )
            return response.text
        except Exception as e:
            logger.error(f"Gemini API error: {str(e)}")
            raise
