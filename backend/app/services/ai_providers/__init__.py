"""
AI Providers module for multi-provider AI integration
"""

from enum import Enum


class AIProvider(str, Enum):
    """Enumeration of supported AI providers"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    DEEPSEEK = "deepseek"
    GEMINI = "gemini"
    PERPLEXITY = "perplexity"
    MIXTURE_OF_AGENTS = "mixture-of-agents"


from .manager import AIProviderManager
from .base import BaseAIProvider
from .openai_provider import OpenAIProvider
from .anthropic_provider import AnthropicProvider
from .deepseek_provider import DeepseekProvider
from .gemini_provider import GeminiProvider
from .perplexity_provider import PerplexityProvider

__all__ = [
    "AIProvider",
    "AIProviderManager",
    "BaseAIProvider",
    "OpenAIProvider",
    "AnthropicProvider",
    "DeepseekProvider",
    "GeminiProvider",
    "PerplexityProvider"
]
