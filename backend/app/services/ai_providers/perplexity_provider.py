"""
Perplexity provider implementation
"""

import openai
from typing import Optional
import logging
from .base import BaseAIProvider

logger = logging.getLogger(__name__)


class PerplexityProvider(BaseAIProvider):
    """Perplexity provider implementation using OpenAI-compatible API"""
    
    def _setup_client(self) -> None:
        """Setup Perplexity client"""
        if self.api_key and self.api_key != "your-perplexity-api-key":
            self.client = openai.OpenAI(
                api_key=self.api_key,
                base_url="https://api.perplexity.ai"
            )
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Perplexity is available"""
        return self.client is not None
    
    async def generate_response(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        model: str = "llama-3.1-sonar-large-128k-online",
        **kwargs
    ) -> str:
        """Generate response using Perplexity"""
        if not self.client:
            raise Exception("Perplexity client not initialized")
        
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Perplexity API error: {str(e)}")
            raise
