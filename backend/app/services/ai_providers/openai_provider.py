"""
OpenAI provider implementation
"""

import openai
from typing import Optional
import logging
from .base import BaseAIProvider

logger = logging.getLogger(__name__)


class OpenAIProvider(BaseAIProvider):
    """OpenAI provider implementation"""
    
    def _setup_client(self) -> None:
        """Setup OpenAI client"""
        if self.api_key and self.api_key != "your-openai-api-key":
            self.client = openai.OpenAI(api_key=self.api_key)
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if OpenAI is available"""
        return self.client is not None
    
    async def generate_response(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        model: str = "gpt-4o",
        **kwargs
    ) -> str:
        """Generate response using OpenAI"""
        if not self.client:
            raise Exception("OpenAI client not initialized")
        
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=max_tokens,
                temperature=temperature,
                **kwargs
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API error: {str(e)}")
            raise
