"""
Anthropic provider implementation
"""

import anthropic
from typing import Optional
import logging
from .base import BaseAIProvider

logger = logging.getLogger(__name__)


class AnthropicProvider(BaseAIProvider):
    """Anthropic provider implementation"""
    
    def _setup_client(self) -> None:
        """Setup Anthropic client"""
        if self.api_key and self.api_key != "your-anthropic-api-key":
            self.client = anthropic.Anthropic(api_key=self.api_key)
        else:
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Anthropic is available"""
        return self.client is not None
    
    async def generate_response(
        self, 
        prompt: str, 
        max_tokens: int = 1000,
        temperature: float = 0.7,
        model: str = "claude-3-5-sonnet-20241022",
        **kwargs
    ) -> str:
        """Generate response using Anthropic"""
        if not self.client:
            raise Exception("Anthropic client not initialized")
        
        try:
            response = self.client.messages.create(
                model=model,
                max_tokens=max_tokens,
                temperature=temperature,
                messages=[{"role": "user", "content": prompt}],
                **kwargs
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Anthropic API error: {str(e)}")
            raise
