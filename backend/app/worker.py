"""
Celery worker for background task processing
"""

from celery import Celery
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

# Create Celery instance
celery_app = Celery(
    "incepta_worker",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=['app.tasks.validation_tasks']
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_routes={
        'app.tasks.validation_tasks.ai_analysis_task': {'queue': 'ai_analysis'},
        'app.tasks.validation_tasks.external_tool_task': {'queue': 'external_tools'},
        'app.tasks.validation_tasks.consolidation_task': {'queue': 'consolidation'},
    },
    worker_concurrency=4,  # Number of worker processes
    task_acks_late=True,
    worker_prefetch_multiplier=1,
)

if __name__ == '__main__':
    celery_app.start()