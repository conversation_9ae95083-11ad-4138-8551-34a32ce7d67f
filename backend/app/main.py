from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.v1.api import api_router
import asyncio
import logging
import signal
import sys
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

# Global task tracking for graceful shutdown
running_tasks = set()

def add_background_task(task):
    """Add a task to the global tracking set"""
    running_tasks.add(task)
    task.add_done_callback(running_tasks.discard)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with graceful shutdown"""
    logger.info("🚀 Starting Incepta API server...")

    # Startup
    yield

    # Shutdown
    logger.info("🛑 Shutting down Incepta API server...")

    # Cancel all running background tasks
    if running_tasks:
        logger.info(f"📋 Cancelling {len(running_tasks)} running tasks...")
        for task in running_tasks.copy():
            if not task.done():
                task.cancel()

        # Wait for tasks to complete with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*running_tasks, return_exceptions=True),
                timeout=10.0  # 10 second timeout
            )
            logger.info("✅ All tasks cancelled successfully")
        except asyncio.TimeoutError:
            logger.warning("⚠️ Some tasks did not complete within timeout")

    logger.info("✅ Shutdown complete")

def create_application() -> FastAPI:
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        debug=settings.debug,
        openapi_url="/api/v1/openapi.json",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        lifespan=lifespan,
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API router
    app.include_router(api_router, prefix="/api/v1")

    @app.get("/")
    async def root():
        return {"message": f"Welcome to {settings.app_name} API"}

    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "version": settings.app_version}

    return app


app = create_application()