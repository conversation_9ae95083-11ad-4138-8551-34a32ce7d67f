from typing import Optional
from pydantic import BaseModel
from datetime import datetime
from app.models.project_planning import PlanningStatus


class ProjectPlanningBase(BaseModel):
    project_name: str
    description: Optional[str] = None
    platforms: Optional[dict] = None
    technology_stack: Optional[dict] = None
    development_phases: Optional[dict] = None
    timeline: Optional[dict] = None
    budget_range: Optional[dict] = None
    branding: Optional[dict] = None
    user_projections: Optional[dict] = None


class ProjectPlanningCreate(ProjectPlanningBase):
    idea_validation_id: Optional[int] = None


class ProjectPlanningUpdate(BaseModel):
    project_name: Optional[str] = None
    description: Optional[str] = None
    platforms: Optional[dict] = None
    technology_stack: Optional[dict] = None
    development_phases: Optional[dict] = None
    timeline: Optional[dict] = None
    budget_range: Optional[dict] = None
    branding: Optional[dict] = None
    user_projections: Optional[dict] = None
    completion_percentage: Optional[int] = None


class ProjectPlanningResponse(ProjectPlanningBase):
    id: int
    user_id: int
    idea_validation_id: Optional[int] = None
    status: PlanningStatus
    completion_percentage: int = 0
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True