from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from decimal import Decimal
from app.models.roadmap import RoadmapStatus


class RoadmapBase(BaseModel):
    title: str
    description: Optional[str] = None


class RoadmapCreate(RoadmapBase):
    project_planning_id: Optional[int] = None
    ai_provider: Optional[str] = None


class RoadmapUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    phases: Optional[dict] = None
    team_composition: Optional[dict] = None
    cost_breakdown: Optional[dict] = None
    timeline_estimates: Optional[dict] = None
    risk_assessments: Optional[dict] = None
    milestones: Optional[dict] = None
    hourly_rates: Optional[dict] = None


class RoadmapResponse(RoadmapBase):
    id: int
    user_id: int
    project_planning_id: Optional[int] = None
    phases: Optional[dict] = None
    team_composition: Optional[dict] = None
    cost_breakdown: Optional[dict] = None
    timeline_estimates: Optional[dict] = None
    risk_assessments: Optional[dict] = None
    milestones: Optional[dict] = None
    total_estimated_cost: Optional[Decimal] = None
    hourly_rates: Optional[dict] = None
    status: RoadmapStatus
    ai_provider: Optional[str] = None
    generation_prompt: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class RoadmapTaskResponse(BaseModel):
    task_id: str
    status: str
    progress: Optional[dict] = None
    result: Optional[dict] = None
    error: Optional[str] = None


class RoadmapListResponse(BaseModel):
    roadmaps: List[RoadmapResponse]
    total: int
    skip: int
    limit: int