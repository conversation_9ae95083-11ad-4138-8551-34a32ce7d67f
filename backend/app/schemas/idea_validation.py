from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime
from app.models.idea_validation import ValidationStatus, ValidationType


class IdeaValidationBase(BaseModel):
    title: str
    description: str
    validation_type: ValidationType = ValidationType.STANDARD


class IdeaValidationCreate(IdeaValidationBase):
    ai_provider: Optional[str] = None


class IdeaValidationUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    validation_type: Optional[ValidationType] = None


class IdeaValidationResponse(IdeaValidationBase):
    id: int
    user_id: int
    status: ValidationStatus
    project_concept: Optional[dict] = None
    user_roles: Optional[dict] = None
    features: Optional[dict] = None
    target_users: Optional[dict] = None
    market_analysis: Optional[dict] = None
    ai_provider: Optional[str] = None
    processing_time: Optional[int] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class ValidationStatusResponse(BaseModel):
    id: int
    status: ValidationStatus
    progress: Optional[dict] = None
    error_message: Optional[str] = None


class MarketResearchResponse(BaseModel):
    id: int
    query: str
    results: Optional[dict] = None
    summary: Optional[str] = None
    key_insights: Optional[dict] = None
    search_provider: Optional[str] = None
    results_count: int = 0
    searched_at: datetime
    
    class Config:
        from_attributes = True


class CompetitorAnalysisResponse(BaseModel):
    id: int
    company_name: str
    website_url: Optional[str] = None
    description: Optional[str] = None
    features: Optional[dict] = None
    pricing: Optional[dict] = None
    strengths: Optional[dict] = None
    weaknesses: Optional[dict] = None
    scraping_success: bool = True
    scraped_at: datetime
    
    class Config:
        from_attributes = True


class IdeaValidationListResponse(BaseModel):
    validations: List[IdeaValidationResponse]
    total: int
    skip: int
    limit: int