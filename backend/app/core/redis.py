import redis
import time
from app.core.config import settings

# Redis connection
redis_client = redis.from_url(
    settings.redis_url,
    encoding="utf-8",
    decode_responses=True,
    socket_connect_timeout=5,
    socket_timeout=5,
)


def get_redis():
    """Get Redis client instance"""
    return redis_client


# Session management
class RedisSessionManager:
    def __init__(self):
        self.redis = redis_client
        self.session_prefix = "session:"
        self.user_session_prefix = "user_sessions:"
    
    async def create_session(self, session_id: str, user_id: int, expire_seconds: int = 3600):
        """Create a new session"""
        session_key = f"{self.session_prefix}{session_id}"
        user_sessions_key = f"{self.user_session_prefix}{user_id}"
        
        # Store session data
        await self.redis.hset(session_key, mapping={
            "user_id": user_id,
            "created_at": str(int(time.time())),
        })
        await self.redis.expire(session_key, expire_seconds)
        
        # Add to user's active sessions
        await self.redis.sadd(user_sessions_key, session_id)
        await self.redis.expire(user_sessions_key, expire_seconds)
    
    async def get_session(self, session_id: str):
        """Get session data"""
        session_key = f"{self.session_prefix}{session_id}"
        return await self.redis.hgetall(session_key)
    
    async def delete_session(self, session_id: str, user_id: int = None):
        """Delete a session"""
        session_key = f"{self.session_prefix}{session_id}"
        
        if user_id:
            user_sessions_key = f"{self.user_session_prefix}{user_id}"
            await self.redis.srem(user_sessions_key, session_id)
        
        await self.redis.delete(session_key)
    
    async def delete_user_sessions(self, user_id: int):
        """Delete all sessions for a user"""
        user_sessions_key = f"{self.user_session_prefix}{user_id}"
        session_ids = await self.redis.smembers(user_sessions_key)
        
        for session_id in session_ids:
            session_key = f"{self.session_prefix}{session_id}"
            await self.redis.delete(session_key)
        
        await self.redis.delete(user_sessions_key)


session_manager = RedisSessionManager()