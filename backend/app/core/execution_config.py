"""
Execution configuration for seamless validation processing
"""

from typing import Dict, List
from enum import Enum
from app.services.ai_providers import AIProvider


class ExecutionStrategy(Enum):
    """Execution strategies for different scenarios"""
    SPEED_OPTIMIZED = "speed"      # Fastest execution, higher cost
    BALANCED = "balanced"          # Balance of speed and cost
    COST_OPTIMIZED = "cost"        # Lowest cost, slower execution
    RELIABILITY_FIRST = "reliable" # Maximum reliability, retries


class ExecutionConfig:
    """Configuration for seamless execution"""
    
    # Provider preferences by strategy
    PROVIDER_PREFERENCES = {
        ExecutionStrategy.SPEED_OPTIMIZED: [
            AIProvider.ANTHROPIC,    # Fastest, most reliable
            AIProvider.OPENAI,       # Fast, reliable
            AIProvider.DEEPSEEK,     # Good speed
            AIProvider.GEMINI,       # Backup
            AIProvider.PERPLEXITY    # Last resort
        ],
        ExecutionStrategy.BALANCED: [
            AIProvider.OPENAI,       # Good balance
            AIProvider.ANTHROPIC,    # High quality
            AIProvider.DEEPSEEK,     # Cost effective
            AIProvider.GEMINI,       # Backup
            AIProvider.PERPLEXITY    # Last resort
        ],
        ExecutionStrategy.COST_OPTIMIZED: [
            AIProvider.DEEPSEEK,     # Cheapest
            AIProvider.GEMINI,       # Low cost
            AIProvider.OPENAI,       # Moderate cost
            AIProvider.ANTHROPIC,    # Higher cost
            AIProvider.PERPLEXITY    # Backup
        ],
        ExecutionStrategy.RELIABILITY_FIRST: [
            AIProvider.ANTHROPIC,    # Most reliable
            AIProvider.OPENAI,       # Very reliable
            AIProvider.DEEPSEEK,     # Reliable
            AIProvider.GEMINI,       # Backup
            AIProvider.PERPLEXITY    # Last resort
        ]
    }
    
    # Timeout configurations by strategy (in seconds)
    TIMEOUT_CONFIGS = {
        ExecutionStrategy.SPEED_OPTIMIZED: {
            "agent_timeout": 90,      # 1.5 minutes per agent
            "phase_timeout": 300,     # 5 minutes per phase
            "total_timeout": 600,     # 10 minutes total
            "retry_attempts": 2
        },
        ExecutionStrategy.BALANCED: {
            "agent_timeout": 120,     # 2 minutes per agent
            "phase_timeout": 420,     # 7 minutes per phase
            "total_timeout": 900,     # 15 minutes total
            "retry_attempts": 3
        },
        ExecutionStrategy.COST_OPTIMIZED: {
            "agent_timeout": 180,     # 3 minutes per agent
            "phase_timeout": 600,     # 10 minutes per phase
            "total_timeout": 1200,    # 20 minutes total
            "retry_attempts": 2
        },
        ExecutionStrategy.RELIABILITY_FIRST: {
            "agent_timeout": 150,     # 2.5 minutes per agent
            "phase_timeout": 480,     # 8 minutes per phase
            "total_timeout": 1200,    # 20 minutes total
            "retry_attempts": 5
        }
    }
    
    # Parallel execution limits by strategy
    PARALLELISM_CONFIGS = {
        ExecutionStrategy.SPEED_OPTIMIZED: {
            "max_concurrent_agents": 5,    # High parallelism
            "provider_rotation": True,     # Use multiple providers
            "aggressive_timeouts": True
        },
        ExecutionStrategy.BALANCED: {
            "max_concurrent_agents": 3,    # Moderate parallelism
            "provider_rotation": True,     # Use multiple providers
            "aggressive_timeouts": False
        },
        ExecutionStrategy.COST_OPTIMIZED: {
            "max_concurrent_agents": 2,    # Lower parallelism
            "provider_rotation": False,    # Stick to cheapest
            "aggressive_timeouts": False
        },
        ExecutionStrategy.RELIABILITY_FIRST: {
            "max_concurrent_agents": 2,    # Conservative parallelism
            "provider_rotation": True,     # Use multiple providers
            "aggressive_timeouts": False
        }
    }
    
    @classmethod
    def get_config(cls, strategy: ExecutionStrategy = ExecutionStrategy.BALANCED) -> Dict:
        """Get complete configuration for a strategy"""
        return {
            "providers": cls.PROVIDER_PREFERENCES[strategy],
            "timeouts": cls.TIMEOUT_CONFIGS[strategy],
            "parallelism": cls.PARALLELISM_CONFIGS[strategy],
            "strategy": strategy
        }
    
    @classmethod
    def get_optimal_strategy(cls, user_priority: str = "balanced") -> ExecutionStrategy:
        """Get optimal strategy based on user preference"""
        mapping = {
            "speed": ExecutionStrategy.SPEED_OPTIMIZED,
            "fast": ExecutionStrategy.SPEED_OPTIMIZED,
            "balanced": ExecutionStrategy.BALANCED,
            "default": ExecutionStrategy.BALANCED,
            "cost": ExecutionStrategy.COST_OPTIMIZED,
            "cheap": ExecutionStrategy.COST_OPTIMIZED,
            "reliable": ExecutionStrategy.RELIABILITY_FIRST,
            "quality": ExecutionStrategy.RELIABILITY_FIRST
        }
        return mapping.get(user_priority.lower(), ExecutionStrategy.BALANCED)


# Default configuration
DEFAULT_EXECUTION_CONFIG = ExecutionConfig.get_config(ExecutionStrategy.BALANCED)
