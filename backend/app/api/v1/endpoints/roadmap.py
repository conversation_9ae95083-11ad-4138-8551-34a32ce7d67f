"""
Roadmap generation endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Any, Dict, List
from app.core.database import get_db
from app.api.deps.auth import get_current_user
from app.models.user import User
from app.models.roadmap import Roadmap, RoadmapStatus
from app.models.project_planning import ProjectPlanning
from app.schemas.roadmap import RoadmapCreate, RoadmapResponse, RoadmapListResponse
from app.services.ai_service import AIService, AIProvider
import logging
import json

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate", response_model=RoadmapResponse)
async def generate_roadmap(
    roadmap_data: RoadmapCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate a development roadmap using AI
    """
    try:
        # Verify project planning exists if provided
        project_planning = None
        if roadmap_data.project_planning_id:
            project_planning = db.query(ProjectPlanning).filter(
                ProjectPlanning.id == roadmap_data.project_planning_id,
                ProjectPlanning.user_id == current_user.id
            ).first()
            
            if not project_planning:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Project planning not found"
                )
        
        # Create roadmap record
        db_roadmap = Roadmap(
            user_id=current_user.id,
            project_planning_id=roadmap_data.project_planning_id,
            title=roadmap_data.title,
            description=roadmap_data.description,
            status=RoadmapStatus.GENERATING
        )
        
        db.add(db_roadmap)
        db.commit()
        db.refresh(db_roadmap)
        
        # Initialize AI service
        ai_service = AIService(
            provider=AIProvider(roadmap_data.ai_provider) if roadmap_data.ai_provider else AIProvider.OPENAI
        )
        
        try:
            # Generate roadmap components
            phases = await ai_service.generate_roadmap_phases(
                roadmap_data.title,
                roadmap_data.description,
                project_planning
            )
            
            team_composition = ai_service.generate_team_composition(phases)
            cost_breakdown = ai_service.generate_cost_breakdown(phases, team_composition)
            timeline_estimates = ai_service.generate_timeline_estimates(phases, team_composition)
            risk_assessments = ai_service.generate_risk_assessments(phases)
            milestones = ai_service.generate_milestones(phases, timeline_estimates)
            
            # Update roadmap with generated data
            db_roadmap.phases = phases
            db_roadmap.team_composition = team_composition
            db_roadmap.cost_breakdown = cost_breakdown
            db_roadmap.timeline_estimates = timeline_estimates
            db_roadmap.risk_assessments = risk_assessments
            db_roadmap.milestones = milestones
            db_roadmap.total_estimated_cost = cost_breakdown.get("total_estimated_cost", 0)
            db_roadmap.hourly_rates = team_composition.get("roles", {})
            db_roadmap.status = RoadmapStatus.COMPLETED
            db_roadmap.ai_provider = str(ai_service.current_provider.value)
            
            db.commit()
            db.refresh(db_roadmap)
            
            logger.info(f"Roadmap generated for user {current_user.id}, roadmap {db_roadmap.id}")
            
        except Exception as ai_error:
            db_roadmap.status = RoadmapStatus.FAILED
            db_roadmap.error_message = str(ai_error)
            db.commit()
            
            logger.error(f"AI roadmap generation failed: {str(ai_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Roadmap generation failed: {str(ai_error)}"
            )
        
        return RoadmapResponse.from_orm(db_roadmap)
        
    except Exception as e:
        logger.error(f"Roadmap generation endpoint error: {str(e)}")
        if 'db_roadmap' in locals():
            db_roadmap.status = RoadmapStatus.FAILED
            db_roadmap.error_message = str(e)
            db.commit()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Roadmap generation failed: {str(e)}"
        )


@router.get("/roadmaps", response_model=RoadmapListResponse)
async def get_user_roadmaps(
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's roadmap history
    """
    roadmaps = db.query(Roadmap).filter(
        Roadmap.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    total = db.query(Roadmap).filter(
        Roadmap.user_id == current_user.id
    ).count()
    
    return RoadmapListResponse(
        roadmaps=[RoadmapResponse.from_orm(r) for r in roadmaps],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/roadmaps/{roadmap_id}", response_model=RoadmapResponse)
async def get_roadmap(
    roadmap_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get specific roadmap by ID
    """
    roadmap = db.query(Roadmap).filter(
        Roadmap.id == roadmap_id,
        Roadmap.user_id == current_user.id
    ).first()
    
    if not roadmap:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Roadmap not found"
        )
    
    return RoadmapResponse.from_orm(roadmap)


@router.put("/roadmaps/{roadmap_id}/recalculate", response_model=RoadmapResponse)
async def recalculate_costs(
    roadmap_id: int,
    hourly_rates: Dict[str, float],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Recalculate roadmap costs with updated hourly rates
    """
    roadmap = db.query(Roadmap).filter(
        Roadmap.id == roadmap_id,
        Roadmap.user_id == current_user.id
    ).first()
    
    if not roadmap:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Roadmap not found"
        )
    
    try:
        ai_service = AIService()
        
        updated_costs = ai_service.recalculate_costs(
            roadmap.phases or [],
            roadmap.team_composition or {},
            hourly_rates
        )
        
        # Update roadmap with new costs
        roadmap.cost_breakdown = updated_costs
        roadmap.total_estimated_cost = updated_costs.get("updated_total_cost", 0)
        roadmap.hourly_rates = hourly_rates
        
        db.commit()
        db.refresh(roadmap)
        
        return RoadmapResponse.from_orm(roadmap)
        
    except Exception as e:
        logger.error(f"Cost recalculation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cost recalculation failed: {str(e)}"
        )


@router.delete("/roadmaps/{roadmap_id}")
async def delete_roadmap(
    roadmap_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a roadmap
    """
    roadmap = db.query(Roadmap).filter(
        Roadmap.id == roadmap_id,
        Roadmap.user_id == current_user.id
    ).first()
    
    if not roadmap:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Roadmap not found"
        )
    
    db.delete(roadmap)
    db.commit()
    
    return {"message": "Roadmap deleted successfully"}