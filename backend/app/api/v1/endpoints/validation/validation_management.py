"""
Validation management endpoints - CRUD operations for validations
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from app.core.database import get_db
from app.api.deps.auth import get_current_user
from app.models.user import User
from app.models.idea_validation import IdeaValidation
from app.schemas.idea_validation import IdeaValidationResponse, IdeaValidationListResponse
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/validations", response_model=IdeaValidationListResponse)
async def get_user_validations(
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's validation history
    """
    try:
        validations = db.query(IdeaValidation).filter(
            IdeaValidation.user_id == current_user.id
        ).offset(skip).limit(limit).all()
        
        total = db.query(IdeaValidation).filter(
            IdeaValidation.user_id == current_user.id
        ).count()
        
        return IdeaValidationListResponse(
            validations=validations,
            total=total,
            skip=skip,
            limit=limit
        )

    except Exception as e:
        logger.error(f"Failed to get user validations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve validations: {str(e)}"
        )


@router.get("/validations/{validation_id}", response_model=IdeaValidationResponse)
async def get_validation(
    validation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get specific validation by ID
    """
    try:
        validation = db.query(IdeaValidation).filter(
            IdeaValidation.id == validation_id,
            IdeaValidation.user_id == current_user.id
        ).first()
        
        if not validation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Validation not found"
            )
        
        return validation

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get validation {validation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve validation: {str(e)}"
        )


@router.delete("/validations/{validation_id}")
async def delete_validation(
    validation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a validation
    """
    try:
        validation = db.query(IdeaValidation).filter(
            IdeaValidation.id == validation_id,
            IdeaValidation.user_id == current_user.id
        ).first()
        
        if not validation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Validation not found"
            )
        
        db.delete(validation)
        db.commit()
        
        return {"message": "Validation deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete validation {validation_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete validation: {str(e)}"
        )


@router.get("/validations/{validation_id}/status")
async def get_validation_status(
    validation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get validation status and progress
    """
    try:
        validation = db.query(IdeaValidation).filter(
            IdeaValidation.id == validation_id,
            IdeaValidation.user_id == current_user.id
        ).first()
        
        if not validation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Validation not found"
            )
        
        return {
            "id": validation.id,
            "status": validation.status,
            "progress": getattr(validation, 'progress', None),
            "error_message": validation.error_message,
            "created_at": validation.created_at,
            "updated_at": validation.updated_at,
            "completed_at": validation.completed_at
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get validation status {validation_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve validation status: {str(e)}"
        )
