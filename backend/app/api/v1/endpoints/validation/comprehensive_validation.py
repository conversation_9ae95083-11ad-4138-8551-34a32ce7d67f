"""
Comprehensive validation endpoints using multi-agent approach
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Any, Dict
from pydantic import BaseModel

from app.core.database import get_db
from app.api.deps.auth import get_current_user
from app.core.security import verify_token
from app.models.user import User
from app.services.ai_service import AIService, AIProvider
import logging
import json
import asyncio

logger = logging.getLogger(__name__)

router = APIRouter()


class ComprehensiveValidationRequest(BaseModel):
    idea_description: str
    answers: dict


@router.post("/comprehensive-validation")
async def comprehensive_validation(
    request: ComprehensiveValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Run comprehensive idea validation using multi-agent approach
    """
    try:
        ai_service = AIService()
        validation_report = await ai_service.comprehensive_idea_validation(
            request.idea_description,
            request.answers
        )
        
        return {
            "status": "completed",
            "report": validation_report,
            "user_id": current_user.id
        }

    except Exception as e:
        logger.error(f"Comprehensive validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Comprehensive validation failed: {str(e)}"
        )


@router.post("/start-comprehensive-validation")
async def start_comprehensive_validation(
    request: ComprehensiveValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Start comprehensive validation process (for SSE streaming)
    """
    try:
        # For now, just return a validation ID
        # In a production system, you'd store this in Redis or similar
        validation_id = f"validation_{current_user.id}_{hash(request.idea_description)}"
        
        return {
            "validation_id": validation_id,
            "status": "started",
            "stream_url": f"/api/v1/validation/comprehensive-validation-stream?validation_id={validation_id}"
        }

    except Exception as e:
        logger.error(f"Failed to start comprehensive validation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start validation: {str(e)}"
        )


@router.get("/comprehensive-validation-stream")
async def comprehensive_validation_stream(
    token: str = None,
    idea: str = None,
    answers: str = None,
    use_celery: bool = True,
    db: Session = Depends(get_db)
):
    """
    Stream comprehensive validation progress using SSE
    """
    try:
        # Verify token if provided
        current_user = None
        if token:
            try:
                current_user = verify_token(token, db)
            except Exception as e:
                logger.warning(f"Token verification failed: {str(e)}")
        
        if not idea:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Idea parameter is required"
            )
        
        # Parse answers
        try:
            answers_dict = json.loads(answers) if answers else {}
        except json.JSONDecodeError:
            answers_dict = {}
        
        # Initialize AI service
        ai_service = AIService()
        
        async def generate_validation_stream():
            try:
                logger.info(f"🚀 Starting comprehensive validation stream for idea: {idea[:50]}...")
                
                # Stream the comprehensive validation
                async for chunk in ai_service.comprehensive_idea_validation_stream(
                    idea, answers_dict, use_celery
                ):
                    yield chunk
                    
                logger.info("✅ Comprehensive validation stream completed")
                
            except Exception as e:
                logger.error(f"❌ Validation stream error: {str(e)}")
                error_data = {
                    "type": "error",
                    "message": f"Validation failed: {str(e)}",
                    "timestamp": "now"
                }
                yield f"data: {json.dumps(error_data)}\n\n"
        
        return StreamingResponse(
            generate_validation_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
                "Access-Control-Allow-Methods": "*"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Stream setup failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to setup validation stream: {str(e)}"
        )


@router.post("/mixture-of-agents-validation")
async def mixture_of_agents_validation(
    request: ComprehensiveValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Run mixture-of-agents validation using multiple AI providers
    """
    try:
        ai_service = AIService()
        validation_report = await ai_service.mixture_of_agents_validation(
            request.idea_description,
            request.answers
        )
        
        return {
            "status": "completed",
            "report": validation_report,
            "methodology": "mixture-of-agents",
            "user_id": current_user.id
        }

    except Exception as e:
        logger.error(f"Mixture of agents validation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Mixture of agents validation failed: {str(e)}"
        )
