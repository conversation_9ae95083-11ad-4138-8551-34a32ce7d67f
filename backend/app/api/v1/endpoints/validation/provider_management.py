"""
AI provider management endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any

from app.api.deps.auth import get_current_user
from app.models.user import User
from app.services.ai_service import AIService
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("")
async def get_available_providers():
    """
    Get list of available AI providers
    """
    try:
        ai_service = AIService()
        available_providers = ai_service.get_available_providers()
        
        return {
            "available_providers": [provider.value for provider in available_providers],
            "total_providers": len(available_providers),
            "supports_mixture_of_agents": "mixture-of-agents" in [p.value for p in available_providers]
        }

    except Exception as e:
        logger.error(f"Failed to get available providers: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve available providers: {str(e)}"
        )


@router.get("/status")
async def get_providers_status():
    """
    Get detailed status of all AI providers
    """
    try:
        ai_service = AIService()
        available_providers = ai_service.get_available_providers()
        
        # Get provider manager for detailed status
        provider_manager = ai_service.provider_manager
        
        provider_status = {}
        for provider_type, provider_instance in provider_manager.providers.items():
            provider_status[provider_type.value] = {
                "available": provider_instance.is_available(),
                "name": provider_instance.get_provider_name(),
                "configured": provider_instance.client is not None
            }
        
        return {
            "providers": provider_status,
            "available_count": len(available_providers),
            "total_count": len(provider_manager.providers),
            "external_tools": ai_service.tools_manager.get_available_tools()
        }

    except Exception as e:
        logger.error(f"Failed to get provider status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve provider status: {str(e)}"
        )


@router.get("/capabilities")
async def get_provider_capabilities():
    """
    Get capabilities and features of each provider
    """
    try:
        capabilities = {
            "openai": {
                "models": ["gpt-4o", "gpt-4", "gpt-3.5-turbo"],
                "features": ["chat", "completion", "function_calling"],
                "strengths": ["general_purpose", "reasoning", "code_generation"],
                "best_for": ["general_validation", "technical_analysis"]
            },
            "anthropic": {
                "models": ["claude-3-5-sonnet-20241022", "claude-3-haiku"],
                "features": ["chat", "completion", "long_context"],
                "strengths": ["analysis", "reasoning", "safety"],
                "best_for": ["comprehensive_analysis", "risk_assessment"]
            },
            "deepseek": {
                "models": ["deepseek-chat", "deepseek-coder"],
                "features": ["chat", "completion", "reasoning"],
                "strengths": ["reasoning", "problem_solving", "cost_effective"],
                "best_for": ["logical_analysis", "question_generation"]
            },
            "gemini": {
                "models": ["gemini-1.5-pro", "gemini-1.5-flash"],
                "features": ["chat", "completion", "multimodal"],
                "strengths": ["multimodal", "large_context", "fast"],
                "best_for": ["data_analysis", "content_generation"]
            },
            "perplexity": {
                "models": ["llama-3.1-sonar-large-128k-online"],
                "features": ["chat", "web_search", "real_time_data"],
                "strengths": ["web_search", "current_information", "citations"],
                "best_for": ["market_research", "competitive_analysis"]
            }
        }
        
        return {
            "provider_capabilities": capabilities,
            "mixture_of_agents": {
                "description": "Uses multiple providers in parallel for comprehensive analysis",
                "requires": "At least 2 providers configured",
                "consolidation_provider": "anthropic (preferred) or first available",
                "benefits": ["diverse_perspectives", "error_resilience", "comprehensive_coverage"]
            }
        }

    except Exception as e:
        logger.error(f"Failed to get provider capabilities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve provider capabilities: {str(e)}"
        )


@router.post("/test/{provider_name}")
async def test_provider(
    provider_name: str,
    current_user: User = Depends(get_current_user)
):
    """
    Test a specific AI provider with a simple request
    """
    try:
        ai_service = AIService()
        
        # Validate provider name
        available_providers = ai_service.get_available_providers()
        provider_names = [p.value for p in available_providers]
        
        if provider_name not in provider_names:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Provider '{provider_name}' not available. Available providers: {provider_names}"
            )
        
        # Test the provider with a simple prompt
        from app.services.ai_providers import AIProvider
        provider = AIProvider(provider_name)
        
        test_prompt = "Respond with 'Hello, I am working correctly!' to confirm you are functioning."
        
        response = await ai_service.provider_manager.generate_response(
            provider, test_prompt, max_tokens=50
        )
        
        return {
            "provider": provider_name,
            "status": "success",
            "test_response": response,
            "message": f"Provider {provider_name} is working correctly"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Provider test failed for {provider_name}: {str(e)}")
        return {
            "provider": provider_name,
            "status": "error",
            "error": str(e),
            "message": f"Provider {provider_name} test failed"
        }
