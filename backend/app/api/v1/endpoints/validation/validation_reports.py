"""
Validation reports endpoints - Managing validation report storage and retrieval
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from pydantic import BaseModel
import uuid

from app.core.database import get_db
from app.api.deps.auth import get_current_user
from app.models.user import User
from app.models.idea_validation import ValidationReport
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


class ValidationReportCreate(BaseModel):
    title: str
    description: str
    user_answers: dict
    report_data: dict
    agent_activities: list = None
    processing_metadata: dict = None


class ValidationReportResponse(BaseModel):
    id: str
    title: str
    description: str
    user_answers: dict
    report_data: dict
    agent_activities: list = None
    processing_metadata: dict = None
    created_at: str
    updated_at: str = None
    
    class Config:
        from_attributes = True


@router.post("", response_model=ValidationReportResponse)
async def create_validation_report(
    report_data: ValidationReportCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Save a validation report to the database
    """
    try:
        # Create new validation report
        db_report = ValidationReport(
            id=str(uuid.uuid4()),
            title=report_data.title,
            description=report_data.description,
            user_id=current_user.id,
            user_answers=report_data.user_answers,
            report_data=report_data.report_data,
            agent_activities=report_data.agent_activities or [],
            processing_metadata=report_data.processing_metadata or {}
        )
        
        db.add(db_report)
        db.commit()
        db.refresh(db_report)
        
        return ValidationReportResponse(
            id=db_report.id,
            title=db_report.title,
            description=db_report.description,
            user_answers=db_report.user_answers,
            report_data=db_report.report_data,
            agent_activities=db_report.agent_activities,
            processing_metadata=db_report.processing_metadata,
            created_at=db_report.created_at.isoformat(),
            updated_at=db_report.updated_at.isoformat() if db_report.updated_at else None
        )

    except Exception as e:
        logger.error(f"Failed to create validation report: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create validation report: {str(e)}"
        )


@router.get("/{report_id}", response_model=ValidationReportResponse)
async def get_validation_report(
    report_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a validation report by ID (public access for sharing)
    """
    try:
        report = db.query(ValidationReport).filter(
            ValidationReport.id == report_id
        ).first()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Validation report not found"
            )
        
        return ValidationReportResponse(
            id=report.id,
            title=report.title,
            description=report.description,
            user_answers=report.user_answers,
            report_data=report.report_data,
            agent_activities=report.agent_activities,
            processing_metadata=report.processing_metadata,
            created_at=report.created_at.isoformat(),
            updated_at=report.updated_at.isoformat() if report.updated_at else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get validation report {report_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve validation report: {str(e)}"
        )


@router.get("", response_model=List[ValidationReportResponse])
async def get_user_validation_reports(
    skip: int = 0,
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all validation reports for the current user
    """
    try:
        reports = db.query(ValidationReport).filter(
            ValidationReport.user_id == current_user.id
        ).offset(skip).limit(limit).all()
        
        return [
            ValidationReportResponse(
                id=report.id,
                title=report.title,
                description=report.description,
                user_answers=report.user_answers,
                report_data=report.report_data,
                agent_activities=report.agent_activities,
                processing_metadata=report.processing_metadata,
                created_at=report.created_at.isoformat(),
                updated_at=report.updated_at.isoformat() if report.updated_at else None
            )
            for report in reports
        ]

    except Exception as e:
        logger.error(f"Failed to get user validation reports: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve validation reports: {str(e)}"
        )


@router.delete("/{report_id}")
async def delete_validation_report(
    report_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a validation report
    """
    try:
        report = db.query(ValidationReport).filter(
            ValidationReport.id == report_id,
            ValidationReport.user_id == current_user.id
        ).first()
        
        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Validation report not found"
            )
        
        db.delete(report)
        db.commit()
        
        return {"message": "Validation report deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete validation report {report_id}: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete validation report: {str(e)}"
        )
