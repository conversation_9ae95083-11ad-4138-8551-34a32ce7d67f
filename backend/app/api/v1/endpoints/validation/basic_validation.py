"""
Basic idea validation endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.database import get_db
from app.api.deps.auth import get_current_user
from app.models.user import User
from app.models.idea_validation import IdeaValidation, ValidationStatus
from app.schemas.idea_validation import IdeaValidationCreate, IdeaValidationResponse
from app.services.ai_service import AIService, AIProvider
import logging
import json

logger = logging.getLogger(__name__)

router = APIRouter()


class IdeaQuestionRequest(BaseModel):
    idea_description: str


@router.post("/validate", response_model=IdeaValidationResponse)
async def validate_idea(
    validation_data: IdeaValidationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Validate a new idea using AI analysis
    """
    try:
        # Create validation record
        db_validation = IdeaValidation(
            title=validation_data.title,
            description=validation_data.description,
            user_id=current_user.id,
            validation_type=validation_data.validation_type,
            status=ValidationStatus.PENDING
        )
        db.add(db_validation)
        db.commit()
        db.refresh(db_validation)

        # Initialize AI service
        ai_provider = AIProvider(validation_data.ai_provider) if validation_data.ai_provider else AIProvider.OPENAI
        ai_service = AIService(provider=ai_provider)

        # Generate validation using AI
        try:
            # Simple validation for basic endpoint
            validation_result = await ai_service.validate_idea(
                validation_data.description,
                {},  # No additional answers for basic validation
                ai_provider
            )
            
            # Parse result if it's JSON
            try:
                result_data = json.loads(validation_result)
                db_validation.project_concept = result_data
            except json.JSONDecodeError:
                # If not JSON, store as text in project_concept
                db_validation.project_concept = {"analysis": validation_result}

            db_validation.status = ValidationStatus.COMPLETED
            db_validation.ai_provider = ai_provider.value

        except Exception as ai_error:
            logger.error(f"AI validation failed: {str(ai_error)}")
            db_validation.status = ValidationStatus.FAILED
            db_validation.error_message = str(ai_error)

        db.commit()
        db.refresh(db_validation)

        return db_validation

    except Exception as e:
        logger.error(f"Validation creation failed: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create validation: {str(e)}"
        )


@router.post("/validate/stream")
async def validate_idea_stream(
    validation_data: IdeaValidationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Validate idea with streaming response for real-time UI updates
    """
    try:
        # Create validation record
        db_validation = IdeaValidation(
            title=validation_data.title,
            description=validation_data.description,
            user_id=current_user.id,
            validation_type=validation_data.validation_type,
            status=ValidationStatus.PROCESSING
        )
        db.add(db_validation)
        db.commit()
        db.refresh(db_validation)

        # Initialize AI service
        ai_provider = AIProvider(validation_data.ai_provider) if validation_data.ai_provider else AIProvider.OPENAI
        ai_service = AIService(provider=ai_provider)

        async def generate_stream():
            try:
                # Send initial status
                yield f"data: {json.dumps({'status': 'started', 'validation_id': db_validation.id})}\n\n"

                # For basic streaming, we'll use simple validation
                validation_result = await ai_service.validate_idea(
                    validation_data.description,
                    {},
                    ai_provider
                )

                # Update database
                try:
                    result_data = json.loads(validation_result)
                    db_validation.project_concept = result_data
                except json.JSONDecodeError:
                    db_validation.project_concept = {"analysis": validation_result}

                db_validation.status = ValidationStatus.COMPLETED
                db_validation.ai_provider = ai_provider.value
                db.commit()

                # Send final result
                yield f"data: {json.dumps({'status': 'completed', 'result': validation_result})}\n\n"

            except Exception as e:
                logger.error(f"Streaming validation failed: {str(e)}")
                db_validation.status = ValidationStatus.FAILED
                db_validation.error_message = str(e)
                db.commit()
                yield f"data: {json.dumps({'status': 'error', 'error': str(e)})}\n\n"

        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )

    except Exception as e:
        logger.error(f"Stream validation setup failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start streaming validation: {str(e)}"
        )


@router.post("/generate-questions")
async def generate_idea_questions(
    request: IdeaQuestionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate dynamic questions based on the idea
    """
    try:
        ai_service = AIService(provider=AIProvider.DEEPSEEK)

        # Use the generate_idea_questions method
        questions_data = await ai_service.generate_idea_questions(request.idea_description)

        return {
            "success": True,
            "questions": questions_data["questions"]
        }

    except Exception as e:
        logger.error(f"Question generation failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate questions: {str(e)}"
        )
