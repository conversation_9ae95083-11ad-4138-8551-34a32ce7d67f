"""
Validation endpoints module
"""

from fastapi import APIRouter
from .basic_validation import router as basic_router
from .comprehensive_validation import router as comprehensive_router
from .validation_management import router as management_router
from .validation_reports import router as reports_router
from .provider_management import router as providers_router

# Create main validation router
router = APIRouter()

# Include all sub-routers
router.include_router(basic_router, tags=["basic-validation"])
router.include_router(comprehensive_router, tags=["comprehensive-validation"])
router.include_router(management_router, tags=["validation-management"])
router.include_router(reports_router, prefix="/reports", tags=["validation-reports"])
router.include_router(providers_router, prefix="/providers", tags=["ai-providers"])

__all__ = ["router"]
