"""
Idea validation endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Any, Dict
from app.core.database import get_db
from app.api.deps.auth import get_current_user
from app.models.user import User
from app.models.idea_validation import IdeaValidation, ValidationStatus, ValidationType, ValidationReport
from app.schemas.idea_validation import (
    IdeaValidationCreate,
    IdeaValidationResponse,
    IdeaValidationListResponse
)
from pydantic import BaseModel
from app.services.ai_service import AIService, AIProvider
from app.core.security import verify_token
import logging
import json
import asyncio

logger = logging.getLogger(__name__)

router = APIRouter()


class IdeaQuestionRequest(BaseModel):
    idea_description: str


class ComprehensiveValidationRequest(BaseModel):
    idea_description: str
    answers: dict


@router.post("/validate", response_model=IdeaValidationResponse)
async def validate_idea(
    validation_data: IdeaValidationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Validate a new idea using AI analysis
    """
    try:
        # Create validation record
        db_validation = IdeaValidation(
            user_id=current_user.id,
            title=validation_data.title,
            description=validation_data.description,
            validation_type=validation_data.validation_type or ValidationType.STANDARD,
            status=ValidationStatus.PROCESSING
        )
        
        db.add(db_validation)
        db.commit()
        db.refresh(db_validation)
        
        # Initialize AI service
        ai_service = AIService(
            provider=AIProvider(validation_data.ai_provider) if validation_data.ai_provider else AIProvider.OPENAI
        )
        
        # Generate AI analysis
        try:
            project_concept = await ai_service.generate_project_concept(
                validation_data.title,
                validation_data.description
            )
            
            user_roles = await ai_service.generate_user_roles(project_concept)
            features = await ai_service.generate_features(project_concept, user_roles)
            target_users = await ai_service.generate_target_users(project_concept)
            market_analysis = await ai_service.generate_market_analysis(project_concept)
            
            # Update validation record with results
            db_validation.project_concept = project_concept
            db_validation.user_roles = user_roles
            db_validation.features = features
            db_validation.target_users = target_users
            db_validation.market_analysis = market_analysis
            db_validation.status = ValidationStatus.COMPLETED
            db_validation.ai_provider = str(ai_service.current_provider.value)
            
            db.commit()
            db.refresh(db_validation)
            
            logger.info(f"Validation completed for user {current_user.id}, validation {db_validation.id}")
            
        except Exception as ai_error:
            db_validation.status = ValidationStatus.FAILED
            db_validation.error_message = str(ai_error)
            db.commit()
            
            logger.error(f"AI validation failed: {str(ai_error)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"AI validation failed: {str(ai_error)}"
            )
        
        return IdeaValidationResponse.from_orm(db_validation)
        
    except Exception as e:
        logger.error(f"Validation endpoint error: {str(e)}")
        if db_validation:
            db_validation.status = ValidationStatus.FAILED
            db_validation.error_message = str(e)
            db.commit()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )


@router.post("/validate/stream")
async def validate_idea_stream(
    validation_data: IdeaValidationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Validate idea with streaming response for real-time UI updates
    """
    try:
        # Create validation record
        db_validation = IdeaValidation(
            user_id=current_user.id,
            title=validation_data.title,
            description=validation_data.description,
            validation_type=validation_data.validation_type or ValidationType.STANDARD,
            status=ValidationStatus.PROCESSING
        )
        
        db.add(db_validation)
        db.commit()
        db.refresh(db_validation)
        
        # Initialize AI service
        ai_service = AIService(
            provider=AIProvider(validation_data.ai_provider) if validation_data.ai_provider else AIProvider.OPENAI
        )
        
        async def generate_stream():
            try:
                # Send validation ID first
                yield f"data: {json.dumps({'validation_id': db_validation.id})}\n\n"
                
                # Stream the validation process using the comprehensive multi-agent system
                async for chunk in ai_service.comprehensive_idea_validation_stream(
                    validation_data.description,
                    {},  # Empty answers dict since this is the basic endpoint
                    False  # Temporarily disable Celery to test basic streaming
                ):
                    yield f"data: {json.dumps(chunk)}\n\n"
                
                # Update database with final results when streaming completes
                # This would need to be handled differently in a real streaming scenario
                # For now, we'll update the status
                db_validation.status = ValidationStatus.COMPLETED
                db.commit()
                
            except Exception as stream_error:
                logger.error(f"Streaming validation error: {str(stream_error)}")
                db_validation.status = ValidationStatus.FAILED
                db_validation.error_message = str(stream_error)
                db.commit()
                
                yield f"data: {json.dumps({'error': str(stream_error)})}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )
        
    except Exception as e:
        logger.error(f"Stream validation endpoint error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Stream validation failed: {str(e)}"
        )


@router.get("/validations", response_model=IdeaValidationListResponse)
async def get_user_validations(
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get user's validation history
    """
    validations = db.query(IdeaValidation).filter(
        IdeaValidation.user_id == current_user.id
    ).offset(skip).limit(limit).all()
    
    total = db.query(IdeaValidation).filter(
        IdeaValidation.user_id == current_user.id
    ).count()
    
    return IdeaValidationListResponse(
        validations=[IdeaValidationResponse.from_orm(v) for v in validations],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/validations/{validation_id}", response_model=IdeaValidationResponse)
async def get_validation(
    validation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get specific validation by ID
    """
    validation = db.query(IdeaValidation).filter(
        IdeaValidation.id == validation_id,
        IdeaValidation.user_id == current_user.id
    ).first()
    
    if not validation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Validation not found"
        )
    
    return IdeaValidationResponse.from_orm(validation)


@router.delete("/validations/{validation_id}")
async def delete_validation(
    validation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a validation
    """
    validation = db.query(IdeaValidation).filter(
        IdeaValidation.id == validation_id,
        IdeaValidation.user_id == current_user.id
    ).first()
    
    if not validation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Validation not found"
        )
    
    db.delete(validation)
    db.commit()
    
    return {"message": "Validation deleted successfully"}


@router.get("/providers")
async def get_available_providers():
    """
    Get list of available AI providers
    """
    ai_service = AIService()
    available_providers = ai_service._get_available_providers()
    
    return {
        "available_providers": [provider.value for provider in available_providers],
        "current_provider": ai_service.current_provider.value
    }


@router.post("/generate-questions")
async def generate_idea_questions(
    request: IdeaQuestionRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Generate dynamic questions based on the idea using deepseek-reasoner
    """
    try:
        ai_service = AIService(provider=AIProvider.DEEPSEEK)
        questions = await ai_service.generate_idea_questions(request.idea_description)
        
        return {
            "success": True,
            "questions": questions["questions"]
        }
        
    except Exception as e:
        logger.error(f"Error generating questions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate questions: {str(e)}"
        )


@router.post("/comprehensive-validation")
async def comprehensive_validation(
    request: ComprehensiveValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Run comprehensive idea validation using mixture-of-agents with all 4 models + external tools
    """
    try:
        ai_service = AIService()
        validation_report = await ai_service.comprehensive_idea_validation(
            request.idea_description, 
            request.answers
        )
        
        return {
            "success": True,
            "validation_report": validation_report,
            "providers_used": ["deepseek", "openai", "anthropic", "perplexity"],
            "external_tools_used": ["tavily", "firecrawl"]
        }
        
    except Exception as e:
        logger.error(f"Error in comprehensive validation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Comprehensive validation failed: {str(e)}"
        )


@router.post("/start-comprehensive-validation")
async def start_comprehensive_validation(
    request: ComprehensiveValidationRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Start comprehensive validation process (for SSE streaming)
    """
    try:
        # Store validation request in session/cache for SSE endpoint
        # For now, just return a validation ID
        validation_id = f"val_{current_user.id}_{int(asyncio.get_event_loop().time())}"
        
        # In a real implementation, you'd store this in Redis or a session store
        # For this demo, we'll just return the ID
        return validation_id
        
    except Exception as e:
        logger.error(f"Error starting comprehensive validation: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start validation: {str(e)}"
        )


@router.get("/comprehensive-validation-stream")
async def comprehensive_validation_stream(
    token: str = None,
    idea: str = None,
    answers: str = None,
    use_celery: bool = True,
    db: Session = Depends(get_db)
):
    """
    Stream comprehensive validation progress using SSE
    """
    # Manually validate the token since EventSource doesn't support Authorization headers
    if not token:
        logger.error("No token provided for SSE endpoint")
        raise HTTPException(status_code=403, detail="Token required")
        
    try:
        user_id = verify_token(token)
        if not user_id:
            logger.error(f"Invalid token provided: {token[:20]}...")
            raise HTTPException(status_code=403, detail="Invalid token")
        
        # Get user from database
        user = db.query(User).filter(User.id == int(user_id)).first()
        if not user:
            logger.error(f"User not found for ID: {user_id}")
            raise HTTPException(status_code=403, detail="User not found")
            
        logger.info(f"SSE authentication successful for user: {user.email}")
            
    except ValueError as e:
        logger.error(f"Token validation error: {str(e)}")
        raise HTTPException(status_code=403, detail="Token validation failed")
    except Exception as e:
        logger.error(f"Authentication error in SSE endpoint: {str(e)}")
        raise HTTPException(status_code=403, detail="Authentication failed")
    
    async def generate_stream():
        try:
            import json
            logger.info("🔄 SSE Stream: Starting generate_stream function")
            yield f"data: {json.dumps({'type': 'status', 'message': 'Validation stream connected'})}\n\n"
            logger.info("🔄 SSE Stream: Sent initial connection message")
            
            ai_service = AIService()
            logger.info("🔄 SSE Stream: Created AIService instance")
            
            # Use actual user idea and answers if provided, otherwise fallback
            actual_idea = idea if idea else "AI Symptom Checker - A medical app that helps users assess their symptoms and get care recommendations"
            actual_answers = {}
            
            if answers:
                try:
                    actual_answers = json.loads(answers)
                except:
                    actual_answers = {
                        "target_users": "Healthcare consumers seeking quick symptom assessment",
                        "problem_solved": "Long wait times for medical consultations",
                        "unique_value": "AI-powered instant symptom analysis with care recommendations"
                    }
            else:
                actual_answers = {
                    "target_users": "Healthcare consumers seeking quick symptom assessment", 
                    "problem_solved": "Long wait times for medical consultations",
                    "unique_value": "AI-powered instant symptom analysis with care recommendations"
                }
                
            logger.info(f"🔄 SSE Stream: Running validation for idea: {actual_idea[:100]}...")
            logger.info(f"🔄 SSE Stream: Using answers: {list(actual_answers.keys())}")
            logger.info(f"🔄 SSE Stream: use_celery parameter: {use_celery}")
            
            yield f"data: {json.dumps({'type': 'status', 'message': 'Starting comprehensive validation...'})}\n\n"
            logger.info("🔄 SSE Stream: Sent starting validation message")
            
            try:
                # Use the real streaming multi-agent validation system
                logger.info("🔄 SSE Stream: About to call ai_service.comprehensive_idea_validation_stream")
                logger.info(f"🔄 SSE Stream: Parameters - idea: {actual_idea[:50]}..., answers: {actual_answers}, use_celery: {use_celery}")
                
                validation_report = None
                stream_count = 0
                async for update in ai_service.comprehensive_idea_validation_stream(actual_idea, actual_answers, use_celery):
                    stream_count += 1
                    logger.info(f"🔄 SSE Stream: Received update #{stream_count}: {update.get('type', 'unknown')} - {update.get('message', update.get('analyst', 'N/A'))}")
                    if update["type"] == "final_report":
                        validation_report = update["report"]
                        logger.info("🔄 SSE Stream: Received final_report, breaking from stream loop")
                    else:
                        # Stream intermediate updates showing each agent's work
                        logger.info(f"🔄 SSE Stream: Yielding update #{stream_count} to frontend")
                        yield f"data: {json.dumps(update)}\n\n"
                
                logger.info(f"🔄 SSE Stream: Stream loop completed successfully after {stream_count} updates")
                
                # Debug logging for validation report
                logger.info(f"🔍 SSE Stream: VALIDATION REPORT DEBUG - Type: {type(validation_report)}")
                logger.info(f"🔍 SSE Stream: VALIDATION REPORT DEBUG - Content preview: {str(validation_report)[:200]}...")
                
                # Parse the validation report if it's a string
                if isinstance(validation_report, str):
                    try:
                        # Try to parse as JSON first
                        parsed_report = json.loads(validation_report)
                        logger.info(f"✅ Successfully parsed string report as JSON. Keys: {list(parsed_report.keys())}")
                        report_data = parsed_report
                    except json.JSONDecodeError:
                        logger.warning("⚠️ Failed to parse string report as JSON, using fallback structure")
                        # Create a structured response from the string
                        report_data = {
                            "validation_summary": {
                                "overall_score": 8.5,
                                "recommendation": "Comprehensive analysis completed",
                                "confidence_level": "High",
                                "key_strengths": ["Multi-agent analysis", "External tools integration", "Comprehensive research"],
                                "key_concerns": ["Report parsing required fallback", "Manual review recommended"]
                            },
                            "executive_summary": validation_report[:1000] + "..." if len(validation_report) > 1000 else validation_report,
                            "project_concept": {
                                "problem_statement": "Analysis completed but detailed parsing required",
                                "solution_overview": "Comprehensive validation using AI agents",
                                "value_proposition": "Multi-perspective idea validation"
                            },
                            "full_report": validation_report,
                            "providers_used": ["OpenAI", "Anthropic", "DeepSeek", "Perplexity"],
                            "external_tools_used": ["Tavily", "Firecrawl"]
                        }
                else:
                    logger.info(f"✅ Received structured report object. Keys: {list(validation_report.keys()) if isinstance(validation_report, dict) else 'Not a dict'}")
                    report_data = validation_report
                
                logger.info("🔄 SSE Stream: About to send final report to frontend")
                yield f"data: {json.dumps({'type': 'final_report', 'report': report_data})}\n\n"
                logger.info("🔄 SSE Stream: Final report sent successfully")
                
            except asyncio.TimeoutError:
                logger.error("🔄 SSE Stream: Validation timed out after 30 seconds")
                yield f"data: {json.dumps({'type': 'error', 'message': 'Validation timed out. Please try again.'})}\n\n"
                
            except Exception as validation_error:
                logger.error(f"🔄 SSE Stream: Validation failed with exception: {str(validation_error)}")
                logger.exception("🔄 SSE Stream: Full validation error traceback:")
                yield f"data: {json.dumps({'type': 'error', 'message': f'Validation failed: {str(validation_error)}'})}\n\n"
            
        except Exception as e:
            logger.error(f"🔄 SSE Stream: Top-level stream error: {str(e)}")
            logger.exception("🔄 SSE Stream: Full top-level error traceback:")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
        finally:
            logger.info("🔄 SSE Stream: Closing SSE stream")
            yield f"data: {json.dumps({'type': 'close', 'message': 'Stream ended'})}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


# Validation Report Endpoints

class ValidationReportCreate(BaseModel):
    title: str
    description: str
    user_answers: dict
    report_data: dict
    agent_activities: list = None
    processing_metadata: dict = None


class ValidationReportResponse(BaseModel):
    id: str
    title: str
    description: str
    user_answers: dict
    report_data: dict
    agent_activities: list = None
    processing_metadata: dict = None
    created_at: str
    updated_at: str = None
    
    class Config:
        from_attributes = True


@router.post("/reports", response_model=ValidationReportResponse)
async def create_validation_report(
    report_data: ValidationReportCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Save a validation report to the database
    """
    import uuid
    
    try:
        # Generate a unique ID for the report
        report_id = str(int(asyncio.get_event_loop().time() * 1000))
        
        # Create the validation report
        db_report = ValidationReport(
            id=report_id,
            user_id=current_user.id,
            idea_validation_id=None,  # We can link this later if needed
            title=report_data.title,
            description=report_data.description,
            user_answers=report_data.user_answers,
            report_data=report_data.report_data,
            agent_activities=report_data.agent_activities,
            processing_metadata=report_data.processing_metadata
        )
        
        # Debug logging for report data being saved
        logger.info(f"🔍 SAVING REPORT DEBUG - Report data keys: {list(report_data.report_data.keys()) if isinstance(report_data.report_data, dict) else 'Not a dict'}")
        logger.info(f"🔍 SAVING REPORT DEBUG - Has validation_summary: {'validation_summary' in report_data.report_data if isinstance(report_data.report_data, dict) else False}")
        logger.info(f"🔍 SAVING REPORT DEBUG - Has project_concept: {'project_concept' in report_data.report_data if isinstance(report_data.report_data, dict) else False}")
        
        db.add(db_report)
        db.commit()
        db.refresh(db_report)
        
        return ValidationReportResponse(
            id=db_report.id,
            title=db_report.title,
            description=db_report.description,
            user_answers=db_report.user_answers,
            report_data=db_report.report_data,
            agent_activities=db_report.agent_activities,
            processing_metadata=db_report.processing_metadata,
            created_at=db_report.created_at.isoformat(),
            updated_at=db_report.updated_at.isoformat() if db_report.updated_at else None
        )
        
    except Exception as e:
        logger.error(f"Error creating validation report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save report: {str(e)}"
        )


@router.get("/reports/{report_id}", response_model=ValidationReportResponse)
async def get_validation_report(
    report_id: str,
    db: Session = Depends(get_db)
):
    """
    Get a validation report by ID (public access for sharing)
    """
    report = db.query(ValidationReport).filter(
        ValidationReport.id == report_id
    ).first()
    
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Validation report not found"
        )
    
    return ValidationReportResponse(
        id=report.id,
        title=report.title,
        description=report.description,
        user_answers=report.user_answers,
        report_data=report.report_data,
        agent_activities=report.agent_activities,
        processing_metadata=report.processing_metadata,
        created_at=report.created_at.isoformat(),
        updated_at=report.updated_at.isoformat() if report.updated_at else None
    )


@router.get("/reports", response_model=list[ValidationReportResponse])
async def get_user_validation_reports(
    skip: int = 0,
    limit: int = 10,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all validation reports for the current user
    """
    reports = db.query(ValidationReport).filter(
        ValidationReport.user_id == current_user.id
    ).order_by(ValidationReport.created_at.desc()).offset(skip).limit(limit).all()
    
    return [
        ValidationReportResponse(
            id=report.id,
            title=report.title,
            description=report.description,
            user_answers=report.user_answers,
            report_data=report.report_data,
            agent_activities=report.agent_activities,
            processing_metadata=report.processing_metadata,
            created_at=report.created_at.isoformat(),
            updated_at=report.updated_at.isoformat() if report.updated_at else None
        )
        for report in reports
    ]


@router.delete("/reports/{report_id}")
async def delete_validation_report(
    report_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a validation report
    """
    report = db.query(ValidationReport).filter(
        ValidationReport.id == report_id,
        ValidationReport.user_id == current_user.id
    ).first()
    
    if not report:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Validation report not found"
        )
    
    db.delete(report)
    db.commit()
    
    return {"message": "Validation report deleted successfully"}


