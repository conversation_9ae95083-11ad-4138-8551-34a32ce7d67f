from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.schemas.auth import (
    LoginRequest, 
    RegisterRequest, 
    Token, 
    PasswordResetRequest,
    PasswordResetConfirm,
    EmailVerificationRequest,
    ResendVerificationRequest
)
from app.schemas.user import UserResponse
from app.services.auth_service import AuthService
from app.api.deps.auth import get_current_user, get_current_active_user
from app.models.user import User

router = APIRouter()


@router.post("/register", response_model=UserResponse)
def register(
    user_data: RegisterRequest,
    db: Session = Depends(get_db)
):
    """Register a new user account"""
    auth_service = AuthService(db)
    user = auth_service.create_user(user_data)
    return user


@router.post("/login", response_model=Token)
def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """Login user and return access token"""
    auth_service = AuthService(db)
    result = auth_service.login_user(login_data)
    
    return {
        "access_token": result["access_token"],
        "token_type": result["token_type"]
    }


@router.get("/me", response_model=UserResponse)
def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information"""
    return current_user


@router.post("/password-reset/request")
def request_password_reset(
    request_data: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """Request password reset"""
    auth_service = AuthService(db)
    auth_service.request_password_reset(request_data.email)
    
    return {"message": "If the email exists, a reset link has been sent"}


@router.post("/password-reset/confirm")
def confirm_password_reset(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """Confirm password reset with token"""
    if not reset_data.passwords_match():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Passwords do not match"
        )
    
    auth_service = AuthService(db)
    auth_service.reset_password(
        reset_data.token, 
        reset_data.new_password, 
        reset_data.confirm_password
    )
    
    return {"message": "Password reset successfully"}


@router.post("/email-verification/request")
def request_email_verification(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Request email verification"""
    if current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already verified"
        )
    
    auth_service = AuthService(db)
    token = auth_service.request_email_verification(current_user)
    
    return {"message": "Verification email sent"}


@router.post("/email-verification/confirm")
def confirm_email_verification(
    verification_data: EmailVerificationRequest,
    db: Session = Depends(get_db)
):
    """Confirm email verification with token"""
    auth_service = AuthService(db)
    auth_service.verify_email(verification_data.token)
    
    return {"message": "Email verified successfully"}


@router.post("/logout")
def logout(
    current_user: User = Depends(get_current_user)
):
    """Logout user (client-side token removal)"""
    # In a JWT-based system, logout is typically handled client-side
    # by removing the token. Server-side logout would require token blacklisting.
    return {"message": "Logged out successfully"}