from fastapi import APIRouter
from app.api.v1.endpoints import auth, validation, roadmap

api_router = APIRouter()

# Include authentication routes
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])

# Include validation routes
api_router.include_router(validation.router, prefix="/validation", tags=["idea-validation"])

# Include roadmap routes
api_router.include_router(roadmap.router, prefix="/roadmap", tags=["roadmap-generation"])

@api_router.get("/ping")
async def ping():
    return {"message": "pong"}

@api_router.get("/")
async def root():
    return {"message": "Incepta API v1"}