from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Enum as SQLEnum, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class RoadmapStatus(enum.Enum):
    GENERATING = "GENERATING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class Roadmap(Base):
    __tablename__ = "roadmaps"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    project_planning_id = Column(Integer, ForeignKey("project_plannings.id"), nullable=True)
    
    # Basic info
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # Phase-specific roadmap data
    phases = Column(JSON, nullable=True)  # Array of phase objects
    team_composition = Column(JSON, nullable=True)  # Role-based team allocation
    cost_breakdown = Column(JSON, nullable=True)  # Detailed cost per phase
    timeline_estimates = Column(JSON, nullable=True)  # Time estimates per phase
    risk_assessments = Column(JSON, nullable=True)  # Risks and mitigation strategies
    milestones = Column(JSON, nullable=True)  # Deliverable-based progress tracking
    
    # Financial data
    total_estimated_cost = Column(Numeric(12, 2), nullable=True)
    hourly_rates = Column(JSON, nullable=True)  # Role-based hourly rates
    
    # Generation metadata
    status = Column(SQLEnum(RoadmapStatus), default=RoadmapStatus.GENERATING)
    ai_provider = Column(String, nullable=True)
    generation_prompt = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="roadmaps")
    project_planning = relationship("ProjectPlanning", back_populates="roadmaps")

    def __repr__(self):
        return f"<Roadmap(id={self.id}, title='{self.title}', status='{self.status.value}')>"