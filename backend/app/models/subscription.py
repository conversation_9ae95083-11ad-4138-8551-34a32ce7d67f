from sqlalchemy import Column, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>ole<PERSON>, DateT<PERSON>, ForeignKey, Numeric, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class SubscriptionStatus(enum.Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    CANCELLED = "CANCELLED"
    PAST_DUE = "PAST_DUE"


class PlanType(enum.Enum):
    FREE = "FREE"
    PRO = "PRO"
    ENTERPRISE = "ENTERPRISE"


class PricingPlan(Base):
    __tablename__ = "pricing_plans"

    id = Column(Integer, primary_key=True, index=True)
    
    # Plan details
    name = Column(String, nullable=False, unique=True)
    plan_type = Column(SQLEnum(PlanType), nullable=False)
    description = Column(String, nullable=True)
    
    # Pricing
    price = Column(Numeric(10, 2), nullable=False)
    currency = Column(String, default="USD")
    billing_period = Column(String, default="month")  # month, year
    
    # Limits and features
    max_validations = Column(Integer, nullable=True)  # null = unlimited
    max_roadmaps = Column(Integer, nullable=True)
    enhanced_validation = Column(Boolean, default=False)
    ai_providers = Column(String, nullable=True)  # JSON array of available providers
    
    # Stripe integration
    stripe_price_id = Column(String, nullable=True, unique=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    subscriptions = relationship("UserSubscription", back_populates="plan")

    def __repr__(self):
        return f"<PricingPlan(id={self.id}, name='{self.name}', type='{self.plan_type.value}')>"


class UserSubscription(Base):
    __tablename__ = "user_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, unique=True)
    plan_id = Column(Integer, ForeignKey("pricing_plans.id"), nullable=False)
    
    # Subscription details
    status = Column(SQLEnum(SubscriptionStatus), default=SubscriptionStatus.ACTIVE)
    
    # Usage tracking
    validations_used = Column(Integer, default=0)
    roadmaps_used = Column(Integer, default=0)
    current_period_start = Column(DateTime(timezone=True), nullable=True)
    current_period_end = Column(DateTime(timezone=True), nullable=True)
    
    # Payment integration
    stripe_subscription_id = Column(String, nullable=True, unique=True)
    stripe_customer_id = Column(String, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    cancelled_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="subscription")
    plan = relationship("PricingPlan", back_populates="subscriptions")

    def __repr__(self):
        return f"<UserSubscription(id={self.id}, user_id={self.user_id}, status='{self.status.value}')>"
    
    def is_pro_user(self) -> bool:
        """Check if user has an active pro subscription"""
        return (
            self.status == SubscriptionStatus.ACTIVE and
            self.plan.plan_type in [PlanType.PRO, PlanType.ENTERPRISE]
        )
    
    def can_use_enhanced_validation(self) -> bool:
        """Check if user can use enhanced validation"""
        return self.is_pro_user() and self.plan.enhanced_validation
    
    def has_validation_quota(self) -> bool:
        """Check if user has remaining validation quota"""
        if self.plan.max_validations is None:  # Unlimited
            return True
        return self.validations_used < self.plan.max_validations