from sqlalchemy import Column, Integer, <PERSON>, Text, <PERSON>ole<PERSON>, DateT<PERSON>, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class ValidationStatus(enum.Enum):
    PENDING = "PENDING"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class ValidationType(enum.Enum):
    STANDARD = "STANDARD"
    ENHANCED = "ENHANCED"


class IdeaValidation(Base):
    __tablename__ = "idea_validations"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Input data
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    validation_type = Column(SQLEnum(ValidationType), default=ValidationType.STANDARD)
    
    # Incepta-proto style AI-generated content
    project_concept = Column(JSON, nullable=True)  # Project overview + user roles
    target_users = Column(JSON, nullable=True)     # User personas + segmentation
    features = Column(JSON, nullable=True)         # Core, must-have, should-have, nice-to-have, future
    market_size_analysis = Column(JSON, nullable=True)      # TAM/SAM/SOM analysis
    target_market = Column(JSON, nullable=True)             # Market positioning + strategy
    unique_value_proposition = Column(JSON, nullable=True)  # Value proposition analysis
    monetization_strategies = Column(JSON, nullable=True)   # Revenue models + pricing
    market_challenges = Column(JSON, nullable=True)         # Barriers + risks
    market_opportunities = Column(JSON, nullable=True)      # Growth potential + expansion
    competitors = Column(JSON, nullable=True)               # Competitive landscape
    citations = Column(JSON, nullable=True)                 # Sources + verification
    
    # Legacy fields (keeping for backward compatibility)
    market_analysis = Column(JSON, nullable=True)
    
    # Validation summary and metadata
    validation_summary = Column(JSON, nullable=True)  # Overall scores and recommendations
    executive_summary = Column(Text, nullable=True)   # Executive summary text
    next_steps = Column(JSON, nullable=True)          # Action items and timeline
    success_metrics = Column(JSON, nullable=True)     # KPIs and metrics
    
    # Processing status
    status = Column(SQLEnum(ValidationStatus), default=ValidationStatus.PENDING)
    error_message = Column(Text, nullable=True)
    
    # Enhanced metadata for incepta-proto
    ai_providers_used = Column(JSON, nullable=True)   # List of AI providers used
    external_tools_used = Column(JSON, nullable=True) # External APIs used (Tavily, Firecrawl)
    agents_consulted = Column(Integer, nullable=True) # Number of specialist agents
    processing_time = Column(Integer, nullable=True)  # Total processing time in seconds
    validation_depth = Column(String, default="Comprehensive") # Standard/Enhanced/Comprehensive
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="idea_validations")
    competitor_analyses = relationship("CompetitorAnalysis", back_populates="idea_validation")
    market_research = relationship("MarketResearch", back_populates="idea_validation")

    def __repr__(self):
        return f"<IdeaValidation(id={self.id}, title='{self.title}', status='{self.status.value}')>"


class CompetitorAnalysis(Base):
    __tablename__ = "competitor_analyses"

    id = Column(Integer, primary_key=True, index=True)
    idea_validation_id = Column(Integer, ForeignKey("idea_validations.id"), nullable=False)
    
    # Competitor data
    company_name = Column(String, nullable=False)
    website_url = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    features = Column(JSON, nullable=True)
    pricing = Column(JSON, nullable=True)
    strengths = Column(JSON, nullable=True)
    weaknesses = Column(JSON, nullable=True)
    
    # Scraping metadata
    scraped_at = Column(DateTime(timezone=True), server_default=func.now())
    scraping_success = Column(Boolean, default=True)
    scraping_error = Column(Text, nullable=True)

    # Relationships
    idea_validation = relationship("IdeaValidation", back_populates="competitor_analyses")
    citations = relationship("VerifiedCitation", back_populates="competitor_analysis")


class MarketResearch(Base):
    __tablename__ = "market_research"

    id = Column(Integer, primary_key=True, index=True)
    idea_validation_id = Column(Integer, ForeignKey("idea_validations.id"), nullable=False)
    
    # Research data
    query = Column(String, nullable=False)
    results = Column(JSON, nullable=True)
    summary = Column(Text, nullable=True)
    key_insights = Column(JSON, nullable=True)
    
    # Search metadata
    search_provider = Column(String, default="tavily")
    searched_at = Column(DateTime(timezone=True), server_default=func.now())
    results_count = Column(Integer, default=0)

    # Relationships
    idea_validation = relationship("IdeaValidation", back_populates="market_research")
    citations = relationship("VerifiedCitation", back_populates="market_research")


class VerifiedCitation(Base):
    __tablename__ = "verified_citations"

    id = Column(Integer, primary_key=True, index=True)
    competitor_analysis_id = Column(Integer, ForeignKey("competitor_analyses.id"), nullable=True)
    market_research_id = Column(Integer, ForeignKey("market_research.id"), nullable=True)
    
    # Citation data
    title = Column(String, nullable=False)
    url = Column(String, nullable=False)
    snippet = Column(Text, nullable=True)
    source_domain = Column(String, nullable=True)
    relevance_score = Column(Integer, default=0)
    
    # Verification status
    is_verified = Column(Boolean, default=False)
    verification_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    competitor_analysis = relationship("CompetitorAnalysis", back_populates="citations")
    market_research = relationship("MarketResearch", back_populates="citations")


class ValidationReport(Base):
    """
    Stores formatted validation reports for display and sharing.
    This complements IdeaValidation with presentation-ready data.
    """
    __tablename__ = "validation_reports"

    id = Column(String, primary_key=True, index=True)  # UUID for report sharing
    idea_validation_id = Column(Integer, ForeignKey("idea_validations.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Report metadata
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    
    # User input data
    user_answers = Column(JSON, nullable=True)  # Questions and answers from validation form
    
    # Formatted report data (ready for frontend display)
    report_data = Column(JSON, nullable=False)  # Complete formatted report
    
    # Report settings
    is_public = Column(Boolean, default=False)  # Public sharing enabled
    share_token = Column(String, nullable=True, index=True)  # For public sharing
    
    # Agent activity logs
    agent_activities = Column(JSON, nullable=True)  # Agent activity timeline
    processing_metadata = Column(JSON, nullable=True)  # Processing stats, timing, etc.
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    idea_validation = relationship("IdeaValidation")
    user = relationship("User", back_populates="validation_reports")

    def __repr__(self):
        return f"<ValidationReport(id='{self.id}', title='{self.title}')>"