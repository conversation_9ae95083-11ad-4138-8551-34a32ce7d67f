from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, JSON
from sqlalchemy.sql import func
import enum
from app.core.database import Base


class AIProviderSettings(Base):
    __tablename__ = "ai_provider_settings"

    id = Column(Integer, primary_key=True, index=True)
    
    # Provider info
    provider_name = Column(String, nullable=False, unique=True)  # openai, anthropic, deepseek, gemini
    model_name = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    
    # Configuration
    api_key_env_var = Column(String, nullable=False)  # Environment variable name
    base_url = Column(String, nullable=True)  # For custom endpoints
    max_tokens = Column(Integer, default=4000)
    temperature = Column(String, default="0.7")  # String to support decimal values
    
    # Rate limiting
    requests_per_minute = Column(Integer, default=60)
    tokens_per_minute = Column(Integer, default=60000)
    
    # Cost tracking
    cost_per_1k_input_tokens = Column(String, nullable=True)  # Decimal as string
    cost_per_1k_output_tokens = Column(String, nullable=True)
    
    # Capabilities
    supports_streaming = Column(Boolean, default=True)
    supports_function_calling = Column(Boolean, default=False)
    max_context_length = Column(Integer, default=4000)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<AIProviderSettings(id={self.id}, provider='{self.provider_name}', model='{self.model_name}')>"


class SystemSettings(Base):
    __tablename__ = "system_settings"

    id = Column(Integer, primary_key=True, index=True)
    
    # Setting identification
    key = Column(String, nullable=False, unique=True)
    value = Column(Text, nullable=True)
    value_type = Column(String, default="string")  # string, integer, boolean, json
    
    # Metadata
    description = Column(Text, nullable=True)
    category = Column(String, default="general")
    is_public = Column(Boolean, default=False)  # Can be exposed to frontend
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<SystemSettings(id={self.id}, key='{self.key}', category='{self.category}')>"
    
    def get_typed_value(self):
        """Return the value converted to its proper type"""
        if self.value is None:
            return None
            
        if self.value_type == "integer":
            return int(self.value)
        elif self.value_type == "boolean":
            return self.value.lower() in ("true", "1", "yes", "on")
        elif self.value_type == "json":
            import json
            return json.loads(self.value)
        else:
            return self.value