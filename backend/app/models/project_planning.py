from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from app.core.database import Base


class PlanningStatus(enum.Enum):
    DRAFT = "DRAFT"
    COMPLETED = "COMPLETED"


class DevelopmentPhase(enum.Enum):
    INTERACTIVE = "INTERACTIVE"
    POC = "POC"
    MVP = "MVP"
    PRODUCTION = "PRODUCTION"


class ProjectPlanning(Base):
    __tablename__ = "project_plannings"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    idea_validation_id = Column(Integer, ForeignKey("idea_validations.id"), nullable=True)
    
    # Basic info
    project_name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # Planning data (7 tabs from original)
    platforms = Column(JSON, nullable=True)  # Web, Mobile, Desktop
    technology_stack = Column(JSON, nullable=True)
    development_phases = Column(JSON, nullable=True)
    timeline = Column(JSON, nullable=True)
    budget_range = Column(JSON, nullable=True)
    branding = Column(JSON, nullable=True)
    user_projections = Column(JSON, nullable=True)
    
    # Status
    status = Column(SQLEnum(PlanningStatus), default=PlanningStatus.DRAFT)
    completion_percentage = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="project_plannings")
    idea_validation = relationship("IdeaValidation")
    roadmaps = relationship("Roadmap", back_populates="project_planning")

    def __repr__(self):
        return f"<ProjectPlanning(id={self.id}, name='{self.project_name}', status='{self.status.value}')>"