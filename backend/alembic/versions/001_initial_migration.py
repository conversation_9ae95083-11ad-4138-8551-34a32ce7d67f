"""Initial migration

Revision ID: 001
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # SQLAlchemy will automatically create the enum types when creating tables

    # Create users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('email', sa.String(), nullable=False),
        sa.Column('username', sa.String(), nullable=True),
        sa.Column('full_name', sa.String(), nullable=True),
        sa.Column('hashed_password', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('role', postgresql.ENUM('user', 'admin', name='userrole'), nullable=False),
        sa.Column('is_verified', sa.<PERSON>(), nullable=True),
        sa.Column('google_id', sa.String(), nullable=True),
        sa.Column('avatar_url', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_google_id'), 'users', ['google_id'], unique=True)

    # Create pricing_plans table
    op.create_table('pricing_plans',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('plan_type', postgresql.ENUM('free', 'pro', 'enterprise', name='plantype'), nullable=False),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('price', sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column('currency', sa.String(), nullable=True),
        sa.Column('billing_period', sa.String(), nullable=True),
        sa.Column('max_validations', sa.Integer(), nullable=True),
        sa.Column('max_roadmaps', sa.Integer(), nullable=True),
        sa.Column('enhanced_validation', sa.Boolean(), nullable=True),
        sa.Column('ai_providers', sa.String(), nullable=True),
        sa.Column('stripe_price_id', sa.String(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('name'),
        sa.UniqueConstraint('stripe_price_id')
    )

    # Create user_subscriptions table
    op.create_table('user_subscriptions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('plan_id', sa.Integer(), nullable=False),
        sa.Column('status', postgresql.ENUM('active', 'inactive', 'cancelled', 'past_due', name='subscriptionstatus'), nullable=True),
        sa.Column('validations_used', sa.Integer(), nullable=True),
        sa.Column('roadmaps_used', sa.Integer(), nullable=True),
        sa.Column('current_period_start', sa.DateTime(timezone=True), nullable=True),
        sa.Column('current_period_end', sa.DateTime(timezone=True), nullable=True),
        sa.Column('stripe_subscription_id', sa.String(), nullable=True),
        sa.Column('stripe_customer_id', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('cancelled_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['plan_id'], ['pricing_plans.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id'),
        sa.UniqueConstraint('stripe_subscription_id')
    )

    # Create idea_validations table
    op.create_table('idea_validations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=False),
        sa.Column('validation_type', postgresql.ENUM('standard', 'enhanced', name='validationtype'), nullable=True),
        sa.Column('project_concept', sa.JSON(), nullable=True),
        sa.Column('user_roles', sa.JSON(), nullable=True),
        sa.Column('features', sa.JSON(), nullable=True),
        sa.Column('target_users', sa.JSON(), nullable=True),
        sa.Column('market_analysis', sa.JSON(), nullable=True),
        sa.Column('status', postgresql.ENUM('pending', 'processing', 'completed', 'failed', name='validationstatus'), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('ai_provider', sa.String(), nullable=True),
        sa.Column('processing_time', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_idea_validations_id'), 'idea_validations', ['id'], unique=False)

    # Create project_plannings table
    op.create_table('project_plannings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('idea_validation_id', sa.Integer(), nullable=True),
        sa.Column('project_name', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('platforms', sa.JSON(), nullable=True),
        sa.Column('technology_stack', sa.JSON(), nullable=True),
        sa.Column('development_phases', sa.JSON(), nullable=True),
        sa.Column('timeline', sa.JSON(), nullable=True),
        sa.Column('budget_range', sa.JSON(), nullable=True),
        sa.Column('branding', sa.JSON(), nullable=True),
        sa.Column('user_projections', sa.JSON(), nullable=True),
        sa.Column('status', postgresql.ENUM('draft', 'completed', name='planningstatus'), nullable=True),
        sa.Column('completion_percentage', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['idea_validation_id'], ['idea_validations.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create roadmaps table
    op.create_table('roadmaps',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('project_planning_id', sa.Integer(), nullable=True),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('phases', sa.JSON(), nullable=True),
        sa.Column('team_composition', sa.JSON(), nullable=True),
        sa.Column('cost_breakdown', sa.JSON(), nullable=True),
        sa.Column('timeline_estimates', sa.JSON(), nullable=True),
        sa.Column('risk_assessments', sa.JSON(), nullable=True),
        sa.Column('milestones', sa.JSON(), nullable=True),
        sa.Column('total_estimated_cost', sa.Numeric(precision=12, scale=2), nullable=True),
        sa.Column('hourly_rates', sa.JSON(), nullable=True),
        sa.Column('status', postgresql.ENUM('generating', 'completed', 'failed', name='roadmapstatus'), nullable=True),
        sa.Column('ai_provider', sa.String(), nullable=True),
        sa.Column('generation_prompt', sa.Text(), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['project_planning_id'], ['project_plannings.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create competitor_analyses table
    op.create_table('competitor_analyses',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('idea_validation_id', sa.Integer(), nullable=False),
        sa.Column('company_name', sa.String(), nullable=False),
        sa.Column('website_url', sa.String(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('features', sa.JSON(), nullable=True),
        sa.Column('pricing', sa.JSON(), nullable=True),
        sa.Column('strengths', sa.JSON(), nullable=True),
        sa.Column('weaknesses', sa.JSON(), nullable=True),
        sa.Column('scraped_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('scraping_success', sa.Boolean(), nullable=True),
        sa.Column('scraping_error', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['idea_validation_id'], ['idea_validations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create market_research table
    op.create_table('market_research',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('idea_validation_id', sa.Integer(), nullable=False),
        sa.Column('query', sa.String(), nullable=False),
        sa.Column('results', sa.JSON(), nullable=True),
        sa.Column('summary', sa.Text(), nullable=True),
        sa.Column('key_insights', sa.JSON(), nullable=True),
        sa.Column('search_provider', sa.String(), nullable=True),
        sa.Column('searched_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('results_count', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['idea_validation_id'], ['idea_validations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create verified_citations table
    op.create_table('verified_citations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('competitor_analysis_id', sa.Integer(), nullable=True),
        sa.Column('market_research_id', sa.Integer(), nullable=True),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('url', sa.String(), nullable=False),
        sa.Column('snippet', sa.Text(), nullable=True),
        sa.Column('source_domain', sa.String(), nullable=True),
        sa.Column('relevance_score', sa.Integer(), nullable=True),
        sa.Column('is_verified', sa.Boolean(), nullable=True),
        sa.Column('verification_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['competitor_analysis_id'], ['competitor_analyses.id'], ),
        sa.ForeignKeyConstraint(['market_research_id'], ['market_research.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

    # Create ai_provider_settings table
    op.create_table('ai_provider_settings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('provider_name', sa.String(), nullable=False),
        sa.Column('model_name', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.Column('api_key_env_var', sa.String(), nullable=False),
        sa.Column('base_url', sa.String(), nullable=True),
        sa.Column('max_tokens', sa.Integer(), nullable=True),
        sa.Column('temperature', sa.String(), nullable=True),
        sa.Column('requests_per_minute', sa.Integer(), nullable=True),
        sa.Column('tokens_per_minute', sa.Integer(), nullable=True),
        sa.Column('cost_per_1k_input_tokens', sa.String(), nullable=True),
        sa.Column('cost_per_1k_output_tokens', sa.String(), nullable=True),
        sa.Column('supports_streaming', sa.Boolean(), nullable=True),
        sa.Column('supports_function_calling', sa.Boolean(), nullable=True),
        sa.Column('max_context_length', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('provider_name')
    )

    # Create system_settings table
    op.create_table('system_settings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('key', sa.String(), nullable=False),
        sa.Column('value', sa.Text(), nullable=True),
        sa.Column('value_type', sa.String(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category', sa.String(), nullable=True),
        sa.Column('is_public', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('key')
    )


def downgrade() -> None:
    # Drop tables in reverse order
    op.drop_table('system_settings')
    op.drop_table('ai_provider_settings')
    op.drop_table('verified_citations')
    op.drop_table('market_research')
    op.drop_table('competitor_analyses')
    op.drop_table('roadmaps')
    op.drop_table('project_plannings')
    op.drop_table('idea_validations')
    op.drop_table('user_subscriptions')
    op.drop_table('pricing_plans')
    op.drop_table('users')

    # Drop enum types
    op.execute("DROP TYPE IF EXISTS plantype")
    op.execute("DROP TYPE IF EXISTS subscriptionstatus")
    op.execute("DROP TYPE IF EXISTS roadmapstatus")
    op.execute("DROP TYPE IF EXISTS developmentphase")
    op.execute("DROP TYPE IF EXISTS planningstatus")
    op.execute("DROP TYPE IF EXISTS validationtype")
    op.execute("DROP TYPE IF EXISTS validationstatus")
    op.execute("DROP TYPE IF EXISTS userrole")