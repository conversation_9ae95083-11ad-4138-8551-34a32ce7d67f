"""make_idea_validation_id_nullable

Revision ID: c9d8d39d9c91
Revises: 002_add_validation_reports
Create Date: 2025-07-02 22:58:20.528640

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c9d8d39d9c91'
down_revision = '002_add_validation_reports'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Make idea_validation_id nullable in validation_reports table
    op.alter_column('validation_reports', 'idea_validation_id', nullable=True)
    
    op.create_index(op.f('ix_ai_provider_settings_id'), 'ai_provider_settings', ['id'], unique=False)
    op.create_index(op.f('ix_competitor_analyses_id'), 'competitor_analyses', ['id'], unique=False)
    op.add_column('idea_validations', sa.Column('market_size_analysis', sa.<PERSON>(), nullable=True))
    op.add_column('idea_validations', sa.Column('target_market', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('unique_value_proposition', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('monetization_strategies', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('market_challenges', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('market_opportunities', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('competitors', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('citations', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('validation_summary', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('executive_summary', sa.Text(), nullable=True))
    op.add_column('idea_validations', sa.Column('next_steps', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('success_metrics', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('ai_providers_used', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('external_tools_used', sa.JSON(), nullable=True))
    op.add_column('idea_validations', sa.Column('agents_consulted', sa.Integer(), nullable=True))
    op.add_column('idea_validations', sa.Column('validation_depth', sa.String(), nullable=True))
    op.drop_column('idea_validations', 'user_roles')
    op.drop_column('idea_validations', 'ai_provider')
    op.create_index(op.f('ix_market_research_id'), 'market_research', ['id'], unique=False)
    op.create_index(op.f('ix_pricing_plans_id'), 'pricing_plans', ['id'], unique=False)
    op.create_index(op.f('ix_project_plannings_id'), 'project_plannings', ['id'], unique=False)
    op.create_index(op.f('ix_roadmaps_id'), 'roadmaps', ['id'], unique=False)
    op.create_index(op.f('ix_system_settings_id'), 'system_settings', ['id'], unique=False)
    op.create_index(op.f('ix_user_subscriptions_id'), 'user_subscriptions', ['id'], unique=False)
    op.drop_index(op.f('ix_users_google_id'), table_name='users')
    op.create_unique_constraint(None, 'users', ['google_id'])
    op.create_index(op.f('ix_verified_citations_id'), 'verified_citations', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_verified_citations_id'), table_name='verified_citations')
    op.drop_constraint(None, 'users', type_='unique')
    op.create_index(op.f('ix_users_google_id'), 'users', ['google_id'], unique=True)
    op.drop_index(op.f('ix_user_subscriptions_id'), table_name='user_subscriptions')
    op.drop_index(op.f('ix_system_settings_id'), table_name='system_settings')
    op.drop_index(op.f('ix_roadmaps_id'), table_name='roadmaps')
    op.drop_index(op.f('ix_project_plannings_id'), table_name='project_plannings')
    op.drop_index(op.f('ix_pricing_plans_id'), table_name='pricing_plans')
    op.drop_index(op.f('ix_market_research_id'), table_name='market_research')
    op.add_column('idea_validations', sa.Column('ai_provider', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('idea_validations', sa.Column('user_roles', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.drop_column('idea_validations', 'validation_depth')
    op.drop_column('idea_validations', 'agents_consulted')
    op.drop_column('idea_validations', 'external_tools_used')
    op.drop_column('idea_validations', 'ai_providers_used')
    op.drop_column('idea_validations', 'success_metrics')
    op.drop_column('idea_validations', 'next_steps')
    op.drop_column('idea_validations', 'executive_summary')
    op.drop_column('idea_validations', 'validation_summary')
    op.drop_column('idea_validations', 'citations')
    op.drop_column('idea_validations', 'competitors')
    op.drop_column('idea_validations', 'market_opportunities')
    op.drop_column('idea_validations', 'market_challenges')
    op.drop_column('idea_validations', 'monetization_strategies')
    op.drop_column('idea_validations', 'unique_value_proposition')
    op.drop_column('idea_validations', 'target_market')
    op.drop_column('idea_validations', 'market_size_analysis')
    op.drop_index(op.f('ix_competitor_analyses_id'), table_name='competitor_analyses')
    op.drop_index(op.f('ix_ai_provider_settings_id'), table_name='ai_provider_settings')
    # ### end Alembic commands ###