"""Add validation reports table

Revision ID: 002_add_validation_reports
Revises: b7aecb1d0682
Create Date: 2025-06-29 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '002_add_validation_reports'
down_revision = 'b7aecb1d0682'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('validation_reports',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('idea_validation_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('user_answers', sa.<PERSON><PERSON>(), nullable=True),
    sa.Column('report_data', sa.<PERSON>(), nullable=False),
    sa.Column('is_public', sa.<PERSON>(), nullable=True),
    sa.Column('share_token', sa.String(), nullable=True),
    sa.Column('agent_activities', sa.JSON(), nullable=True),
    sa.Column('processing_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['idea_validation_id'], ['idea_validations.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_validation_reports_id'), 'validation_reports', ['id'], unique=False)
    op.create_index(op.f('ix_validation_reports_share_token'), 'validation_reports', ['share_token'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_validation_reports_share_token'), table_name='validation_reports')
    op.drop_index(op.f('ix_validation_reports_id'), table_name='validation_reports')
    op.drop_table('validation_reports')
    # ### end Alembic commands ###