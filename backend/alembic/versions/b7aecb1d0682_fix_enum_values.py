"""Fix enum values

Revision ID: b7aecb1d0682
Revises: 001
Create Date: 2025-06-28 11:06:50.928507

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b7aecb1d0682'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Update enum values to match Python enum names - only for existing enum types
    op.execute("ALTER TYPE userrole RENAME VALUE 'user' TO 'USER'")
    op.execute("ALTER TYPE userrole RENAME VALUE 'admin' TO 'ADMIN'")
    
    op.execute("ALTER TYPE validationstatus RENAME VALUE 'pending' TO 'PENDING'")
    op.execute("ALTER TYPE validationstatus RENAME VALUE 'processing' TO 'PROCESSING'")  
    op.execute("ALTER TYPE validationstatus RENAME VALUE 'completed' TO 'COMPLETED'")
    op.execute("ALTER TYPE validationstatus RENAME VALUE 'failed' TO 'FAILED'")
    
    op.execute("ALTER TYPE validationtype RENAME VALUE 'standard' TO 'STANDARD'")
    op.execute("ALTER TYPE validationtype RENAME VALUE 'enhanced' TO 'ENHANCED'")
    
    op.execute("ALTER TYPE planningstatus RENAME VALUE 'draft' TO 'DRAFT'")
    op.execute("ALTER TYPE planningstatus RENAME VALUE 'completed' TO 'COMPLETED'")
    
    op.execute("ALTER TYPE roadmapstatus RENAME VALUE 'generating' TO 'GENERATING'")
    op.execute("ALTER TYPE roadmapstatus RENAME VALUE 'completed' TO 'COMPLETED'")
    op.execute("ALTER TYPE roadmapstatus RENAME VALUE 'failed' TO 'FAILED'")
    
    op.execute("ALTER TYPE subscriptionstatus RENAME VALUE 'active' TO 'ACTIVE'")
    op.execute("ALTER TYPE subscriptionstatus RENAME VALUE 'inactive' TO 'INACTIVE'")
    op.execute("ALTER TYPE subscriptionstatus RENAME VALUE 'cancelled' TO 'CANCELLED'")
    op.execute("ALTER TYPE subscriptionstatus RENAME VALUE 'past_due' TO 'PAST_DUE'")
    
    op.execute("ALTER TYPE plantype RENAME VALUE 'free' TO 'FREE'")
    op.execute("ALTER TYPE plantype RENAME VALUE 'pro' TO 'PRO'")
    op.execute("ALTER TYPE plantype RENAME VALUE 'enterprise' TO 'ENTERPRISE'")


def downgrade() -> None:
    pass