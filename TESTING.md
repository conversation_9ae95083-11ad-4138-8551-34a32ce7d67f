# 🧪 Testing Guide for Incepta Setup

This guide provides step-by-step instructions to test the Incepta backend setup.

## 📋 Pre-Testing Checklist

### ✅ Required Files Check
Verify these files exist:
- [ ] `backend/app/main.py`
- [ ] `backend/app/core/config.py`
- [ ] `backend/pyproject.toml`
- [ ] `backend/alembic.ini`
- [ ] `.env` file
- [ ] `docker-compose.dev.yml`

### ✅ Environment Setup
1. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your configuration (at minimum set SECRET_KEY)

## 🚀 Option 1: Local Development Testing (Recommended First)

### Step 1: Install Backend Dependencies
```bash
cd backend
poetry install
```

### Step 2: Test Configuration Loading
```bash
cd backend
poetry run python -c "from app.core.config import settings; print(f'✅ App: {settings.app_name}, DB: {settings.database_url}')"
```

### Step 3: Test FastAPI App Creation
```bash
cd backend
poetry run python -c "from app.main import app; print(f'✅ FastAPI app created: {app.title} v{app.version}')"
```

### Step 4: Start Development Server
```bash
cd backend
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Step 5: Test API Endpoints
Open another terminal and test:
```bash
# Basic health check
curl http://localhost:8000/

# API ping
curl http://localhost:8000/api/v1/ping

# API documentation (open in browser)
open http://localhost:8000/api/docs
```

### Step 6: Test Database (SQLite for local)
```bash
cd backend
# Run migrations
poetry run alembic upgrade head

# Check if tables were created
poetry run python -c "
from app.core.database import engine
from sqlalchemy import inspect
inspector = inspect(engine)
tables = inspector.get_table_names()
print(f'✅ Created {len(tables)} tables: {tables[:5]}...')
"
```

## 🐳 Option 2: Docker Testing

### Step 1: Start Database Services Only
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### Step 2: Check Service Status
```bash
docker-compose -f docker-compose.dev.yml ps
```

### Step 3: Test Database Connection
```bash
# Wait for postgres to be ready
docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres

# Test connection
docker-compose -f docker-compose.dev.yml exec postgres psql -U postgres -d incepta -c "SELECT version();"
```

### Step 4: Test Redis Connection
```bash
docker-compose -f docker-compose.dev.yml exec redis redis-cli ping
```

### Step 5: Test MinIO
```bash
# Check MinIO status
curl -f http://localhost:9000/minio/health/live

# Access MinIO console (open in browser)
open http://localhost:9001
# Login: minioadmin / minioadmin
```

### Step 6: Update Environment for Docker
```bash
# Update .env for Docker services
DATABASE_URL=postgresql://postgres:password@localhost:5432/incepta
REDIS_URL=redis://localhost:6379/0
MINIO_ENDPOINT=localhost:9000
```

### Step 7: Run Backend with Docker Services
```bash
cd backend
poetry run alembic upgrade head
poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🧪 API Testing

### Authentication Endpoints
```bash
# Register new user
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "confirm_password": "testpassword123",
    "full_name": "Test User"
  }'

# Login user
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>", 
    "password": "testpassword123"
  }'
```

### Protected Endpoints (use token from login)
```bash
# Get current user info
curl -X GET http://localhost:8000/api/v1/auth/me \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🐛 Common Issues & Solutions

### Issue: Import Errors
**Problem**: `ModuleNotFoundError: No module named 'fastapi'`
**Solution**: 
```bash
cd backend
poetry install
poetry shell  # Activate virtual environment
```

### Issue: Database Connection Error
**Problem**: `could not connect to server`
**Solutions**:
1. For local testing: Use SQLite in `.env`:
   ```
   DATABASE_URL=sqlite:///./test.db
   ```
2. For Docker: Ensure PostgreSQL is running:
   ```bash
   docker-compose -f docker-compose.dev.yml up postgres -d
   ```

### Issue: Redis Connection Error
**Problem**: `redis.exceptions.ConnectionError`
**Solutions**:
1. For local testing: Install and start Redis locally, or use fake Redis:
   ```
   REDIS_URL=redis://localhost:6379/0
   ```
2. For Docker: Ensure Redis is running:
   ```bash
   docker-compose -f docker-compose.dev.yml up redis -d
   ```

### Issue: Docker Build Fails
**Problem**: Various Docker build errors
**Solution**: Use the simplified development compose file:
```bash
docker-compose -f docker-compose.dev.yml up -d
```

## ✅ Success Criteria

Your setup is working correctly if:

- [ ] ✅ Backend server starts without errors
- [ ] ✅ API responds to `http://localhost:8000/`
- [ ] ✅ API docs load at `http://localhost:8000/api/docs`
- [ ] ✅ Database migrations run successfully
- [ ] ✅ User registration/login works
- [ ] ✅ Protected endpoints require authentication

## 🎯 Next Steps After Successful Testing

1. **Continue with Phase 2**: AI Integration and Core API Endpoints
2. **Add real API keys** to `.env` for AI providers
3. **Set up Celery worker** for background tasks
4. **Test idea validation workflow**

## 📞 Troubleshooting Help

If you encounter issues:

1. **Check logs**: Look at terminal output for specific error messages
2. **Verify environment**: Ensure `.env` file has correct values
3. **Check dependencies**: Run `poetry install` in backend directory
4. **Database issues**: Start with SQLite for simplicity
5. **Port conflicts**: Ensure ports 8000, 5432, 6379, 9000, 9001 are available

Run the setup verification script:
```bash
python3 test_setup.py
```